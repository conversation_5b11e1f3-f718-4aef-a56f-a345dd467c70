import streamlit as st
import sys
import numpy as np
import pandas as pd

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

def apply_value_based_color_mapping(styler, column_name):
    """
    根据数值正负性和绝对值大小应用颜色映射
    负值使用冷色系(蓝色系)，正值使用暖色系(红色系)
    """
    def color_mapping(val):
        """为单个值应用颜色"""
        # 转换为数值
        try:
            numeric_val = pd.to_numeric(val, errors='coerce')
        except:
            return ''
        
        if pd.isna(numeric_val):
            return ''
        
        # 使用更严格的零值检测，考虑浮点数精度问题
        if abs(numeric_val) < 1e-10:  # 非常接近零的值都视为零
            # 零值使用中性灰色
            return 'background-color: rgba(200, 200, 200, 0.3); color: black;'
        elif numeric_val > 0:
            # 正值使用暖色系(红色系) - 促进/生成
            return 'background-color: rgba(255, 150, 150, 0.7); color: black;'
        else:  # numeric_val < 0
            # 负值使用冷色系(蓝色系) - 抑制/消耗
            return 'background-color: rgba(150, 150, 255, 0.7); color: black;'
    
    try:
        # 使用applymap对指定列应用颜色
        return styler.applymap(color_mapping, subset=[column_name])
    except Exception as e:
        # 如果失败，返回原始styler
        st.warning(f"颜色映射应用失败: {e}")
        return styler

def create_color_coded_dataframe(df, color_column=None):
    """
    创建带颜色编码的DataFrame显示
    """
    if df.empty:
        return df
        
    try:
        styled = df.style
        
        # 应用颜色映射
        if color_column and color_column in df.columns:
            styled = apply_value_based_color_mapping(styled, color_column)
        
        # 格式化数值
        format_dict = {}
        if 'Relative values' in df.columns:
            format_dict['Relative values'] = '{:.2%}'
        if 'Absolute values' in df.columns:
            format_dict['Absolute values'] = '{:.2e}'
        
        if format_dict:
            styled = styled.format(format_dict)
        
        # 应用表格样式
        styled = styled.set_table_styles([
            {'selector': 'th', 'props': [
                ('font-weight', 'bold'), 
                ('text-align', 'center'), 
                ('background-color', '#eaf3fa'),
                ('color', 'black')
            ]},
            {'selector': 'td', 'props': [
                ('text-align', 'center'),
                ('padding', '8px')
            ]},
            {'selector': 'table', 'props': [
                ('border-collapse', 'collapse'),
                ('border', '1px solid #ddd')
            ]}
        ])
        
        return styled
        
    except Exception as e:
        st.warning(f"样式应用失败: {e}")
        return df

# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="单点ROP分析", page_icon="📊")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .error-card {
        background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--warning-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 标签页样式优化 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: var(--surface-color);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 4px var(--shadow-light);
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
    }
    
    /* 输入控件样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 选择框内容区域 */
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 选择框文本 */
    .stSelectbox > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 选择框选项样式 */
    .stSelectbox option {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 下拉选项 */
    .stSelectbox [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 下拉选项悬停效果 */
    .stSelectbox [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 下拉菜单容器 */
    .stSelectbox [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 选择框显示文本 */
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保下拉箭头可见 */
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 数据表格样式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        border-radius: 4px;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">📊 单点ROP分析</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        反应路径分析 - 研究反应对组分生成的贡献和组分间的元素流向
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if ('a' and 'b') not in st.session_state:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行ROP分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

elif st.session_state['b'].net_reaction_rate_exist is False:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少反应速率数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有反应速率输出，无法进行ROP分析。请检查CHEMKIN输出设置。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ 分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        ROP_type = st.radio(
            '**分析类型**', 
            ['积分ROP', 'EndPointROP'], 
            captions=[
                '将ROP沿时间或距离积分，适合流动管或火焰',
                '取稳态最后一点ROP，适合JSR'
            ],
            help="选择合适的ROP分析类型"
        )
    
    with col2:
        threshold = st.number_input(
            '**相对值显示阈值**', 
            min_value=0.00, 
            max_value=1.00, 
            value=0.10,
            format="%.3f",
            help="设置显示阈值，过滤较小的贡献值"
        )
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 分析标签页
# ========================================

tab1, tab2 = st.tabs(['🔬 反应ROP', '🧪 组分ROP'])

# ========================================
# 反应ROP分析
# ========================================

with tab1:
    st.markdown("### 🔬 反应对组分生成的贡献分析")
    
    with st.expander("📋 分析参数", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            selected_variable_reaction = st.selectbox(
                "请选择工况：", 
                options=st.session_state['b'].variables,
                key='V1',
                help="选择要分析的工况点"
            )
        
        with col2:
            selected_species_reaction = st.selectbox(
                "请选择组分：", 
                options=st.session_state['a'].species,
                key='s1',
                help="选择要分析的目标组分"
            )
    
    # 自动分析
    with st.spinner('正在进行反应ROP分析...'):
        if ROP_type == '积分ROP':
            fig_single_species1 = st.session_state['a'].plot_ROP_single(
                st.session_state['b'],
                selected_species_reaction, 
                selected_variable_reaction, 
                threshold,
                'integral'
            )
        else:
            fig_single_species1 = st.session_state['a'].plot_ROP_single(
                st.session_state['b'],
                selected_species_reaction, 
                selected_variable_reaction, 
                threshold,
                'end'
            )
        
        # 显示图表
        st.markdown("#### 📊 ROP分析图表")
        st.plotly_chart(fig_single_species1, use_container_width=True)
        
        # 显示数据表格
        st.markdown("#### 📊 详细数据表")
        with st.container():
            df = st.session_state['a'].ROP_percent.T.copy()
            
            # 添加颜色说明
            st.markdown("""
            <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(240, 248, 255, 0.5); border-radius: 5px;">
                <small><strong>颜色说明：</strong> 
                <span style="background-color: rgba(255, 150, 150, 0.7); padding: 2px 6px; border-radius: 3px;">红色 = 促进/生成</span>
                <span style="background-color: rgba(150, 150, 255, 0.7); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">蓝色 = 抑制/消耗</span>
                <span style="background-color: rgba(200, 200, 200, 0.3); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">灰色 = 零值</span>
                </small>
            </div>
            """, unsafe_allow_html=True)
            
            try:
                # 使用新的颜色编码函数
                styled_df = create_color_coded_dataframe(df, 'Relative values')
                st.dataframe(styled_df, use_container_width=True)
                    
            except Exception as e:
                st.error(f"表格处理失败: {str(e)}")
                st.info("显示原始数据表:")
                st.dataframe(df, use_container_width=True)

# ========================================
# 组分ROP分析
# ========================================

with tab2:
    st.markdown("### 🧪 组分间元素流向分析")
    
    with st.expander("📋 分析参数", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            selected_variable_species = st.selectbox(
                "请选择工况：", 
                options=st.session_state['b'].variables, 
                key='V2',
                help="选择要分析的工况点"
            )
        
        with col2:
            selected_element = st.selectbox(
                "请选择元素：", 
                options=st.session_state['a'].species_element.columns.tolist(),
                help="选择要追踪的元素"
            )
        
        with col3:
            element_index = st.session_state['a'].species_element.index[
                st.session_state['a'].species_element.loc[:, selected_element] != 0
            ].tolist()
            
            selected_species_species = st.selectbox(
                "请选择组分：", 
                options=element_index, 
                key='S2',
                help="选择包含该元素的组分"
            )
    
    # 自动分析
    with st.spinner('正在进行组分ROP分析...'):
        if ROP_type == '积分ROP':
            fig_single_species2 = st.session_state['a'].plot_ROP_single_species(
                st.session_state['b'],
                selected_species_species, 
                selected_variable_species,
                selected_element, 
                threshold, 
                'integral'
            )
        else:
            fig_single_species2 = st.session_state['a'].plot_ROP_single_species(
                st.session_state['b'],
                selected_species_species, 
                selected_variable_species,
                selected_element, 
                threshold, 
                'end'
            )
        
        # 显示图表
        st.markdown("#### 📊 元素流向图表")
        st.plotly_chart(fig_single_species2, use_container_width=True)
        
        # 显示数据表格
        st.markdown("#### 📊 详细数据表")
        with st.container():
            if hasattr(st.session_state['a'], 'ROP_percent_flux') and st.session_state['a'].ROP_percent_flux is not None:
                df2 = st.session_state['a'].ROP_percent_flux.T.copy()
                
                # 添加颜色说明
                st.markdown("""
                <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(240, 248, 255, 0.5); border-radius: 5px;">
                    <small><strong>颜色说明：</strong> 
                    <span style="background-color: rgba(255, 150, 150, 0.7); padding: 2px 6px; border-radius: 3px;">红色 = 流入目标组分</span>
                    <span style="background-color: rgba(150, 150, 255, 0.7); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">蓝色 = 流出目标组分</span>
                    <span style="background-color: rgba(200, 200, 200, 0.3); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">灰色 = 零值</span>
                    </small>
                </div>
                """, unsafe_allow_html=True)
                
                try:
                    # 使用新的颜色编码函数
                    styled_df2 = create_color_coded_dataframe(df2, 'Relative values')
                    st.dataframe(styled_df2, use_container_width=True)
                    
                except Exception as e:
                    st.error(f"表格处理失败: {str(e)}")
                    st.info("显示原始数据表:")
                    st.dataframe(df2, use_container_width=True)
            else:
                st.warning("ROP数据不可用，请检查分析过程是否正确完成")

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>📊 单点ROP分析 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)