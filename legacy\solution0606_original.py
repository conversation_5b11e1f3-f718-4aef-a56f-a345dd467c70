import copy
import datetime
import math
import os
import re
import streamlit as st
import plotly
import plotly.graph_objs as go
from plotly.subplots import make_subplots
import cantera as ct
import numpy as np
import pandas as pd
from scipy.optimize import curve_fit
from lmfit import Model
from tqdm import tqdm
# import graphviz
import warnings
#import networkx

warnings.filterwarnings("ignore")
ct.suppress_thermo_warnings()

class solution():
    def __init__(self, gas_out='gas.out', test=False):
        self.elements = None
        self.species = None
        self.reactions = None
        self.elements = None
        self.reactants = None
        self.products = None
        self.stoichimetric = None
        self.load_file = False
        if not test:
            self.gas_out = str(gas_out.read(), 'utf-8')
        else:
            with open(gas_out, 'r') as f:
                self.gas_out = f.read()

    def process_gas_out(self, progress_bar=False):
        print('正在处理gas.out文件信息')
        gas_out_lines = self.gas_out.splitlines()
        species_flag = False
        species_info = []
        for line in gas_out_lines:
            if species_flag == True and '(k = A T**b exp(-E/RT))' in line:
                species_flag = False
            if line.startswith(' CONSIDERED '):
                species_flag = True
            if species_flag == True:
                species_info.append(line)
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在处理反信息')
            progress_bar1.empty()
            progress_bar2 = st.progress(0, text='正在处理反应系数矩阵')
            progress_bar2.empty()
        reaction_flag = False
        reaction_info = []
        for i, line in enumerate(gas_out_lines):
            if reaction_flag == True and ' UNITS for the preceding reactions' in line:
                reaction_flag = False
            if reaction_flag == True:
                reaction_info.append(line)
            if line.startswith('      REACTIONS CONSIDERED'):
                reaction_flag = True
        element_list = species_info[0].split()[6:]
        self.species_element = pd.DataFrame(columns=element_list)
        self.species_MW = pd.DataFrame(columns=['MW'])
        for line in species_info[2:-4]:
            species = line.split()[1]
            self.species_element.loc[species, :] = line.split()[7:]
            self.species_MW.loc[species, :] = line.split()[4]
        self.species_element = self.species_element.astype(int)
        self.species_MW = self.species_MW.astype(float).apply(lambda x: round(x)).T

        print('处理完毕！')
        self.reaction_index = pd.DataFrame(columns=['reaction_equation'])
        stoichi_raw = pd.DataFrame(
            columns=self.species_element.index)  # 建立计量比矩阵，横坐标为组分，纵坐标为元素                                       #
        print('正在处理反应信息')
        Arr_param = dict()
        self.Arr_all = []
        self.cantera_reactions = []

        def replace_sci_num(string):
            if string.startswith('-'):
                string = '-' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-',
                                                                                                             'E-')
            elif string.startswith('+'):
                string = '+' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-',
                                                                                                             'E-')
            else:
                string = string.replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
            return string

        for i, line in enumerate(tqdm(reaction_info)):
            if line.split('.')[0].strip().isdigit():  # 判断起始是否是数字，如果是数字，则为反应式所在行
                no_index_line = line.replace(line.split('.')[0] + '.', '')  # 1000个反应后没有空格了，因此用.号分割出反推，将序号替换掉，避免反应物中有。
                reaction = no_index_line.split()[0]  # 根据空格拆分所在行
                if '=' not in reaction:  # 有时候由于反应式过长，反应物到了下一行，需要再合并两行的数据
                    reaction = reaction + reaction_info[i + 1].strip()
                if '<=>' in reaction:
                    self.cantera_reactions.append(
                        reaction.replace('+', ' + ').replace('<=>', ' <=> ').replace('( + M)', ' (+M)'))
                else:
                    self.cantera_reactions.append(
                        reaction.replace('+', ' + ').replace('=', ' <=> ').replace('<=> >', '<=> ').replace('( + M)',
                                                                                                            ' (+M)'))
                self.reaction_index.loc[
                    int(line.split('.')[0].strip()), 'reaction_equation'] = reaction  # 每行的第0个单元为反应式的序号
                Arr_param['A'] = float(replace_sci_num(no_index_line.split()[1]))
                Arr_param['b'] = float(replace_sci_num(no_index_line.split()[2]))
                Arr_param['Ea'] = float(replace_sci_num(no_index_line.split()[3]))
                self.Arr_all.append(copy.deepcopy(Arr_param))

        stoichi_raw = pd.DataFrame(columns=self.species_element.index,
                                   index=[f'{reaction}#{i + 1}' for i, reaction in
                                          enumerate(self.reaction_index.loc[:, 'reaction_equation'])],
                                   data=0)  # 建立计量比矩阵，正坐标为组分，纵坐标为反应

        print('处理完毕！')

        print('正在处理化学计量数矩阵')
        reacts_prods = pd.DataFrame(index=stoichi_raw.index, columns=['reactants', 'products'])
        last_reaction = 0
        self.Arr_SI_unit = []

        self.reactants = dict()
        self.products = dict()
        if progress_bar:
            progress_bar1.progress((i + 1) / len(reaction_info), text='正在处理反信息')

        for i, reaction in enumerate(tqdm(stoichi_raw.index)):
            if progress_bar:
                progress_bar2.progress((i + 1) / len(stoichi_raw.index), text='正在处理反应系数矩阵')
            # 对反应列表中的反应
            if reaction == last_reaction:
                continue
            last_reaction = reaction
            if '<' in reaction:
                reactant_orig = '#'.join(reaction.split('#')[:-1]).split('<=')[
                    0]  # 组分中有#号，则倒数先用#获取反应方程'C3H3+C2H2<=>C#CC*CCJ#222'在用#将其连接起来
            else:
                reactant_orig = '#'.join(reaction.split('#')[:-1]).split('=')[0]
            reactant = re.sub('\(\+.*\)', '', reactant_orig)  # 首先除去带括号的，避免出现问题三体反应，
            species_r = reactant.split('+')  # 其次将其根据=两边分为反应物和生成物
            # species_r=re.findall(r'\d*\.?\w+\(*\w*\)*\.?-*\w*\(*\w*\)*\*?',reactant)                                     #利用正则找到反应物和生成物中的组分和计量比：（\d*\.）处理总包反应出现的小数计量比 \w+\(*\w*\)*处理包含括号的内容 (-*)处理扩折号
            reacts_prods.loc[:, 'reactants'][i] = species_r  # 保存一下每隔反应的反应物信息，后边可能有用

            sp_r = []
            for j, sp in enumerate(species_r):  # 对每个反应物中的组分
                if sp != 'm' and sp != 'M':  # 若这个组分不是M
                    if sp[0].isdigit() or sp[0] == '.':  # 判断首位是否是数字或符号.
                        sto = re.search(r'\d*\.*\d*', sp)[0]  # 如果是的话正则搜索一下这个数字，search只返回第一个
                        sp = re.sub(r'\A\d*\.*\d*', '', sp)
                        stoichi_raw.loc[reaction, sp] -= float(sto)  # 去掉组分中的计量比，将其系数存于stoichi矩阵中
                    else:
                        sto = 1  # 如果首位不是字母或.，则计量比是1
                        stoichi_raw.loc[reaction, sp] -= float(sto)  # 同样保存其计量比
                    sp_r.append(sp)
                    self.reactants[reaction] = sp_r
            if '>' in reaction:
                product_orig = '#'.join(reaction.split('#')[:-1]).split('=>')[
                    1]  # 组分中有#号，则从右边先用#获取反应方程',C3H3+C2H2<=>C#CC*CCJ#222'在用#将其连接起来
            else:
                product_orig = '#'.join(reaction.split('#')[:-1]).split('=')[1]
            product = re.sub('\(\+.*\)', '', product_orig)  # 去掉所有括号里的内容
            species_p = product.split('+')
            # species_p=re.findall(r'\d*\.?\w+\(*\w*\)*\.?\.?-*\w*\(*\w*\)*\*?',product)                   #同样过程对反应物
            reacts_prods.loc[:, 'products'][i] = species_p

            sp_p = []
            for j, sp in enumerate(species_p):
                if sp != 'm' and sp != 'M' and sp != 'HV' and sp != 'H*':  # 防止存在HV等激发态光子，其不是组分
                    if sp[0].isdigit() or sp[0] == '.':
                        sto = re.search(r'\d*\.*\d*', sp)[0]
                        sp = re.sub(r'\A\d*\.*\d*', '', sp)
                        stoichi_raw.loc[reaction, sp] += float(sto)
                    else:
                        sto = 1
                        stoichi_raw.loc[reaction, sp] += float(sto)
                    sp_p.append(sp)
                    self.products[reaction] = sp_p
            Arr_SI = dict()  # 为cantera读取准备的国际单位公式参数
            Arr_SI['A'] = self.Arr_all[i]['A'] * (1000** (len(self.reactants[reaction]) - 1))
            Arr_SI['b'] = self.Arr_all[i]['b']
            Arr_SI['Ea'] = self.Arr_all[i]['Ea'] * 4184
            self.Arr_SI_unit.append(copy.deepcopy(Arr_SI))

        self.species = [str(species) for species in stoichi_raw.columns]
        self.reactions = [f'{reaction}' for reaction in stoichi_raw.index]
        self.elements = element_list
        # self.reactants = reacts_prods.iloc[:, 0]
        # self.products = reacts_prods.iloc[:, 1]
        self.stoichimetric = stoichi_raw
        self.stoichimetric.index = self.reactions
        print('处理完毕！')

    def plot_ROP_single(self, b, select_species, selected_temp, threshold=0.001, datatype='integral', plot=False):
        if datatype == 'integral':
            ROP = self.integral_ROP[selected_temp]
        elif datatype == 'end':
            ROP = self.end_ROP[selected_temp]
        self.select_rop = pd.DataFrame(columns=self.stoichimetric.index, index=['raw', 'abs'])
        self.select_rop.loc['raw', :] = ROP.loc[:, select_species]
        self.select_rop.loc['abs', :] = ROP.loc[:, select_species].abs()
        self.select_rop.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.select_rop.drop('abs', inplace=True)
        self.select_rop['Production'] = np.sum(self.select_rop[self.select_rop > 0], 1)
        self.select_rop['Consumption'] = np.sum(self.select_rop[self.select_rop <= 0], 1)
        self.ROP_percent = pd.DataFrame(index=self.select_rop.index, columns=self.select_rop.columns)
        ROP_nor_P = self.select_rop[self.select_rop > 0].multiply(1 / self.select_rop['Production'], axis="index")
        ROP_nor_N = self.select_rop[self.select_rop <= 0].multiply(-1 / self.select_rop['Consumption'], axis="index")
        self.ROP_percent[self.select_rop > 0] = ROP_nor_P[self.select_rop > 0]
        self.ROP_percent[self.select_rop < 0] = ROP_nor_N[self.select_rop < 0]
        self.ROP_percent.loc['abs', :] = self.ROP_percent.loc['raw', :].abs()
        self.ROP_percent.index = ['Relative values', 'abs']
        self.ROP_percent.loc['Absolute values', :] = self.select_rop.loc['raw', :]
        self.ROP_percent.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.ROP_percent.drop('abs', inplace=True)
        self.ROP_percent.drop('Production', axis=1, inplace=True)
        self.ROP_percent.drop('Consumption', axis=1, inplace=True)
        self.ROP_percent = self.ROP_percent.loc[:, self.ROP_percent.loc['Absolute values', :] != 0]
        self.ROP_percent.loc['Direction', :] = 'Produce'
        self.ROP_percent.loc['Direction', self.ROP_percent.loc['Relative values', :].astype(float) <= 0] = 'Consume'
        self.ROP_percent = self.ROP_percent.T[np.abs(self.ROP_percent.T['Relative values']) > threshold].T
        max_limit = len(self.ROP_percent.T)
        fig = make_subplots(rows=1, cols=2, horizontal_spacing=0.05)
        fig.add_trace(go.Bar(
            y=self.ROP_percent.columns,
            x=self.ROP_percent.loc['Relative values'] * 100,
            orientation='h',
        ), row=1, col=1)

        fig.add_trace(go.Bar(
            y=self.select_rop.columns[0:max_limit],
            x=self.select_rop.loc['raw'][0:max_limit],
            orientation='h',
        ), row=1, col=2)

        fig.update_layout(title={
            'text': f"Cumulative {select_species} formation at {b.variable_name} {selected_temp} {b.variable_unit}",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=400,
            font=dict(size=12,
                      color="RebeccaPurple"),
            showlegend=False,
            xaxis={'title': 'ROP relative percent (%)', 'rangemode': 'tozero',
                   'ticks': 'outside'},
            xaxis2={'title': 'ROP absolute values (mole cm3 s)', 'showexponent': 'all', 'exponentformat': 'e',
                    'rangemode': 'normal',
                    'ticks': 'outside'},
            yaxis1={'showgrid': True, },
            yaxis2={'side': 'right', 'showgrid': True, })
        if plot:
            fig.show(fig)

        return fig

    def get_coefficients(self, reaction, selected_element, selected_species):
        # 在给定reaction中选定的species中包含的element
        return self.species_element.loc[selected_species, selected_element] * self.stoichimetric.abs().loc[
            reaction, selected_species]

    def get_flux_factor(self, reaction, selected_element, reverse=False):
        """
        获得元素流向分析时组分的转化比例
        :param a: solution 实例
        :param reaction: a中的反应方程
        :return: 返回一个反应物和产物的dataframe
        """
        # 检查质量守恒

        if reverse == False:
            reactants = [species for species in self.reactants[reaction] if self.species_element.loc[
                species, selected_element] != 0]
            products = [species for species in self.products[reaction] if self.species_element.loc[
                species, selected_element] != 0]
        else:
            reactants = [species for species in self.products[reaction] if self.species_element.loc[
                species, selected_element] != 0]
            products = [species for species in self.reactants[reaction] if self.species_element.loc[
                species, selected_element] != 0]
        flux_factor = pd.DataFrame(index=reactants, columns=products).fillna(0)

        if len(reactants) == 1:
            reaction_type = '单分子反应'
            for product in products:
                flux_factor.loc[reactants[0], product] = self.get_coefficients(reaction, selected_element, product) / \
                                                         self.get_coefficients(reaction, selected_element, reactants[0])
        elif len(reactants) == 2 and len(products) == 1:
            flux_factor = flux_factor + 1
            reaction_type = '双分子重聚'
        elif len(reactants) == 2 and len(products) >= 2:
            if reactants[0]==reactants[1]:#单独处理一类特殊反应例如'CH2NH2+CH2NH2=CH3NH2+CH2NH'
                flux_factor.loc[:,:]=1
                flux_factor.index=[reactants[0],'dup'] #把重复行名称重命名
            elif len(products) == 2 and (products[0]==products[1]):
                flux_factor.loc[:,:]=1
                flux_factor.columns=[products[0],'dup']
            reactants_MWs = []
            products_MWs = []
            for reactant, product in zip(reactants, products):  # 获得所有组分的摩尔质量
                reactants_MWs.append(self.species_MW[reactant].tolist()[0])
                products_MWs.append(self.species_MW[product].tolist()[0])

            if reactants_MWs[0] - 1 in products_MWs:  # 如果反应物0质量-1存在于产物中 说明该物质是被提取的物质
                reactants_1_target = products[products_MWs.index(reactants_MWs[1] + 1)]  # 根据质量abstrator找到对应产物是什么
                flux_factor.loc[reactants[1], reactants_1_target] = 1  # 将反应物1到对应产物的factor设置为1
                products_MWs.remove(reactants_MWs[1] + 1)  # 提出反应物1对应的产物
                products.remove(reactants_1_target)
                if reactants_MWs[0] - np.sum(products_MWs) == 1:  # 如果剩余产物等于反应物1质量+1

                    for product in products:  # 对剩余产物的factor按比分配
                        flux_factor.loc[reactants[0], product] = self.get_coefficients(reaction, selected_element,
                                                                                       product) / \
                                                                 self.get_coefficients(reaction, selected_element,
                                                                                       reactants[0])
                    if selected_element == 'H':
                        flux_factor.loc[reactants[0], reactants_1_target] = 1 / self.get_coefficients(reaction,
                                                                                                      selected_element,
                                                                                                      reactants[
                                                                                                          0])  # 对reac0目标产物只占一个H原子
                    else:
                        flux_factor.loc[reactants[0], reactants_1_target] = 0
                reaction_type = '氢提取反应'
            elif reactants_MWs[0] + 1 in products_MWs:  # 如果反应物0质量+1存在于产物中 说明该物质是abstractor
                reactants_0_target = products[products_MWs.index(reactants_MWs[0] + 1)]  # 根据质量数查找反应物对应的产物
                flux_factor.loc[reactants[0], reactants_0_target] = 1
                products_MWs.remove(reactants_MWs[0] + 1)
                products.remove(reactants_0_target)
                if reactants_MWs[1] - np.sum(products_MWs) == 1:
                    for product in products:
                        flux_factor.loc[reactants[1], product] = self.get_coefficients(reaction, selected_element,
                                                                                       product) / \
                                                                 self.get_coefficients(reaction, selected_element,
                                                                                       reactants[1])
                    if selected_element == 'H':
                        flux_factor.loc[reactants[1], reactants_0_target] = 1 / self.get_coefficients(reaction,
                                                                                                      selected_element,
                                                                                                      reactants[1])
                    else:
                        flux_factor.loc[reactants[1], reactants_0_target] = 0
                reaction_type = '氢提取反应'
            else:  # 如果上述条件均不满，那应该是歧化反应

                if reactants_MWs[0] > reactants_MWs[1]:
                    heavy_r = reactants[0]
                    light_r = reactants[1]
                else:
                    heavy_r = reactants[1]
                    light_r = reactants[0]
                reactant0_element = self.species_element.loc[reactants[0], selected_element]
                reactant1_element = self.species_element.loc[reactants[1], selected_element]
                product0_element = self.species_element.loc[products[0], selected_element]
                if self.get_coefficients(reaction, selected_element, product[1]) > self.get_coefficients(reaction,
                                                                                                         selected_element,
                                                                                                         light_r):
                    # 如果light_r元素少于prod1，它的所有元素都去prod1, prod0没有。
                    flux_factor.loc[light_r, products[1]] = 1
                    flux_factor.loc[light_r, products[0]] = 0
                    # heavry_r去prod1的元素为 prod1的元素减去light_r给的元素，再按比例分配
                    flux_factor.loc[heavy_r, products[1]] = (self.get_coefficients(reaction, selected_element,
                                                                                   product[1]) -
                                                             self.get_coefficients(reaction, selected_element,
                                                                                   light_r)) / self.get_coefficients(
                        reaction, selected_element, heavy_r)
                    flux_factor.loc[heavy_r, products[0]] = self.get_coefficients(reaction, selected_element,
                                                                                  product[0]) / self.get_coefficients(
                        reaction, selected_element, heavy_r)

                else:
                    # 如果light_r的元素多余prod1的元素，首先根据prod1的元素比例分给prod1
                    flux_factor.loc[light_r, products[1]] = self.get_coefficients(reaction, selected_element,
                                                                                  product[1]) / \
                                                            self.get_coefficients(reaction, selected_element, light_r)
                    flux_factor.loc[light_r, products[0]] = 1 - flux_factor.loc[light_r, products[1]]
                    # 剩下的分别prod0;heavr_r不取prod1，因为prod1已经被Light_r给满了，所以全去prod0
                    flux_factor.loc[heavy_r, products[1]] = 0
                    flux_factor.loc[heavy_r, products[0]] = 1
            reaction_type = '歧化反应'
        elif len(reactants) == 3:  # 3分子反应先把反应物和产物中一样的组分剔除，在进行分配
            for reactant in reactants:
                if reactant in products:
                    reactants.remove(reactant)
                    products.remove(reactant)
                    flux_factor.loc[reactant, reactant] = 1
            for reactant in reactants:
                for product in products:
                    flux_factor.loc[reactant, product] = self.species_element.loc[product, selected_element] * \
                                                         self.stoichimetric.abs().loc[reaction, product] / \
                                                         (self.species_element.loc[reactant, selected_element] *
                                                          self.stoichimetric.abs().loc[reaction, reactant])
            reaction_type = '3原子反应'
        return flux_factor

    def plot_ROP_single_species(self, b, select_species, selected_temp, selected_element, threshold=0.01,
                                datatype='integral', plot=False):
        if datatype == 'integral':
            rop_species = self.integral_ROP[selected_temp][self.integral_ROP[selected_temp].loc[:, select_species] != 0]
        elif datatype == 'end':
            rop_species = self.end_ROP[selected_temp][self.end_ROP[selected_temp].loc[:, select_species] != 0]
        flux = pd.DataFrame(index=self.species, columns=self.species).fillna(0)
        source = {}
        source_sorted = {}  # 建立一个用来存放各反应比例数的字典，之后用来给source进行排序
        for i in range(len(rop_species.index)):
            species_in_reaction = rop_species.iloc[i, :][rop_species.iloc[i, :] != 0]
            for species in species_in_reaction.index:
                if rop_species.loc[:, species][i] * rop_species.loc[:, select_species][i] < 0 and \
                        self.species_element.loc[
                            species, selected_element] != 0:
                    flux.loc[select_species, species] += rop_species.loc[:, species][i]
                    if species not in source.keys():
                        source[species] = [f'{rop_species.index[i]:30} {rop_species.loc[:, species][i]:9.2e}']
                        source_sorted[species] = [rop_species.loc[:, species][i]]
                    else:
                        source[species].append(f'{rop_species.index[i]:30} {rop_species.loc[:, species][i]:9.2e}')
                        source_sorted[species].append(rop_species.loc[:, species][i])
        for species in source.keys():
            rank_order = np.argsort(np.abs(source_sorted[species]))[::-1]
            source[species] = np.array(source[species])[rank_order]
        ROP = flux.loc[select_species, flux.loc[select_species, :] != 0]
        select_rop = pd.DataFrame(columns=ROP.index, index=['raw', 'abs'])
        select_rop.loc['raw', :] = ROP
        select_rop.loc['abs', :] = ROP.abs()
        select_rop.sort_values('abs', axis=1, inplace=True, ascending=False)
        select_rop.drop('abs', inplace=True)
        production_total = np.sum(select_rop.loc['raw', select_rop.loc['raw', :] > 0]) + 1E-100  # 加一个极小的数防止除0
        consumption_total = np.sum(select_rop.loc['raw', select_rop.loc['raw', :] < 0]) + 1E-100
        self.ROP_percent_flux = pd.DataFrame(index=select_rop.index, columns=select_rop.columns)
        ROP_nor_P = select_rop.loc['raw', select_rop.loc['raw', :] < 0].multiply(1 / consumption_total, axis="index")
        ROP_nor_N = select_rop.loc['raw', select_rop.loc['raw', :] >= 0].multiply(-1 / production_total, axis="index")
        self.ROP_percent_flux.loc['raw', select_rop.loc['raw', :] < 0] = ROP_nor_P[select_rop.loc['raw', :] < 0]
        self.ROP_percent_flux.loc['raw', select_rop.loc['raw', :] >= 0] = ROP_nor_N[select_rop.loc['raw', :] >= 0]
        self.ROP_percent_flux.loc['abs', :] = self.ROP_percent_flux.loc['raw', :].abs()
        self.ROP_percent_flux.index = ['Relative values', 'abs']
        self.ROP_percent_flux.loc['Absolute values', :] = select_rop.loc['raw', :]
        self.ROP_percent_flux.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.ROP_percent_flux.drop('abs', inplace=True)

        self.ROP_percent_flux.loc['Direction', :] = 'To'
        self.ROP_percent_flux.loc[
            'Direction', self.ROP_percent_flux.loc['Relative values', :].astype(float) >= 0] = 'From'
        self.ROP_percent_flux.loc['Source', :] = source
        self.ROP_percent_flux = self.ROP_percent_flux.T[
            np.abs(self.ROP_percent_flux.T['Relative values']) > threshold].T
        max_limit = len(self.ROP_percent_flux.T)
        fig = make_subplots(rows=1, cols=2, horizontal_spacing=0.05)
        fig.add_trace(go.Bar(
            y=self.ROP_percent_flux.columns,
            x=self.ROP_percent_flux.loc['Relative values'] * 100,
            orientation='h',
        ), row=1, col=1)

        fig.add_trace(go.Bar(
            y=select_rop.columns[0:max_limit],
            x=select_rop.loc['raw'][0:max_limit],
            orientation='h',
        ), row=1, col=2)

        fig.update_layout(title={
            'text': f"Cumulative {select_species} formation at {selected_temp}",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=400,
            font=dict(size=12,
                      color="RebeccaPurple"),
            showlegend=False,
            xaxis={'title': 'ROP relative percent (%)', 'rangemode': 'tozero',
                   'ticks': 'outside'},
            xaxis2={'title': 'ROP absolute values (mole cm3 s)', 'showexponent': 'all', 'exponentformat': 'e',
                    'rangemode': 'normal',
                    'ticks': 'outside'},
            yaxis1={'showgrid': True, },
            yaxis2={'side': 'right', 'showgrid': True, })
        if plot:
            iplot(fig)
        return fig

    def flux_all(self, selected_temp, selected_element, threshold=0.01, type='integral', progress_bar=False):
        if type == 'integral':
            rop_species = self.integral_ROP[selected_temp]
        elif type == 'end':
            rop_species = self.end_ROP[selected_temp]
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在处理反信息')
            progress_bar1.empty()
        self.flux = pd.DataFrame(index=self.species, columns=self.species).fillna(0)
        self.flux_normalized = pd.DataFrame(index=self.species, columns=self.species).fillna(0)
        for i, reaction in tqdm(enumerate(self.reactions)):
            for reactant in self.reactants[reaction]:
                if self.species_element.loc[reactant, selected_element] != 0:
                    for product in self.products[reaction]:
                        if self.species_element.loc[
                            product, selected_element] != 0:  # 保证产物中也有元素，才能在get fluex_factor中查询到
                            if rop_species.loc[reaction, reactant] < 0:
                                # 组分在反应物，ROP<0，则反应物向产物转化 ROP<0表示组分在消耗
                                # 需要注意rop_species为反应reaction中向species转化的rop，谁消耗应该就用谁的rop值乘factor为产物的值
                                # 如果始终乘反应物的ROP那么flux_factor没必要进行逆反应运算，直接转置即可
                                self.flux.loc[reactant, product] += rop_species.loc[reaction, reactant] * \
                                                                    self.get_flux_factor(reaction,
                                                                                         selected_element).loc[
                                                                        reactant, product]
                                self.flux.loc[product, reactant] -= rop_species.loc[reaction, reactant] * \
                                                                    self.get_flux_factor(reaction,
                                                                                         selected_element).loc[
                                                                        reactant, product]
                            elif rop_species.loc[
                                reaction, reactant] > 0:  # 组分在反应物，且ROP>0，则产物向反应物转化，reverse反应True，目标向组分转化
                                self.flux.loc[reactant, product] -= rop_species.loc[reaction, product] * \
                                                                    self.get_flux_factor(reaction,
                                                                                         selected_element,
                                                                                         reverse=True).loc[
                                                                        product, reactant]
                                self.flux.loc[product, reactant] += rop_species.loc[reaction, product] * \
                                                                    self.get_flux_factor(reaction,
                                                                                         selected_element,
                                                                                         reverse=True).loc[
                                                                        product, reactant]
                if progress_bar:
                    progress_bar1.progress((i + 1) / len(self.reactions), f"正在处理{reaction}")
        self.flux['Production'] = np.sum(self.flux[self.flux > 0], 1)
        self.flux['Consumption'] = np.sum(self.flux[self.flux <= 0], 1)
        self.flux_normalized = pd.DataFrame(index=self.flux.index, columns=self.flux.columns)
        ROP_nor_P = self.flux[self.flux > 0].multiply(1 / self.flux['Production'], axis="index")
        ROP_nor_N = self.flux[self.flux <= 0].multiply(-1 / self.flux['Consumption'], axis="index")
        self.flux_normalized[self.flux > 0] = ROP_nor_P[self.flux > 0]
        self.flux_normalized[self.flux < 0] = ROP_nor_N[self.flux < 0]
        self.flux_normalized.loc['abs', :] = self.flux_normalized.loc['raw', :].abs()
        self.flux_normalized.index = ['Relative values', 'abs']
        self.flux_normalized.loc['Absolute values', :] = self.select_rop.loc['raw', :]
        self.flux_normalized.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.flux_normalized.drop('abs', inplace=True)
        self.flux_normalized.drop('Production', axis=1, inplace=True)
        self.flux_normalized.drop('Consumption', axis=1, inplace=True)

    def sg_display_format(self, dataframe):
        dataframe.loc['Absolute values', :] = dataframe.loc['Absolute values', :].apply(lambda x: f'{x:.2e}')
        dataframe.loc['Relative values', :] = dataframe.loc['Relative values', :].apply(lambda x: f'{x:.2%}')
        table_values = dataframe.T
        return table_values

    def process_ROP_multi(self, b, select_species, threshold=0.01, datatype='integral', plot=False):
        if b.net_reaction_rate_exist:
            self.species_rop = pd.DataFrame(columns=np.sort(b.variables),
                                            index=self.reactions)  # 此处对variable进行了排序，避免有开头的变量最高，只能在这里排序，在前处理时排序会造成数据对应不了
            #            species_rop = self.integral_ROP[select_species].astype(float)
            for temp in np.sort(b.variables):
                if datatype == 'integral':
                    self.species_rop.loc[:, temp] = self.integral_ROP[temp].loc[:, select_species]
                elif datatype == 'end':
                    self.species_rop.loc[:, temp] = self.end_ROP[temp].loc[:, select_species]
            self.species_rop = copy.deepcopy(self.species_rop[self.species_rop.apply(np.sum, axis=1) != 0].T)
        elif b.net_reaction_rate_exist == False and b.ROP_exist == True:
            if datatype == 'integral':
                self.species_rop = copy.deepcopy(self.integral_ROP[select_species].astype(float))
            elif datatype == 'end':
                self.species_rop = copy.deepcopy(self.end_ROP[select_species].astype(float))
        self.species_rop.loc['overall'] = np.sum(self.species_rop.apply(lambda x: x ** 2), 0)
        self.species_rop.sort_values('overall', axis=1, inplace=True, ascending=False)
        self.species_rop.drop('overall', inplace=True)
        self.species_rop['Production'] = np.sum(self.species_rop[self.species_rop > 0], 1)
        self.species_rop['Consumption'] = np.sum(self.species_rop[self.species_rop <= 0], 1)
        self.species_rop['Net_production'] = self.species_rop['Production'] + self.species_rop['Consumption']

        self.species_rop.insert(0, 'Net_production', self.species_rop.pop('Net_production'))
        self.species_rop.insert(0, 'Consumption', self.species_rop.pop('Consumption'))
        self.species_rop.insert(0, 'Production', self.species_rop.pop('Production'))  # 将这三列调整至前三行

        self.ROP_percent2 = pd.DataFrame(index=self.species_rop.index, columns=self.species_rop.columns, dtype=float)
        ROP_nor_P = self.species_rop[self.species_rop > 0].multiply(1 / self.species_rop['Production'], axis="index")
        ROP_nor_N = self.species_rop[self.species_rop <= 0].multiply(-1 / self.species_rop['Consumption'], axis="index")

        self.ROP_percent2[self.species_rop > 0] = ROP_nor_P[self.species_rop > 0]
        self.ROP_percent2[self.species_rop < 0] = ROP_nor_N[self.species_rop < 0]
        first_three = self.species_rop.iloc[:, :3]
        rest = self.ROP_percent2.iloc[:, 3:].loc[:, self.ROP_percent2.iloc[:, 3:].abs().max() > threshold]
        self.ROP_percent2 = pd.concat([first_three, rest], axis=1)
        species_rop_rest = self.species_rop.loc[:, rest.columns]
        self.species_rop = self.species_rop.loc[:, self.ROP_percent2.columns]
        self.species_rop['Others_production'] = self.species_rop['Production'] - np.sum(
            species_rop_rest[species_rop_rest > 0], axis=1)
        self.species_rop['Others_consumption'] = self.species_rop['Consumption'] - np.sum(
            species_rop_rest[species_rop_rest < 0], axis=1)
        self.ROP_percent2['Others_production'] = 1 - np.sum(rest[rest > 0], axis=1)
        self.ROP_percent2['Others_consumption'] = -1 - np.sum(rest[rest < 0], axis=1)
        self.ROP_percent2_display = copy.deepcopy(self.ROP_percent2)
        self.species_rop_display = copy.deepcopy(self.species_rop)
        self.ROP_percent2_display.iloc[:, :3] = self.ROP_percent2_display.iloc[:, :3].applymap(lambda x: f'{x:.2e}')
        self.ROP_percent2_display.iloc[:, 3:] = self.ROP_percent2.iloc[:, 3:].applymap(
            lambda x: f'{x:.2%}')  # 格式化把非str的单元格变成2位百分数
        self.species_rop_display = self.species_rop.applymap(
            lambda x: f'{x:.2e}' if isinstance(x, float) else x)

    def plot_ROP_multi(self, b, plot=False):
        fig = go.Figure()
        for i in np.arange(0, 3, 1):
            fig.add_trace(go.Scatter(
                x=self.species_rop.index,
                y=self.species_rop.iloc[:, i],
                name=self.species_rop.columns[i],
                line=dict(shape='linear', width=3), ))

        for i in range(3, len(self.ROP_percent2.T)):
            if self.species_rop.iloc[:, i].max() > 0:
                fig.add_trace(go.Scatter(
                    x=self.species_rop.index,
                    y=self.species_rop.iloc[:, i],
                    name=self.species_rop.columns[i],
                    line=dict(shape='linear', width=2), ))
            else:
                fig.add_trace(go.Scatter(
                    x=self.species_rop.index,
                    y=self.species_rop.iloc[:, i],
                    name=self.species_rop.columns[i],
                    line=dict(shape='linear', width=2, dash='dot')))

        fig.update_layout(title={
            'text': f"Rate of production analysis",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=600,
            font=dict(size=12,
                      color="RebeccaPurple"),
            xaxis={'title': f"{b.variable_name} / {b.variable_unit}",
                   'ticks': 'outside'},
            yaxis={'title': " Absolute ROP (mole-cm3-s)", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'})
        if plot:
            fig.show(fig)
        return fig

    def ele_ini(self, b, reactants):
        self.elements_initial = pd.DataFrame(index=self.species_element.columns, columns=b.variables, data=0)
        for reactant in reactants:
            self.elements_initial = self.elements_initial + self.species_element.loc[reactant, :].apply(
                lambda x: x * self.mole_fractions_max.loc[:, reactant])
        self.elements_initial = self.elements_initial.loc[self.elements_initial.max(axis=1) != 0, :].T

    def element_plot(self, b, selected_element, threshold=0.05, plot=False):

        self.element_percent = pd.DataFrame(index=b.variables, columns=self.mole_fractions.columns)
        for sp in self.mole_fractions.columns:
            self.element_percent.loc[:, sp] = self.mole_fractions.loc[:, sp] * self.species_element.loc[
                sp, selected_element] / self.elements_initial.loc[:, selected_element].T
        self.element_percent.loc['overall'] = np.sum(self.element_percent.abs(), 0)
        self.element_percent.sort_values('overall', axis=1, inplace=True, ascending=False)
        self.element_percent.drop('overall', inplace=True)
        self.element_percent = self.element_percent.loc[:, self.element_percent.abs().max() > threshold]
        self.element_percent_display = self.element_percent.applymap(
            lambda x: f'{x:.2%}' if type(x) is float else x).T
        fig = go.Figure()
        for i in range(np.sum(self.element_percent.abs().max() > 0.005)):
            fig.add_trace(go.Scatter(
                x=self.element_percent.index,
                y=self.element_percent.iloc[:, i],
                name=self.element_percent.columns[i],
                line=dict(shape='linear', width=2), ))

        fig.update_layout(title={
            'text': f"Element {selected_element} Distribuation",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=600,
            font=dict(size=16,
                      color="RebeccaPurple"),
            xaxis={'title': "Temperature / K",
                   'ticks': 'outside',
                   },
            yaxis={'title': "Species", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'})
        if plot:
            fig.show(fig)
        return fig


class ckfile():
    def __init__(self, input_file='chemkin_file'):
        self.temperature_exist = None
        self.xlsx = pd.ExcelFile(input_file)
        self.soln_sheets = None
        self.end_point_sheets = None
        self.rop_sheets = None
        self.max_sheets = None
        self.variables = None
        self.num_variable = 0
        self.variable_name = None
        self.variable_unit = None
        self.net_reaction_rate_exist = False
        self.mole_fraction_exist = False
        self.rop_line = False
        self.sensitivity_exist = False
        self.preprocessing = False
        self.ROP_exist = False

    def load_chemkin_file(self, ):
        sheets = self.xlsx.sheet_names
        self.soln_sheets = [sheet for sheet in sheets if 'soln' in sheet]
        self.end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
        self.rop_sheets = [sheet for sheet in sheets if 'rop' in sheet]
        self.max_sheets = [sheet for sheet in sheets if 'max_point' in sheet]
        self.sensitivity_sheets = [sheet for sheet in sheets if 'sen' in sheet]
        if len(self.end_point_sheets) != 0:
            endPointsI = self.end_point_sheets[0]
            endPoints = self.xlsx.parse(endPointsI.rstrip())
            varible_columns = [re.search('.*_C1_.*', name) for name in endPoints.columns
                               if re.search('.*_C1_.*', name) != None]
            self.num_variable = 0
            for varible_column in varible_columns:
                potenial_varible = list(endPoints.loc[:, varible_column[0]])
                if len(potenial_varible) != 1 and len(np.unique(potenial_varible)) == 1:
                    pass
                else:
                    self.variables = potenial_varible
                    self.num_variable += 1
            if self.num_variable > 1:
                print("注意！当前输出结果有不止一个变量，最好分开输出")
            self.variable_name = varible_column[0].strip().split('_')[0]
            self.variable_unit = varible_column[0].strip().split('_')[-1]
            if np.sum(endPoints.columns.str.contains('Net_rxn_rate_')) == 0:
                print('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
                st.warning('注意！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
            else:
                self.net_reaction_rate_exist = True
                print(f'导入文件成功\n 导入文件覆盖工况点包括：{self.variable_name}:{self.variables} {self.variable_unit}')
                st.info(f'导入文件覆盖工况点包括：{self.variable_name} {self.variables} {self.variable_unit}', icon="🤖")
            if np.sum(endPoints.columns.str.contains('Mole_fraction')) != 0:
                self.mole_fraction_exist = True
            if np.sum(endPoints.columns.str.contains('_ROP_')) != 0:
                self.ROP_exist = True
            if np.sum(endPoints.columns.str.contains('_Sensitivity_')) != 0:
                self.sensitivity_exist = True
            if np.sum(endPoints.columns.str.contains('Temperature')) != 0:
                self.temperature_exist = True

        else:
            self.variables = ['unknow']
            firstpoints = self.xlsx.parse(self.soln_sheets[0].rstrip())
            if np.sum(firstpoints.columns.str.contains('Net_rxn_rate_')) == 0:
                print('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
                st.error('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
            if np.sum(firstpoints.columns.str.contains('Mole_fraction')) != 0:
                self.mole_fraction_exist = True
            if np.sum(firstpoints.columns.str.contains('Temperature')) != 0:
                self.temperature_exist = True
            else:
                self.net_reaction_rate_exist = True
                print(f'导入文件成功\n 导入文件为单个点，工况为：{self.variables} {self.variable_unit}')
                print(f'导入文件覆盖工况点包括：{self.variable_name} {self.variables} {self.variable_unit}')
                st.info(f'导入文件覆盖工况点包括：{self.variable_name} {self.variables} {self.variable_unit}', icon="🤖")
        self.preprocessing = True

    def combine_sheets(self, gas_out_file, progress_bar=False, mole_only=False):

        if progress_bar:
            progress_bar1 = st.progress(0, text='正在合并Sheets')
            progress_bar1.empty()
            progress_bar2 = st.progress(0, text='正在积分ROP值')
            progress_bar2.empty()
            progress_bar3 = st.progress(0, text='正在积分ROP值')
            progress_bar3.empty()
        print("正在合并Sheets")

        if self.mole_fraction_exist is True and len(self.end_point_sheets) != 0:
            print("正在合并Sheets")
            for i, sheet in enumerate(tqdm(self.end_point_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_end_raw = self.xlsx.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
                if i == 0:
                    new_sheet = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                else:
                    sheet_end = sheet_end_raw.iloc[:,
                                sheet_end_raw.columns.str.contains(' Mole_fraction_')]  # #如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                    new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
            gas_out_file.mole_fractions = copy.deepcopy(new_sheet)
            gas_out_file.mole_fractions.columns = gas_out_file.mole_fractions.columns.str.replace(' Mole_fraction_',
                                                                                                  '').str.replace(
                '_end_point_()', '', regex=False)
            gas_out_file.mole_fractions.index = self.variables

            ############处理最大值所在列表
            for i, sheet in enumerate(tqdm(self.max_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_max_raw = self.xlsx.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
                if i == 0:
                    new_sheet = sheet_max_raw.iloc[:, sheet_max_raw.columns.str.contains(' Mole_fraction_')]
                else:  # #如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                    sheet_max = sheet_max_raw.iloc[:, sheet_max_raw.columns.str.contains(' Mole_fraction_')]
                    new_sheet = pd.concat([new_sheet, sheet_max], axis=1)
            gas_out_file.mole_fractions_max = copy.deepcopy(new_sheet)
            gas_out_file.mole_fractions_max.columns = gas_out_file.mole_fractions.columns.str.replace(' Mole_fraction_',
                                                                                                      '').str.replace(
                '_max_point_()', '', regex=False)
            gas_out_file.mole_fractions_max.index = self.variables
            gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(8,
                                                                                         gas_out_file.mole_fractions_max.index[
                                                                                             0]).index.tolist()
        # if self.temperature_exist:
        #     temp_soln = dict()
        #     for i, sheet in enumerate(tqdm(self.soln_sheets, desc="正在处理工作表", unit='sheets')):
        #         if sheet.str.contains('no_1'):
        #             temp_soln_raw = self.xlsx.parse(sheet, index_col=0)
        #         temp_first=temp_soln_raw.iloc[0,1]
        #         temp_soln[temp_first]=temp_soln_raw.iloc[:,:2]

        if self.sensitivity_exist is True:
            #            sg.popup("没有导出Net_reaction_rate,只能通过ROP进行分析，需要导出ROP是点选'ALL", title='警告',any_key_closes=True)
            sen_no_p = ''  # 建立一个变量存在上一个工作表的No符号
            sen_collect = dict()  # 建立字典存放不同温读的df表
            working_conditions = 1  # 初始工作表工况序号为1
            print("正在合并Sheets")
            for i, sheet in enumerate(tqdm(self.sensitivity_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_sen_raw = self.xlsx.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
                sheet_sen = sheet_sen_raw.iloc[:, ~sheet_sen_raw.columns.str.contains('Sen')]  # 去掉标题中有Total的列
                sen_no_c = sheet.split('#')[-1].split('_')[0]  # 通过#拆分，#后边的数字就是第几个工况
                if i == 0:  # 如果是第一个工作表新建一个工作表
                    new_sheet = sheet_sen
                if sen_no_c == sen_no_p:  # 如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                    new_sheet = pd.concat([new_sheet, sheet_sen], axis=1)
                if sen_no_c != sen_no_p and i != 0:  # 如果不同，并且不是第一个工作表，那就把之前的工作表保存在soln_collect中
                    sen_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_sen  # 再新建一个工作表，等于当前读取的工作表
                    working_conditions += 1  # 工况序号+1
                sen_no_p = sen_no_c
                if progress_bar:
                    progress_bar3.progress((i + 1) / len(self.sensitivity_sheets),
                                           f"正在积分ROP 工况点：{(i + 1)}/{len(self.sensitivity_sheets)} ")
            sen_collect[self.variables[working_conditions - 1]] = new_sheet
            print('处理完毕!')
            # if self.temperature_exist:
            #     ign_sen_raw2 = pd.DataFrame(index=self.variables, columns=sen_collect[self.variables[0]].columns)
            # end_sen_raw2 = pd.DataFrame(index=self.variables, columns=sen_collect[self.variables[0]].columns)
            # gas_out_file.sen_species_output = [a for a in set([s.strip().split('_')[0] for s in
            #                                                    sen_collect[self.variables[0]].columns])]
            # for temp in (tqdm(sen_collect, desc="正在积分ROP", unit='温度点')):
            #     sen = sen_collect[temp]
            #     if self.temperature_exist:
            #         ign_sen_raw2.loc[temp, :] = sen.iloc[ign_index,:].tolist()
            #     end_sen_raw2.loc[temp, :] = sen.iloc[-1, :].tolist()
            # ign_sen = dict()
            # end_sen = dict()

            # for species in tqdm(gas_out_file.sen_species_output, desc="正在处理组分信息", unit='组分'):
            #     ign_sen[species] = ign_sen_raw2.loc[:,
            #                         ign_sen_raw2.columns.str.contains(' ' + species + '_Sen', regex=False)]
            #     end_sen[species] = end_sen_raw2.loc[:,
            #                         end_sen_raw2.columns.str.contains(' ' + species + '_Sen', regex=False)]
            #     reaction_sen = [gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] for a in
            #                     ign_sen[species].columns.str.split('#')]  # 利用reaction_index找到分割#和_之间的反应序号
            #     reaction_sen_end = [gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] for a in
            #                         end_sen[species].columns.str.split('#')]

        if self.net_reaction_rate_exist:
            soln_no_p = ''  # 建立一个变量存在上一个工作表的No符号
            soln_collect = dict()  # 建立字典存放不同温读的df表
            working_conditions = 1  # 初始工作表工况序号为1
            for i, sheet in enumerate(tqdm(self.soln_sheets, desc="正在处理工作表", unit='sheets')):

                sheet_soln_raw = self.xlsx.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
                sheet_soln = sheet_soln_raw.iloc[:,
                             sheet_soln_raw.columns.str.contains('Net')]  # 找到列标题中包含'Net'的列，即净反应速率
                if '#' in sheet:
                    soln_no_c = sheet.split('#')[-1].split('_')[0]  # 通过#拆分，#后边的数字就是第几个工况
                else:  # 对单个点没有end_point sheet的情况
                    soln_no_c = 1
                if i == 0:  # 如果是第一个工作表新建一个工作表
                    new_sheet = sheet_soln
                if soln_no_c == soln_no_p:  # 如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                    new_sheet = pd.concat([new_sheet, sheet_soln], axis=1)
                if soln_no_c != soln_no_p and i != 0:  # 如果不同，并且不是第一个工作表，那就把之前的工作表保存在soln_collect中
                    soln_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_soln  # 再新建一个工作表，等于当前读取的工作表
                    working_conditions += 1  # 工况序号+1
                soln_no_p = soln_no_c
                if progress_bar:
                    progress_bar1.progress((i + 1) / len(self.soln_sheets),
                                           f'正在合并Sheets:{i + 1}/{len(self.soln_sheets)}')
            soln_collect[self.variables[working_conditions - 1]] = new_sheet
            print('处理完毕!')
            stoichi = copy.deepcopy(gas_out_file.stoichimetric)
            if len(soln_collect[self.variables[0]].columns) != len(gas_out_file.reaction_index):
                st.warning("注意当前反应速率输出不完全，可能存在反应没有路径连接")

                exist_reaction_index = []
                all_reaction = set([i for i in range(1, len(gas_out_file.reaction_index) + 1)])

                for no in soln_collect[self.variables[0]].columns:
                    exist_reaction_index.append(int(no.split('#')[1].split('_')[0]))
                no_reaction_index = list(all_reaction - set(exist_reaction_index))
                for r in no_reaction_index:
                    print(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                    st.warning(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                    stoichi.drop(gas_out_file.stoichimetric.index[r - 1], inplace=True)
            int_rate = dict()  # 类似的建立积分速率和积分ROP的储存字典
            int_rop = dict()
            end_rate = dict()
            end_rop = dict()
            print('正在积分ROP值')
            try:
                for i, temp in enumerate(tqdm(soln_collect, desc="正在积分ROP", unit='温度点')):
                    rate = soln_collect[temp]  # 对每一个温度点的rate进行积分

                    int_rate[temp] = [np.trapz(rate.loc[:, j], rate.index) for j in rate.columns]
                    end_rate[temp] = rate.iloc[-1, :].tolist()
                    int_rop[temp] = stoichi.apply(lambda x: np.multiply(x, int_rate[temp]), axis=0)
                    end_rop[temp] = stoichi.apply(lambda x: np.multiply(x, end_rate[temp]), axis=0)
                    gas_out_file.rop_species_output = gas_out_file.species
                    if progress_bar:
                        progress_bar2.progress((i + 1) / len(soln_collect),
                                               f"正在积分ROP 工况点：{(i + 1)}/{len(soln_collect)} ")
                print('处理完毕！')
            except ValueError:
                int_rop = None
                print("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
                st.error("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
            gas_out_file.integral_ROP = int_rop
            gas_out_file.end_ROP = end_rop

        elif self.net_reaction_rate_exist == False and self.ROP_exist == True:
            print("没有导出Net_reaction_rate,只能通过ROP进行分析，需要导出ROP是点选'ALL")
            rop_no_p = ''  # 建立一个变量存在上一个工作表的No符号
            rop_collect = dict()  # 建立字典存放不同温读的df表
            working_conditions = 1  # 初始工作表工况序号为1
            print("正在合并Sheets")
            for i, sheet in enumerate(tqdm(self.rop_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_rop_raw = self.xlsx.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
                sheet_rop = sheet_rop_raw.iloc[:, ~sheet_rop_raw.columns.str.contains('Total')]  # 去掉标题中有Total的列
                rop_no_c = sheet.split('#')[-1].split('_')[0]  # 通过#拆分，#后边的数字就是第几个工况
                if i == 0:  # 如果是第一个工作表新建一个工作表
                    new_sheet = sheet_rop
                if rop_no_c == rop_no_p:  # 如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                    new_sheet = pd.concat([new_sheet, sheet_rop], axis=1)
                if rop_no_c != rop_no_p and i != 0:  # 如果不同，并且不是第一个工作表，那就把之前的工作表保存在soln_collect中
                    rop_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_rop  # 再新建一个工作表，等于当前读取的工作表
                    working_conditions += 1  # 工况序号+1
                rop_no_p = rop_no_c
                if progress_bar:
                    progress_bar2.progress((i + 1) / len(self.rop_sheets),
                                           f"正在积分ROP 工况点：{(i + 1)}/{len(self.rop_sheets)} ")
            rop_collect[self.variables[working_conditions - 1]] = new_sheet
            print('处理完毕!')
            int_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)
            end_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)
            gas_out_file.rop_species_output = [a for a in set([s.strip().split('_')[0] for s in
                                                               rop_collect[self.variables[0]].columns])]
            for temp in (tqdm(rop_collect, desc="正在积分ROP", unit='温度点')):
                rop = rop_collect[temp]
                int_rop_raw2.loc[temp, :] = [np.trapz(rop.loc[:, j], rop.index) for j in rop.columns]
                end_rop_raw2.loc[temp, :] = rop.iloc[-1, :].tolist()
            int_rop2 = dict()
            end_rop2 = dict()
            for species in tqdm(gas_out_file.rop_species_output, desc="正在处理组分信息", unit='组分'):
                int_rop2[species] = int_rop_raw2.loc[:,
                                    int_rop_raw2.columns.str.contains(' ' + species + '_ROP', regex=False)]
                end_rop2[species] = end_rop_raw2.loc[:,
                                    end_rop_raw2.columns.str.contains(' ' + species + '_ROP', regex=False)]
                reaction_rop = [gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0]+"#"+a[1].split('_')[0] for a in
                                int_rop2[species].columns.str.split('#')]  # 利用reaction_index找到分割#和_之间的反应序号
                reaction_rop_end = [gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0]+"#"+a[1].split('_')[0] for a in
                                    end_rop2[species].columns.str.split('#')]  # 利用reaction_index找到分割#和_之间的反应序号
                int_rop2[species].columns = reaction_rop
                end_rop2[species].columns = reaction_rop_end
            print('处理完毕!')
            gas_out_file.integral_ROP = int_rop2
            gas_out_file.end_ROP = end_rop2


class ct_file():
    def __init__(self, input_file='thermo_file'):
        if os.path.exists(input_file):
            print(input_file)
            result = os.system(f'python -m cantera.ck2yaml --thermo="{input_file}" --permissive --output=thermo.yaml')
            if result == 0:
                self.ct_species = ct.Species.list_from_file('thermo.yaml')
                self.reverse_rate_constants = None
                st.success('导入成功！')
            else:
                st.error('热力学文件导入失败，请检查！')
        else:
            st.error('文件不存在！')

    def reversed_rate(self, a, selected_reaction, temp_min, temp_max):
        """

        :param a: gas_out class
        :param selected_reaction: reaction equation
        :return: dataframe
        """
        reaction_where = a.reactions.index(selected_reaction)
        reaction = ct.Reaction(equation=a.cantera_reactions[reaction_where], rate=a.Arr_SI_unit[reaction_where])
        reaction_reverse_name = f'{reaction.product_string} <=> {reaction.reactant_string}'
        if reaction.reversible:
            try:
                self.ct_gas = ct.Solution(thermo='ideal-gas', species=self.ct_species, kinetics='gas',
                                          reactions=[reaction])
                self.reverse_rate_constants = pd.DataFrame()
                for T in np.arange(temp_min, temp_max, 10):
                    self.ct_gas.TP = T, ct.one_atm
                    self.reverse_rate_constants.loc[T, reaction_reverse_name] = self.ct_gas.reverse_rate_constants[0] / (1000**(len(reaction.products)-1))
                    self.reverse_rate_constants.loc[T, 'forward'] = self.ct_gas.forward_rate_constants[0] / (1000**(len(reaction.reactants)-1))
                    self.reverse_rate_constants.loc[T, 'EQ_constant'] = self.ct_gas.equilibrium_constants[0]*(1000**(len(reaction.reactants)-len(reaction.products))) #kmol/m3 to mol/cm3
            except ValueError:
                #               sg.popup_error('错误！热力学文件中找不到组分信息')
                pass
        else:
            pass

    #           sg.popup_error('该反应不可逆！',)
    # def fit_arr(self, T_low, T_high):
    #     def Arr_fit(T, A, n, Ea):
    #         return np.log(A) + n * np.log(T) - Ea / (T * 8.314 / 4.184)
    #
    #     ln_rc_fit = np.array(np.log(self.reverse_rate_constants.iloc[:, 0].loc[float(T_low):float(T_high)]))
    #     T_fit = np.array(self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high].index)
    #     try:
    #         popt, pcov = curve_fit(Arr_fit, T_fit, ln_rc_fit)
    #         self.Arr_fitted = dict()
    #         self.Arr_fitted['A'] = popt[0]
    #         self.Arr_fitted['b'] = popt[1]
    #         self.Arr_fitted['Ea'] = popt[2]
    #         data_fit_origin = pd.DataFrame(index=T_fit, columns=['original', 'fitted'])
    #         data_fit_origin.loc[:, 'original'] = self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high]
    #         data_fit_origin.loc[:, 'fitted'] = popt[0] * T_fit ** popt[1] * np.exp(-popt[2] / (T_fit * 8.314 / 4.184))
    #         return data_fit_origin
    #     except RuntimeError:
    #         #            sg.popup_error("拟合失败！该反应无法拟合为单方程阿仑尼乌斯形式")
    #         pass
    def fit_arr(self, T_low, T_high):
        # 定义 Arrhenius 方程
        def Arr_fit(T, A, n, Ea):
            return np.log(A) + n * np.log(T) - Ea / (T * 8.314 / 4.184)

        # 从原始数据中选择拟合区间的数据
        ln_rc_fit = np.array(np.log(self.reverse_rate_constants.iloc[:, 0].loc[float(T_low):float(T_high)]))
        print(ln_rc_fit)
        T_fit = np.array(self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high].index)
        print(T_fit)
        # 使用 LMFIT 建立模型
        model = Model(Arr_fit)

        # 设置参数的初始值和边界（可根据需要调整）
        params = model.make_params(A=10000, n=1, Ea=5000)

        # 执行拟合

        result = model.fit(ln_rc_fit, params, T=T_fit)

        # 保存拟合结果
        self.Arr_fitted = {
            'A': result.params['A'].value,
            'b': result.params['n'].value,
            'Ea': result.params['Ea'].value
        }

        # 构造一个包含原始数据和拟合数据的 DataFrame
        data_fit_origin = pd.DataFrame(index=T_fit, columns=['original', 'fitted'])
        data_fit_origin.loc[:, 'original'] = self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high]
        data_fit_origin.loc[:, 'fitted'] = np.exp(
            np.log(result.params['A'].value) +
            result.params['n'].value * np.log(T_fit) -
            result.params['Ea'].value / (T_fit * 8.314 / 4.184)
            )


def compare_plot(exp_file, simu_files,store_path, progress_bar=False):
    exp_data = pd.read_excel(exp_file, index_col=0)
    files = simu_files
    file_names = []
    mole_fractions = dict()
    data_plot = dict()
    progress_bar1 = st.progress(0, text='正在处理反信息')
    progress_bar1.empty()
    for file in files:
        file_name = file.name
        file_names.append(file.name)
        sheets = pd.ExcelFile(file).sheet_names
        xl = pd.ExcelFile(file)
        end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
        for i, sheet in enumerate(end_point_sheets):
            sheet_end_raw = xl.parse(sheet, index_col=0)  # 读取工作表，将其第一列设为index
            if i == 0:
                new_sheet = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
            else:
                sheet_end = sheet_end_raw.iloc[:,
                            sheet_end_raw.columns.str.contains(' Mole_fraction_')]  # #如果前一个工作表工况和后一个工况相同，则把两个工作表合并
                new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
            mole_fractions[file_name] = copy.deepcopy(new_sheet)
            mole_fractions[file_name].columns = mole_fractions[file_name].columns.str.replace(' Mole_fraction_', '',
                                                                                              regex=False).str.replace(
                '_end_point_()', '', regex=False)
            variable = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains('.*_C1_.*')]
            if len(sheet_end_raw) == 0:
                #                sg.popup_error("没有导出摩尔分数！")
                pass
                return None
            if len(variable.T) > 1:
                print('注意，导出了两种反应变量，会导致绘图出现错误，请选一种')
                exit()
            else:
                mole_fractions[file_name].index = variable.iloc[:, 0]
                data_plot[file_name] = pd.DataFrame(index=variable.iloc[:, 0], columns=exp_data.columns)

    variable_name = variable.columns.str.split('_')[0][0]
    variable_unit = variable.columns.str.split('_')[0][-1]
    # varible_columns = [re.search('.*_C1_.*', name) for name in data_end[file_name].columns
    #                    if re.search('.*_C1_.*', name) != None]
    colors = plotly.colors.qualitative.Plotly
    rows_plot = (len(exp_data.columns) + 1) // 2  # 确保行数足够容纳所有列
    position = 1 - 1 / rows_plot + 0.01
    # 创建子图，设置每个子图的标题
    fig = make_subplots(rows=rows_plot, cols=2, subplot_titles=exp_data.columns, vertical_spacing=0.02)
    for num_p, species in enumerate(exp_data.columns):
        if progress_bar1:
            progress_bar1.progress((num_p + 1) / len(exp_data.columns))  # 更新进度条
        row = int(num_p / 2) + 1
        col = int(num_p % 2) + 1

        for i, file_name in enumerate(file_names):
            # 添加模拟数据的轨迹
            try:
                fig.add_trace(
                    go.Scatter(
                        x=mole_fractions[file_name].index,
                        y=mole_fractions[file_name][species],
                        mode='lines',
                        name=f'Sim_{file_name}',
                        line=dict(color=colors[i % len(colors)]),  # 使用颜色列表中的颜色
                        showlegend=(num_p == 0)  # 只在第一个子图的第一个文件名显示图例
                    ),
                    row=row,
                    col=col
                )
            except KeyError as err:
                 st.warning(f'警告，{file_name}机理文件中没有找到{species}')
        # 添加实验数据的轨迹
        fig.add_trace(
            go.Scatter(
                x=exp_data.index,
                y=exp_data[species],
                mode='markers',
                name='Exp.',
                showlegend=(num_p == 0)  # 只在第一个子图显示实验数据的图例
            ),
            row=row,
            col=col
        )
        fig.update_xaxes(title_text=f"{variable_name} {variable_unit}", row=row, col=col)
        fig.update_yaxes(title_text="Mole Fraction", tickformat='.2e', row=row, col=col)
    # 更新整体布局设置，一次性设置避免循环中重复设置
    fig.update_layout(
        title='Comparison Plot',
        legend=dict(
            x=0.5,
            y=position,
            xanchor='center',
            yanchor='top',
            orientation='h',  # 水平放置图例
            font=dict(family="Courier", size=14)),
        xaxis_title=f"{variable_name} ({variable_unit})",
        yaxis={'title': "Mole Fraction", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'},
        height=500 * rows_plot,  # 调整整个图表的高度
        width=1000  # 图表宽度
    )

    current_datetime = datetime.datetime.now()
    formatted_datetime = current_datetime.strftime("%Y%m%d-%H-%M")
    # Showing the plot
    fig.write_html(f'{store_path}/{formatted_datetime}对比结果.html')
    print()

    os.startfile(f'{store_path}/{formatted_datetime}对比结果.html')
    return data_plot


# data_plot.to_excel(f'{output_path}/Sim_data_{EQ}.xlsx')

def save_compare_plot(save_path, data_plot):
    writer = pd.ExcelWriter(save_path, engine='openpyxl')
    for key in data_plot.keys():
        data_plot[key].to_excel(writer, f'{key}')
    writer.close()


def forward_rate(reaction_equation, Arr_param, t_min=300, t_max=2000):
    """
    :param temp_range:
    :param reaction_equation: string
    :param Arr_param: ckfile.Arr_para
    :return: dataframe
    """
    temp_range = np.arange(t_min, t_max, 10)

    def rate_constant(A, n, Ea, temp_range):
        temps = temp_range
        rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
        return rc

    rc = rate_constant(Arr_param['A'], Arr_param['b'], Arr_param['Ea'], temp_range)
    fc = pd.DataFrame(index=temp_range, columns=[reaction_equation], data=rc)
    return fc


def process_rc(string=None, t_min=300, t_max=2000):
    """
    temp_range=np.arange(300, 3500, 10)
    :param temp_range:
    :param string:
    :return: DataFrame contains temperature and rc
    """
    temp_range = np.arange(t_min, t_max, 10)
    reactions = string
    lines = reactions.split('\n')

    def rate_constant(A, n, Ea, temp_range):
        temps = temp_range
        rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
        return rc

    data = pd.DataFrame(index=temp_range)
    for line in lines:
        if not line.strip() == '':
            if '!' in line:
                line = line[0:line.rfind('!')].strip()
            else:
                line = line.strip()
            try:
                if 'PLOG' in line:
                    reaction = 'P=' + line.split('/')[1].split()[0]
                    A = float(line.split('/')[1].split()[1])
                    n = float(line.split('/')[1].split()[2])
                    Ea = float(line.split('/')[1].split()[3])
                elif '=' in line:
                    id = 1
                    reaction = re.split(r"\s+", line)[0]
                    A = float(re.split(r"\s+", line)[id])
                    n = float(re.split(r"\s+", line)[id + 1])
                    Ea = float(re.split(r"\s+", line)[id + 2])
                else:
                    id = 0
                    A = float(re.split(r"\s+", line)[id])
                    n = float(re.split(r"\s+", line)[id + 1])
                    Ea = float(re.split(r"\s+", line)[id + 2])
                    reaction = f'{A=:.2e}  {n=:.2f}  {Ea=:.2e}'
                rc = rate_constant(A, n, Ea, temp_range)
                if reaction in data.columns:
                    data.loc[:, reaction] = data.loc[:, reaction]+rc
                else:
                    data.loc[:, reaction] = rc
                print(rc)
            except ValueError:
                pass
    #                sg.popup_error("错误！输入参数不符合规范，请确保反应式前无多余字符，且每个反应之间通过回车换行")
    return data


def plot_rc(dataframe, plot=False):
    fig = go.Figure()
    for col in dataframe.columns:
        fig.add_trace(go.Scatter(x=dataframe.index.astype(float), y=dataframe[col],
                                 mode='lines',  # 线条模式
                                 name=col,  # 使用列名作为图例标签
                                 line=dict(width=2)))  # 线条宽度

    # 设置图形的布局
    fig.update_layout(
        title='Rate constant',
        xaxis_title='Temperature / K',
        yaxis={'title': 'Rate constant / cm^3 mol^-1 s^-1', 'showgrid': True, 'rangemode': 'tozero',
               'tickformat': '.1e'},
        yaxis_type='log',  # Y轴设置为对数刻度
        legend=dict(
            x=0.5,
            y=-0.2,
            xanchor='center',
            yanchor='top',
            orientation='h',
            font=dict(size=14)
        ),
        margin=dict(r=20),  # 右边距，确保图例的空间
    )

    # 添加网格
    fig.update_xaxes(showgrid=True)
    fig.update_yaxes(showgrid=True)
    if plot:
        fig.show()
    return fig


if __name__ == '__main__':
    #a = solution('e:\\BaiduSyncdisk\\py\\ipynb\\rop\\test1_gas.out', test=True)
    a = solution('D:\\chemkin\\DMF_OXIDATION\\New_gas.out', test=True)
    a.process_gas_out()
    thermofile_path="D:\\thermo.dat"
    b = ckfile('D:\\chemkin\\DMF_OXIDATION\\OH_ROP.xlsm')
    b.load_chemkin_file()
    b.combine_sheets(a)
    #selected_temp = 0.7
    #selected_element = 'N'
    #select_species = 'CH3NH2'
    #c = ct_file(thermofile_path)
    #ctfile.reversed_rate(a, 'CH3+NH2=CH3NH2#1798', 200, 2090)
    #ctfile.reverse_rate_constants.iloc[:, 2]
    #a.plot_ROP_single_species( b,select_species, selected_temp,selected_element, threshold=0.01,datatype='integral')
    #a.flux_all(selected_temp, selected_element, threshold=0.01, datatypetype='integral')
    #a.plot_ROP_single('CH3NH2', 0.7, 'N',0.04,datatype='integral')
    a.process_ROP_multi(b, 'OH', threshold=0.01, datatype='end', plot=False)

    #c.reversed_rate(a,'O+DMF=OH+CH3N(CH2)CHO#15',500,2000)
    #rc_fit = c.fit_arr(500,2000)

# c.K_ci_process('A1CH3(+M)=A1CH2+H(+M)#1',1000,1,a.stoichimetric)
