import streamlit as st
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="元素流向分析", page_icon="🔄")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .error-card {
        background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--warning-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .warning-card {
        background: linear-gradient(135deg, rgba(255, 127, 14, 0.1), rgba(31, 119, 180, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--secondary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .success-card {
        background: linear-gradient(135deg, rgba(44, 160, 44, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--success-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 输入控件样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 选择框内容区域 */
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 选择框文本 */
    .stSelectbox > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 选择框选项样式 */
    .stSelectbox option {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 下拉选项 */
    .stSelectbox [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 下拉选项悬停效果 */
    .stSelectbox [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 下拉菜单容器 */
    .stSelectbox [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 选择框显示文本 */
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保下拉箭头可见 */
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stMultiSelect > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 多选框样式改进 */
    .stMultiSelect > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    .stMultiSelect > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 多选框下拉选项 */
    .stMultiSelect [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 多选框下拉选项悬停效果 */
    .stMultiSelect [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 多选框下拉菜单容器 */
    .stMultiSelect [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 多选框显示文本 */
    .stMultiSelect [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保多选框下拉箭头可见 */
    .stMultiSelect [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 数据表格样式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        border-radius: 4px;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">🔄 元素流向分析</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        元素流向路径分析 - 追踪元素在不同组分间的转移和分布变化
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

# 添加详细的调试信息
print("=" * 80)
print("元素流向分析页面调试信息")
print("=" * 80)

# 检查session_state中的对象
print(f"session_state keys: {list(st.session_state.keys())}")

if 'a' in st.session_state:
    gas_obj = st.session_state['a']
    print(f"gas_obj type: {type(gas_obj)}")
    print(f"gas_obj module: {gas_obj.__class__.__module__}")

    # 检查关键属性
    key_attrs = ['elements', 'possible_reactants', 'species', 'species_element', 'species_MW']
    for attr in key_attrs:
        if hasattr(gas_obj, attr):
            value = getattr(gas_obj, attr)
            if value is not None:
                if hasattr(value, 'shape'):
                    print(f"  {attr}: {type(value)} with shape {value.shape}")
                elif hasattr(value, '__len__'):
                    print(f"  {attr}: {type(value)} with length {len(value)}")
                    if isinstance(value, list) and len(value) > 0:
                        print(f"    first few items: {value[:5]}")
                else:
                    print(f"  {attr}: {type(value)} = {value}")
            else:
                print(f"  {attr}: None")
        else:
            print(f"  {attr}: NOT FOUND")

if 'b' in st.session_state:
    chemkin_obj = st.session_state['b']
    print(f"chemkin_obj type: {type(chemkin_obj)}")
    print(f"chemkin_obj module: {chemkin_obj.__class__.__module__}")
    print(f"mole_fraction_exist: {getattr(chemkin_obj, 'mole_fraction_exist', 'NOT FOUND')}")

print("=" * 80)

if ('a' and 'b') not in st.session_state:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行元素流向分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

elif st.session_state['b'].mole_fraction_exist is False:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少摩尔分数数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有导出摩尔分数，无法进行元素流向分析。请检查CHEMKIN输出设置。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ 分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        # 检查elements属性是否存在和有效
        print(f"检查elements属性...")
        gas_obj = st.session_state['a']

        # 详细检查elements属性
        has_elements = hasattr(gas_obj, 'elements')
        elements_value = getattr(gas_obj, 'elements', None)
        print(f"  hasattr(gas_obj, 'elements'): {has_elements}")
        print(f"  elements value: {elements_value}")
        print(f"  elements type: {type(elements_value)}")

        if has_elements and elements_value:
            print(f"  elements content: {elements_value}")
            selected_element = st.selectbox(
                "**目标元素**：",
                options=elements_value,
                help="选择要追踪的元素"
            )
        else:
            st.error("❌ 元素数据未找到，请检查前处理是否正确完成")
            st.info(f"调试信息: elements属性 = {elements_value}")

            # 尝试查找其他可能的元素属性
            possible_element_attrs = [attr for attr in dir(gas_obj) if 'element' in attr.lower()]
            st.info(f"可能的元素相关属性: {possible_element_attrs}")

            st.stop()
    
    with col2:
        # 检查possible_reactants属性是否存在
        print(f"检查possible_reactants属性...")
        gas_obj = st.session_state['a']

        # 详细检查possible_reactants属性
        has_possible_reactants = hasattr(gas_obj, 'possible_reactants')
        possible_reactants_value = getattr(gas_obj, 'possible_reactants', None)
        print(f"  hasattr(gas_obj, 'possible_reactants'): {has_possible_reactants}")
        print(f"  possible_reactants value: {possible_reactants_value}")
        print(f"  possible_reactants type: {type(possible_reactants_value)}")

        if has_possible_reactants and possible_reactants_value:
            print(f"  possible_reactants content: {possible_reactants_value}")
            selected_species = st.multiselect(
                "**初始反应物组成**：",
                possible_reactants_value,
                help="选择初始反应物，用于元素流向分析的起点"
            )
        else:
            st.error("❌ 反应物数据未找到，请检查前处理是否正确完成")
            st.info(f"调试信息: possible_reactants属性 = {possible_reactants_value}")

            # 尝试查找其他可能的反应物属性
            possible_reactant_attrs = [attr for attr in dir(gas_obj) if 'reactant' in attr.lower() or 'species' in attr.lower()]
            st.info(f"可能的反应物相关属性: {possible_reactant_attrs[:10]}")  # 只显示前10个

            # 检查species属性作为备选
            if hasattr(gas_obj, 'species') and gas_obj.species:
                st.info(f"发现species属性，长度: {len(gas_obj.species)}")
                st.info(f"前几个species: {gas_obj.species[:5] if gas_obj.species else 'None'}")

            st.stop()
    
    with col3:
        threshold = st.number_input(
            '**相对值显示阈值**',
            min_value=0.0,
            max_value=1.0,
            value=0.001,  # 降低默认阈值
            format="%.4f",
            help="设置显示阈值，过滤较小的流向贡献。如果结果为空，请尝试降低此值"
        )

        # 添加显示所有数据的选项
        show_all = st.checkbox(
            "显示所有数据（忽略阈值）",
            value=False,
            help="勾选此项将显示所有组分，忽略阈值设置"
        )
    
    # 检查是否需要选择横坐标变量（没有end_point sheets的情况）
    if hasattr(st.session_state['b'], 'need_extract_endpoints') and st.session_state['b'].need_extract_endpoints:
        st.markdown("---")
        st.markdown("##### 🎯 横坐标变量选择")
        st.info("检测到没有end_point数据表，请选择横坐标变量和数据点类型：")
        
        col1_axis, col2_axis = st.columns(2)
        
        with col1_axis:
            # 获取可用的横坐标变量
            x_vars_last = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'last')
            x_vars_first = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'first')
            
            # 合并两种数据点的变量选项
            all_x_vars = set(x_vars_last.keys()) | set(x_vars_first.keys())
            
            if all_x_vars:
                selected_x_var = st.selectbox(
                    "**横坐标变量**：",
                    options=list(all_x_vars),
                    help="选择用作横坐标的变量",
                    key="element_x_var"
                )
            else:
                st.error("未找到可用的横坐标变量")
                selected_x_var = None
        
        with col2_axis:
            point_type = st.radio(
                "**数据点类型**：",
                options=['last', 'first'],
                format_func=lambda x: '最后一点' if x == 'last' else '第一点',
                help="选择使用每个工况的第一点还是最后一点数据",
                key="element_point_type"
            )
        
        # 更新横坐标变量设置
        if selected_x_var and point_type:
            x_vars_data = x_vars_last if point_type == 'last' else x_vars_first
            if selected_x_var in x_vars_data:
                # 临时更新variable信息用于绘图
                st.session_state['a'].temp_variable_name = selected_x_var
                st.session_state['a'].temp_variable_unit = ''
                st.session_state['a'].temp_variables = x_vars_data[selected_x_var]
                
                # 显示选择的横坐标数据
                st.success(f"已选择：{selected_x_var} ({point_type}点)")
                st.text(f"数据值: {x_vars_data[selected_x_var]}")
            else:
                st.error(f"所选变量 {selected_x_var} 在 {point_type} 点数据中不可用")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 分析计算和结果显示
# ========================================

# 添加分析执行调试信息
print("=" * 60)
print("分析执行调试信息")
print("=" * 60)
print(f"selected_element: {selected_element if 'selected_element' in locals() else 'NOT DEFINED'}")
print(f"selected_species: {selected_species if 'selected_species' in locals() else 'NOT DEFINED'}")
print(f"threshold: {threshold if 'threshold' in locals() else 'NOT DEFINED'}")
print(f"show_all: {show_all if 'show_all' in locals() else 'NOT DEFINED'}")
print(f"selected_species type: {type(selected_species) if 'selected_species' in locals() else 'NOT DEFINED'}")
print(f"selected_species length: {len(selected_species) if 'selected_species' in locals() and selected_species else 0}")

# 动态调整阈值
effective_threshold = 0.0 if show_all else threshold
print(f"effective_threshold: {effective_threshold}")
print("=" * 60)

if not selected_species:
    print("显示警告：请选择初始反应物")
    st.markdown("""
<div class="warning-card">
        <h3 style="color: var(--secondary-color); margin: 0;">⚙️ 请选择初始反应物</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请在上方参数设置中选择初始反应物组成，分析结果将自动显示。
        </p>
    </div>
    """, unsafe_allow_html=True)
else:
    print("开始执行元素流向分析...")
    print(f"  使用元素: {selected_element}")
    print(f"  使用反应物: {selected_species}")
    print(f"  原始阈值: {threshold}")
    print(f"  有效阈值: {effective_threshold}")

    # 自动分析计算
    with st.spinner('正在进行元素流向分析，请稍候...'):
        try:
            print("  步骤1: 调用ele_ini...")
            # 初始化元素分析
            st.session_state['a'].ele_ini(st.session_state['b'], selected_species)
            print("  步骤1: ele_ini完成")

            print("  步骤2: 调用element_plot...")
            # 生成元素流向图
            fig_element = st.session_state['a'].element_plot(
                st.session_state['b'],
                selected_element,
                threshold=effective_threshold
            )
            print(f"  步骤2: element_plot完成，返回类型: {type(fig_element)}")

            # 检查过滤前的数据
            if hasattr(st.session_state['a'], 'element_percent'):
                element_percent_raw = st.session_state['a'].element_percent
                print(f"  过滤后element_percent shape: {element_percent_raw.shape}")
                if element_percent_raw.shape[1] > 0:
                    print(f"  过滤后列名: {list(element_percent_raw.columns)}")
                    print(f"  过滤后最大值: {element_percent_raw.abs().max().max()}")
                else:
                    print("  ❌ 过滤后没有数据列")

            # 检查摩尔分数数据
            if hasattr(st.session_state['a'], 'mole_fractions'):
                mole_fractions = st.session_state['a'].mole_fractions
                print(f"  mole_fractions shape: {mole_fractions.shape}")
                print(f"  mole_fractions columns: {list(mole_fractions.columns[:5])}...")  # 只显示前5个

                # 检查选择元素的组分
                if hasattr(st.session_state['a'], 'species_element'):
                    species_element = st.session_state['a'].species_element
                    element_species = species_element[species_element[selected_element] > 0]
                    print(f"  含{selected_element}元素的组分数量: {len(element_species)}")
                    print(f"  含{selected_element}元素的组分: {list(element_species.index[:10])}...")  # 只显示前10个
            
            print("  步骤3: 显示结果...")

            # 检查生成的数据
            if hasattr(st.session_state['a'], 'element_percent_display'):
                element_data = st.session_state['a'].element_percent_display
                print(f"  element_percent_display shape: {element_data.shape}")
                print(f"  element_percent_display type: {type(element_data)}")
                print(f"  element_percent_display columns: {list(element_data.columns)}")
                print(f"  element_percent_display index: {list(element_data.index)}")
            else:
                print("  ❌ element_percent_display 不存在")

            # 显示成功消息
            st.markdown("""
<div class="success-card">
                <h3 style="color: var(--success-color); margin: 0;">✅ 分析完成</h3>
                <p style="margin: 0.5rem 0 0 0;">
                    元素流向分析已完成，结果如下所示。
                </p>
            </div>
            """, unsafe_allow_html=True)

            # 显示图表
            print("  显示图表...")
            st.markdown(f"### 🔄 {selected_element} 元素流向分布图")
            if fig_element is not None:
                st.plotly_chart(fig_element, use_container_width=True)
                print("  图表显示完成")
            else:
                st.error("图表生成失败")
                print("  ❌ 图表为None")

            # 显示数据表格
            print("  显示数据表格...")
            st.markdown("### 📊 元素分布详细数据")

            st.markdown(f"#### {selected_element} 元素在各组分中的分布 (突出显示最大值)")

            if hasattr(st.session_state['a'], 'element_percent_display'):
                try:
                    st.dataframe(
                        st.session_state['a'].element_percent_display.style.highlight_max(axis=0),
                        use_container_width=True
                    )
                    print("  数据表格显示完成")
                except Exception as table_error:
                    st.error(f"数据表格显示失败: {table_error}")
                    print(f"  ❌ 数据表格显示失败: {table_error}")
                    # 尝试显示原始数据
                    st.dataframe(st.session_state['a'].element_percent_display, use_container_width=True)
            else:
                st.error("数据表格不可用")
                print("  ❌ element_percent_display 不存在")
            
        except Exception as e:
            print(f"  ❌ 元素流向分析异常: {str(e)}")
            print(f"  异常类型: {type(e)}")
            import traceback
            print(f"  异常堆栈: {traceback.format_exc()}")

            st.error(f"❌ 元素流向分析失败: {str(e)}")
            st.info("请检查数据完整性和参数设置")

            # 显示详细错误信息
            with st.expander("🔍 详细错误信息", expanded=False):
                st.code(traceback.format_exc())

# ========================================
# 分析说明
# ========================================

with st.expander("ℹ️ 分析说明", expanded=False):
    st.markdown("""
### 元素流向分析说明
    
    **分析目的**：
    - 追踪特定元素在反应过程中的流向和分布变化
    - 识别元素的主要载体组分
    - 分析元素在不同组分间的转移规律
    
    **参数说明**：
    - **目标元素**：要追踪的化学元素（如C、H、O、N等）
    - **初始反应物**：反应的初始组分，作为元素流向分析的起点
    - **显示阈值**：过滤较小的元素分布，突出主要流向
    
    **数据解释**：
    - **元素分布图**：显示元素在不同组分中的分布随条件变化
    - **详细数据表**：各组分中元素分布的具体数值
    - **最大值突出**：自动突出显示元素含量最高的组分
    
    **应用建议**：
    - 关注元素分布较高的主要载体组分
    - 分析元素流向的变化趋势和转折点
    - 结合反应机理理解元素转移过程
    """)

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>🔄 元素流向分析 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)

