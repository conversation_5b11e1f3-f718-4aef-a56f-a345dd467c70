"""
字符串处理工具模块
"""
import re
from typing import List, <PERSON><PERSON>


def replace_scientific_notation(string: str) -> str:
    """处理科学计数法字符串格式"""
    if string.startswith('-'):
        return '-' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
    elif string.startswith('+'):
        return '+' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
    else:
        return string.replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')


def clean_reaction_equation(reaction: str) -> str:
    """清理反应方程式格式"""
    if '<=>' in reaction:
        return reaction.replace('+', ' + ').replace('<=>', ' <=> ').replace('( + M)', ' (+M)')
    else:
        return reaction.replace('+', ' + ').replace('=', ' <=> ').replace('<=> >', '<=> ').replace('( + M)', ' (+M)')


def extract_species_from_reaction(reaction_side: str) -> List[str]:
    """从反应方程式的一侧提取组分信息"""
    cleaned_side = re.sub(r'\(\+.*\)', '', reaction_side)
    species_list = cleaned_side.split('+')
    
    processed_species = []
    for species in species_list:
        species = species.strip()
        if species not in ['m', 'M', 'HV', 'H*'] and species:
            processed_species.append(species)
    
    return processed_species
def parse_stoichiometric_coefficient(species_string: str) -> Tuple[str, float]:
    """解析化学计量数"""
    species_string = species_string.strip()
    
    if species_string[0].isdigit() or species_string[0] == '.':
        stoich_match = re.search(r'^\d*\.?\d*', species_string)
        if stoich_match:
            stoich_str = stoich_match.group()
            stoich = float(stoich_str) if stoich_str else 1.0
            species = re.sub(r'^\d*\.?\d*', '', species_string)
        else:
            stoich = 1.0
            species = species_string
    else:
        stoich = 1.0
        species = species_string
    
    return species, stoich