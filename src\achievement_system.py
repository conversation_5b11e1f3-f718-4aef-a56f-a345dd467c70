"""
成就系统和持久化管理器
Achievement System and Persistence Manager

功能：
- 点击计数器持久化
- 启动次数统计
- 计算机变更检测
- 成就系统管理
"""

import os
import json
import hashlib
import platform
import uuid
from datetime import datetime
from pathlib import Path
import streamlit as st

class AchievementSystem:
    """成就系统管理器"""
    
    def __init__(self):
        self.config_dir = self._get_config_directory()
        self.config_file = self.config_dir / "rop_achievements.json"
        self.computer_id = self._get_computer_id()
        self._ensure_config_directory()
        
    def _get_config_directory(self):
        """获取配置文件目录"""
        if platform.system() == "Windows":
            config_dir = Path(os.environ.get('APPDATA', '')) / "ROP_Analysis_Tool"
        else:
            config_dir = Path.home() / ".rop_analysis_tool"
        return config_dir
    
    def _ensure_config_directory(self):
        """确保配置目录存在"""
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_computer_id(self):
        """生成计算机唯一标识符"""
        try:
            # 使用多种系统信息生成唯一ID
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
            }
            
            # 尝试获取MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                system_info['mac'] = mac
            except:
                pass
            
            # 生成哈希
            info_string = json.dumps(system_info, sort_keys=True)
            computer_id = hashlib.md5(info_string.encode()).hexdigest()[:16]
            return computer_id
            
        except Exception:
            # 如果获取失败，使用随机ID
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()[:16]
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 检查计算机ID是否匹配
                if config.get('computer_id') != self.computer_id:
                    # 计算机变更，重置数据
                    return self._create_default_config()
                    
                return config
            else:
                return self._create_default_config()
        except Exception:
            return self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        return {
            'computer_id': self.computer_id,
            'launch_count': 0,
            'click_count': 0,
            'easter_egg_unlocked': False,  # 经典彩蛋（100次点击）
            'ultimate_easter_egg_unlocked': False,  # 终极彩蛋（H2O序列）
            'achievements': [],
            'first_launch': datetime.now().isoformat(),
            'last_launch': datetime.now().isoformat(),
            'version': '6.1',
            # 摩尔斯序列相关
            'morse_sequence': [],
            'sequence_start_time': None,
            'current_page': None,
            'h2o_sequence_completed': False,
            'sequence_attempts': 0
        }
    
    def _save_config(self, config):
        """保存配置文件"""
        try:
            config['last_launch'] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            st.error(f"保存配置失败: {e}")
    
    def increment_launch_count(self):
        """增加启动次数"""
        config = self._load_config()
        config['launch_count'] += 1
        self._save_config(config)
        return config['launch_count']
    
    def get_launch_count(self):
        """获取启动次数"""
        config = self._load_config()
        return config.get('launch_count', 0)
    
    def increment_click_count(self):
        """增加点击次数"""
        config = self._load_config()
        config['click_count'] += 1
        
        # 检查是否达到100次点击
        if config['click_count'] >= 100 and not config.get('easter_egg_unlocked', False):
            config['easter_egg_unlocked'] = True
            self._add_achievement(config, "ultimate_easter_egg", "🎪 终极彩蛋大师", "累计点击100次解锁终极彩蛋！")
        
        self._save_config(config)
        return config['click_count']
    
    def get_click_count(self):
        """获取点击次数"""
        config = self._load_config()
        return config.get('click_count', 0)
    
    def is_easter_egg_unlocked(self):
        """检查终极彩蛋是否解锁"""
        config = self._load_config()
        return config.get('easter_egg_unlocked', False)
    
    def _add_achievement(self, config, achievement_id, title, description):
        """添加成就"""
        achievement = {
            'id': achievement_id,
            'title': title,
            'description': description,
            'unlocked_at': datetime.now().isoformat()
        }
        
        # 检查是否已存在
        existing_ids = [a.get('id') for a in config.get('achievements', [])]
        if achievement_id not in existing_ids:
            config.setdefault('achievements', []).append(achievement)
    
    def get_achievements(self):
        """获取所有成就"""
        config = self._load_config()
        return config.get('achievements', [])
    
    def add_launch_achievement(self):
        """添加启动相关成就"""
        config = self._load_config()
        launch_count = config.get('launch_count', 0)

        # 定义成就里程碑 - 按用户要求更新
        milestones = [
            (1, "first_launch", "🚀 初次启动", "欢迎使用ROP分析工具箱！"),
            (10, "reaction_worker", "🔧 反应动力学打工仔", "启动应用10次，踏上反应动力学的征程！"),
            (50, "reaction_novice", "⚗️ 反应动力学小佬", "启动应用50次，您已掌握基础反应动力学！"),
            (200, "reaction_expert", "🧪 反应动力学中佬", "启动应用200次，您是反应动力学的专家！"),
            (1000, "reaction_master", "🏆 反应动力学大佬", "启动应用1000次，您已达到反应动力学大师级别！"),
        ]

        for count, achievement_id, title, description in milestones:
            if launch_count >= count:
                self._add_achievement(config, achievement_id, title, description)

        # 检查是否达到终极彩蛋的基础条件
        if launch_count >= 42 and not config.get('ultimate_easter_egg_hint_shown', False):
            self._add_achievement(config, "mystery_hint", "🔍 神秘的线索", "💧 生命之源的秘密等待着被发现... (在关于页面寻找线索)")
            config['ultimate_easter_egg_hint_shown'] = True

        self._save_config(config)
    
    def reset_data(self):
        """重置所有数据"""
        config = self._create_default_config()
        self._save_config(config)
        return True
    
    def add_morse_signal(self, signal_type, current_page):
        """添加摩尔斯信号到序列"""
        config = self._load_config()

        # 检查基础条件
        if config.get('launch_count', 0) < 42:
            return False

        # 检查是否在关于页面
        if current_page != 'ℹ️ 关于':
            self._reset_morse_sequence(config)
            return False

        current_time = datetime.now()

        # 如果是新序列的开始
        if not config.get('morse_sequence'):
            config['sequence_start_time'] = current_time.isoformat()
            config['morse_sequence'] = []

        # 检查时间窗口（60秒）
        start_time = datetime.fromisoformat(config['sequence_start_time'])
        if (current_time - start_time).total_seconds() > 60:
            self._reset_morse_sequence(config)
            config['sequence_start_time'] = current_time.isoformat()
            config['morse_sequence'] = []

        # 添加信号到序列
        signal = '.' if signal_type == 'balloon' else '-'
        config['morse_sequence'].append(signal)
        config['sequence_attempts'] = config.get('sequence_attempts', 0) + 1

        # 检查H2O序列：H(....)+2(..---)+O(---)
        target_sequence = ['.','.','.','.','.','.','-','-','-','-','-','-']
        current_sequence = config['morse_sequence']

        # 检查是否完成序列
        if len(current_sequence) == len(target_sequence):
            if current_sequence == target_sequence:
                config['h2o_sequence_completed'] = True
                config['ultimate_easter_egg_unlocked'] = True
                self._add_achievement(config, "ultimate_chemist", "🧬 终极化学大师", "🎉 恭喜！您发现了H2O的秘密！您是真正的化学大师！")
                self._reset_morse_sequence(config)
                self._save_config(config)
                return True
        elif len(current_sequence) > len(target_sequence):
            # 序列太长，重置
            self._reset_morse_sequence(config)

        self._save_config(config)
        return False

    def _reset_morse_sequence(self, config):
        """重置摩尔斯序列"""
        config['morse_sequence'] = []
        config['sequence_start_time'] = None

    def is_ultimate_easter_egg_unlocked(self):
        """检查终极彩蛋是否解锁"""
        config = self._load_config()
        return config.get('ultimate_easter_egg_unlocked', False)

    def get_morse_progress(self):
        """获取摩尔斯序列进度"""
        config = self._load_config()
        target_sequence = ['.','.','.','.','.','.','.','-','-','-','-','-','-']
        current_sequence = config.get('morse_sequence', [])

        return {
            'current_length': len(current_sequence),
            'target_length': len(target_sequence),
            'current_sequence': ''.join(current_sequence),
            'target_sequence': ''.join(target_sequence),
            'progress_percent': min(len(current_sequence) / len(target_sequence) * 100, 100),
            'time_remaining': self._get_time_remaining(config)
        }

    def _get_time_remaining(self, config):
        """获取序列剩余时间"""
        if not config.get('sequence_start_time'):
            return 60

        start_time = datetime.fromisoformat(config['sequence_start_time'])
        elapsed = (datetime.now() - start_time).total_seconds()
        return max(0, 60 - elapsed)

    def get_system_info(self):
        """获取系统信息"""
        config = self._load_config()
        return {
            'computer_id': self.computer_id[:8] + "...",  # 只显示部分ID
            'launch_count': config.get('launch_count', 0),
            'click_count': config.get('click_count', 0),
            'first_launch': config.get('first_launch', 'Unknown'),
            'last_launch': config.get('last_launch', 'Unknown'),
            'achievements_count': len(config.get('achievements', [])),
            'easter_egg_unlocked': config.get('easter_egg_unlocked', False),
            'ultimate_easter_egg_unlocked': config.get('ultimate_easter_egg_unlocked', False),
            'sequence_attempts': config.get('sequence_attempts', 0)
        }

# 全局实例
achievement_system = AchievementSystem()
