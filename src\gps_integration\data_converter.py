#!/usr/bin/env python3
"""
GPS数据格式转换器
将当前项目的数据格式转换为GPS兼容格式
"""

import numpy as np
import pandas as pd
import re
from typing import Dict, List, Tuple, Any


class GPSDataConverter:
    """GPS数据格式转换器"""
    
    def __init__(self):
        self.species_elemental_composition = {}
        self.reaction_stoichiometry = []
        self.species_molecular_weights = {}
    
    def extract_elemental_composition_from_gas_obj(self, gas_obj) -> Dict[str, Dict[str, int]]:
        """从gas对象中提取组分的元素组成（使用现有的species_element属性）"""
        print("🔍 从gas对象提取元素组成...")

        if not hasattr(gas_obj, 'species_element'):
            raise ValueError("gas对象缺少species_element属性")

        species_element_df = gas_obj.species_element
        species_composition = {}

        # 转换DataFrame为字典格式
        for species in species_element_df.index:
            composition = {}
            for element in species_element_df.columns:
                count = species_element_df.loc[species, element]
                if count > 0:
                    composition[element] = int(count)

            if composition:  # 只保存有元素的组分
                species_composition[species] = composition

        print(f"   提取了 {len(species_composition)} 个组分的元素组成")
        print(f"   元素类型: {list(species_element_df.columns)}")

        self.species_elemental_composition = species_composition
        return species_composition
    
    def extract_molecular_weights_from_gas_obj(self, gas_obj) -> Dict[str, float]:
        """从gas对象中提取分子量（使用现有的species_MW属性）"""
        print("🔍 从gas对象提取分子量...")

        if not hasattr(gas_obj, 'species_MW'):
            raise ValueError("gas对象缺少species_MW属性")

        species_mw_df = gas_obj.species_MW
        molecular_weights = {}

        # 转换DataFrame为字典格式
        for species in species_mw_df.columns:
            mw = species_mw_df.loc['MW', species]
            molecular_weights[species] = float(mw)

        print(f"   提取了 {len(molecular_weights)} 个组分的分子量")
        self.species_molecular_weights = molecular_weights
        return molecular_weights
    
    def convert_stoichiometry_to_gps_format(self, 
                                          stoichiometric_matrix: pd.DataFrame,
                                          reaction_index: pd.DataFrame,
                                          species_names: List[str]) -> List[Dict]:
        """将化学计量系数矩阵转换为GPS格式的反应化学计量"""
        print("🔄 转换反应化学计量数据...")
        
        reaction_stoichiometry = []
        
        for i, (rxn_idx, rxn_row) in enumerate(stoichiometric_matrix.iterrows()):
            reactants = {}
            products = {}
            
            for j, species in enumerate(species_names):
                coeff = rxn_row.iloc[j] if j < len(rxn_row) else 0
                
                if coeff < 0:  # 反应物
                    reactants[species] = abs(coeff)
                elif coeff > 0:  # 产物
                    products[species] = coeff
            
            # 只保存有反应物和产物的反应
            if reactants or products:
                reaction_stoichiometry.append({
                    'reactants': reactants,
                    'products': products,
                    'reaction_id': i,
                    'reaction_name': reaction_index.iloc[i, 0] if i < len(reaction_index) else f"Reaction_{i+1}"
                })
        
        print(f"   转换了 {len(reaction_stoichiometry)} 个反应")
        self.reaction_stoichiometry = reaction_stoichiometry
        return reaction_stoichiometry
    
    def prepare_gps_input_data(self,
                              gas_obj,
                              condition_key: str = None,
                              use_end_point: bool = False,
                              fuel_species: List[str] = None) -> Dict[str, Any]:
        """准备GPS算法所需的输入数据"""
        print("📦 准备GPS输入数据...")
        
        # 提取元素组成（如果还没有）
        if not self.species_elemental_composition:
            self.extract_elemental_composition_from_gas_obj(gas_obj)
            self.extract_molecular_weights_from_gas_obj(gas_obj)
        
        # 转换反应化学计量（如果还没有）
        if not self.reaction_stoichiometry and hasattr(gas_obj, 'stoichimetric'):
            self.convert_stoichiometry_to_gps_format(
                gas_obj.stoichimetric,
                gas_obj.reaction_index,
                gas_obj.species
            )
        
        # 选择反应速率数据
        if use_end_point and hasattr(gas_obj, 'end_ROP'):
            rop_data = gas_obj.end_ROP
            data_type = "end_point"
        elif hasattr(gas_obj, 'integral_ROP'):
            rop_data = gas_obj.integral_ROP
            data_type = "integral"
        else:
            raise ValueError("未找到反应速率数据 (integral_ROP 或 end_ROP)")
        
        # 选择条件
        if condition_key is None:
            condition_key = list(rop_data.keys())[0]  # 使用第一个条件
        
        if condition_key not in rop_data:
            raise ValueError(f"条件 {condition_key} 不存在于反应速率数据中")
        
        # 获取该条件下的反应速率
        rop_matrix = rop_data[condition_key]  # 形状: (n_reactions, n_species)
        
        # 计算净反应速率（每个反应的总速率）
        net_reaction_rates = []
        for i in range(len(self.reaction_stoichiometry)):
            if i < rop_matrix.shape[0]:
                # 使用反应对所有组分的ROP的平均值作为净反应速率
                rate = np.mean(np.abs(rop_matrix.iloc[i, :]))
                net_reaction_rates.append(rate)
            else:
                net_reaction_rates.append(0.0)
        
        # 获取组分浓度
        if hasattr(gas_obj, 'mole_fractions'):
            # 查找对应条件的摩尔分数
            mole_fractions_df = gas_obj.mole_fractions
            if condition_key in mole_fractions_df.index:
                species_concentrations = mole_fractions_df.loc[condition_key].tolist()
            else:
                # 使用第一行作为默认值
                species_concentrations = mole_fractions_df.iloc[0].tolist()
        else:
            # 使用默认值
            species_concentrations = [0.1] * len(gas_obj.species)
        
        # 准备燃料组成
        if fuel_species is None:
            # 自动检测含碳组分作为燃料
            fuel_species = []
            for species in gas_obj.species:
                if species in self.species_elemental_composition:
                    if 'C' in self.species_elemental_composition[species]:
                        fuel_species.append(species)
        
        fuel_composition = {}
        for i, species in enumerate(gas_obj.species):
            if species in fuel_species:
                fuel_composition[species] = species_concentrations[i]
        
        # 估算反应焓（简化处理）
        reaction_enthalpies = [100.0] * len(self.reaction_stoichiometry)  # 使用固定值
        
        gps_data = {
            'species_names': gas_obj.species,
            'species_elemental_composition': self.species_elemental_composition,
            'species_molecular_weights': self.species_molecular_weights,
            'reaction_stoichiometry': self.reaction_stoichiometry,
            'net_reaction_rates': net_reaction_rates,
            'species_concentrations': species_concentrations,
            'reaction_enthalpies': reaction_enthalpies,
            'fuel_composition': fuel_composition,
            'condition_info': {
                'condition_key': condition_key,
                'data_type': data_type,
                'n_species': len(gas_obj.species),
                'n_reactions': len(self.reaction_stoichiometry)
            }
        }
        
        print(f"   ✅ GPS数据准备完成:")
        print(f"      组分数量: {len(gps_data['species_names'])}")
        print(f"      反应数量: {len(gps_data['reaction_stoichiometry'])}")
        print(f"      燃料组分: {list(fuel_composition.keys())}")
        print(f"      条件: {condition_key} ({data_type})")
        
        return gps_data
    
    def validate_gps_data(self, gps_data: Dict[str, Any]) -> bool:
        """验证GPS数据的完整性"""
        print("✅ 验证GPS数据完整性...")
        
        required_keys = [
            'species_names', 'species_elemental_composition', 'reaction_stoichiometry',
            'net_reaction_rates', 'species_concentrations', 'fuel_composition'
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in gps_data or not gps_data[key]:
                missing_keys.append(key)
        
        if missing_keys:
            print(f"   ❌ 缺失数据: {missing_keys}")
            return False
        
        # 检查数据一致性
        n_species = len(gps_data['species_names'])
        n_reactions = len(gps_data['reaction_stoichiometry'])
        
        if len(gps_data['species_concentrations']) != n_species:
            print(f"   ❌ 组分浓度数量不匹配: {len(gps_data['species_concentrations'])} vs {n_species}")
            return False
        
        if len(gps_data['net_reaction_rates']) != n_reactions:
            print(f"   ❌ 反应速率数量不匹配: {len(gps_data['net_reaction_rates'])} vs {n_reactions}")
            return False
        
        print("   ✅ GPS数据验证通过")
        return True
