import streamlit as st
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

import numpy as np
import pandas as pd
import io

def apply_value_based_color_mapping_multi(styler, exclude_rows=None):
    """
    为多点ROP表格应用基于数值的颜色映射
    exclude_rows: 不参与颜色映射的行名列表
    """
    def color_mapping(val):
        """为单个值应用颜色"""
        try:
            numeric_val = pd.to_numeric(val, errors='coerce')
        except:
            return ''
        
        if pd.isna(numeric_val):
            return ''
        
        # 使用更严格的零值检测，考虑浮点数精度问题
        if abs(numeric_val) < 1e-10:  # 非常接近零的值都视为零
            # 零值使用中性灰色
            return 'background-color: rgba(200, 200, 200, 0.3); color: black;'
        elif numeric_val > 0:
            # 正值使用暖色系(红色系) - 促进/生成
            return 'background-color: rgba(255, 150, 150, 0.7); color: black;'
        else:  # numeric_val < 0
            # 负值使用冷色系(蓝色系) - 抑制/消耗
            return 'background-color: rgba(150, 150, 255, 0.7); color: black;'
    
    try:
        # 获取DataFrame
        df = styler.data
        
        # 确定需要应用颜色映射的行
        if exclude_rows:
            target_rows = [idx for idx in df.index if idx not in exclude_rows]
        else:
            target_rows = df.index.tolist()
        
        if target_rows:
            # 只对指定行应用颜色映射
            return styler.applymap(color_mapping, subset=pd.IndexSlice[target_rows, :])
        else:
            return styler
            
    except Exception as e:
        st.warning(f"颜色映射应用失败: {e}")
        return styler



# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="多点ROP分析", page_icon="📈")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .error-card {
        background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--warning-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .success-card {
        background: linear-gradient(135deg, rgba(44, 160, 44, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--success-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 标签页样式优化 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: var(--surface-color);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 4px var(--shadow-light);
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
    }
    
    /* 输入控件样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 选择框内容区域 */
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 选择框文本 */
    .stSelectbox > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 选择框选项样式 */
    .stSelectbox option {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 下拉选项 */
    .stSelectbox [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 下拉选项悬停效果 */
    .stSelectbox [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 下拉菜单容器 */
    .stSelectbox [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 选择框显示文本 */
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保下拉箭头可见 */
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 数据表格样式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        border-radius: 4px;
    }
    
    /* 增强下载按钮样式 */
    .stDownloadButton > button {
        background: linear-gradient(90deg, #2E86AB, #A23B72) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.6rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        box-shadow: 0 3px 6px rgba(0,0,0,0.2) !important;
        transition: all 0.3s ease !important;
        text-transform: none !important;
    }
    
    .stDownloadButton > button:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 5px 12px rgba(0,0,0,0.3) !important;
        background: linear-gradient(90deg, #236B87, #8B2F5A) !important;
    }
    
    .stDownloadButton > button:active {
        transform: translateY(0px) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">📈 多点ROP分析</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        多工况点反应路径分析 - 研究不同条件下反应贡献的变化趋势
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if ('a' and 'b') not in st.session_state:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行多点ROP分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

elif not st.session_state['b'].net_reaction_rate_exist and not st.session_state['b'].ROP_exist:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少反应速率数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有反应速率输出，无法进行ROP分析。请检查CHEMKIN输出设置。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ 分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        ROP_type = st.radio(
            '**分析类型**', 
            ['积分ROP', 'EndPointROP'], 
            captions=[
                '将ROP沿时间或距离积分，适合流动管或火焰',
                '取稳态最后一点ROP，适合JSR'
            ],
            help="选择合适的ROP分析类型"
        )
    
    with col2:
        selected_species_multi = st.selectbox(
            "**目标组分**：", 
            options=st.session_state['a'].rop_species_output, 
            key='m1',
            help="选择要分析的目标组分"
        )
    
    with col3:
        threshold = st.number_input(
            '**相对值显示阈值**', 
            min_value=0.0, 
            max_value=1.0, 
            value=0.1,
            format="%.3f",
            help="设置显示阈值，过滤较小的贡献值"
        )
    
    # 检查是否需要选择横坐标变量（没有end_point sheets的情况）
    if hasattr(st.session_state['b'], 'need_extract_endpoints') and st.session_state['b'].need_extract_endpoints:
        st.markdown("---")
        st.markdown("##### 🎯 横坐标变量选择")
        st.info("检测到没有end_point数据表，请选择横坐标变量和数据点类型：")
        
        col1_axis, col2_axis = st.columns(2)
        
        with col1_axis:
            # 获取可用的横坐标变量
            x_vars_last = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'last')
            x_vars_first = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'first')
            
            # 合并两种数据点的变量选项
            all_x_vars = set(x_vars_last.keys()) | set(x_vars_first.keys())
            
            if all_x_vars:
                selected_x_var = st.selectbox(
                    "**横坐标变量**：",
                    options=list(all_x_vars),
                    help="选择用作横坐标的变量"
                )
            else:
                st.error("未找到可用的横坐标变量")
                selected_x_var = None
        
        with col2_axis:
            point_type = st.radio(
                "**数据点类型**：",
                options=['last', 'first'],
                format_func=lambda x: '最后一点' if x == 'last' else '第一点',
                help="选择使用每个工况的第一点还是最后一点数据"
            )
        
        # 更新横坐标变量设置
        if selected_x_var and point_type:
            x_vars_data = x_vars_last if point_type == 'last' else x_vars_first
            if selected_x_var in x_vars_data:
                # 临时更新variable信息用于绘图
                st.session_state['a'].temp_variable_name = selected_x_var
                st.session_state['a'].temp_variable_unit = ''
                st.session_state['a'].temp_variables = x_vars_data[selected_x_var]
                
                # 显示选择的横坐标数据
                st.success(f"已选择：{selected_x_var} ({point_type}点)")
                st.text(f"数据值: {x_vars_data[selected_x_var]}")
            else:
                st.error(f"所选变量 {selected_x_var} 在 {point_type} 点数据中不可用")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 分析计算和结果显示（自动分析）
# ========================================

with st.spinner('正在进行多点ROP分析，请稍候...'):
    # 处理数据
    if ROP_type == '积分ROP':
        st.session_state['a'].process_ROP_multi(
            st.session_state['b'], 
            selected_species_multi, 
            threshold, 
            datatype='integral'
        )
    else:
        st.session_state['a'].process_ROP_multi(
            st.session_state['b'], 
            selected_species_multi, 
            threshold, 
            datatype='end'
        )
    
    # 显示成功消息
    st.markdown("""
<div class="success-card">
        <h3 style="color: var(--success-color); margin: 0;">✅ 分析完成</h3>
        <p style="margin: 0.5rem 0 0 0;">
            多点ROP分析已完成，结果如下所示。
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # 生成图表
    fig_multi_rop = st.session_state['a'].plot_ROP_multi(st.session_state['b'])
    
    # 显示图表
    st.markdown("### 📈 多点ROP趋势图")
    st.plotly_chart(fig_multi_rop, use_container_width=True)
    
    # 显示数据表格
    st.markdown("### 📊 多点ROP详细数据")
    
    tabm1, tabm2 = st.tabs(['📊 相对值', '📋 绝对值'])
    
    with tabm1:
        st.markdown("#### 相对值数据")
        
        # 添加颜色说明
        st.markdown("""
        <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(240, 248, 255, 0.5); border-radius: 5px;">
            <small><strong>颜色说明：</strong> 
            <span style="background-color: rgba(255, 150, 150, 0.7); padding: 2px 6px; border-radius: 3px;">红色 = 促进/生成</span>
            <span style="background-color: rgba(150, 150, 255, 0.7); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">蓝色 = 抑制/消耗</span>
            <span style="background-color: rgba(200, 200, 200, 0.3); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">灰色 = 零值</span>
            <br><small style="color: #666; margin-top: 5px; display: block;">注：Production, Consumption, Net_production行不参与颜色映射</small>
            </small>
        </div>
        """, unsafe_allow_html=True)
        
        # 使用数值型DataFrame进行美化和高亮
        df_percent = st.session_state['a'].ROP_percent2.T.copy()
        sci_rows = ['Production', 'Consumption', 'Net_production']
        
        try:
            styled_percent = (
                df_percent.style
                .set_table_styles([
                    {'selector': 'th', 'props': [
                        ('font-weight', 'bold'), 
                        ('text-align', 'center'), 
                        ('background-color', '#eaf3fa'),
                        ('color', 'black')
                    ]},
                    {'selector': 'td', 'props': [
                        ('text-align', 'center'),
                        ('padding', '8px')
                    ]},
                    {'selector': 'table', 'props': [
                        ('border-collapse', 'collapse'),
                        ('border', '1px solid #ddd')
                    ]}
                ])
                .format('{:.2e}')
            )
            
            # 应用颜色映射，排除前三行
            styled_percent = apply_value_based_color_mapping_multi(styled_percent, exclude_rows=sci_rows)
            
            # 对非前三行的部分单独格式化为百分比
            for idx in df_percent.index:
                if idx not in sci_rows:
                    styled_percent = styled_percent.format('{:.2%}', subset=pd.IndexSlice[idx, :])
            
            # 高亮最大值（只对反应行）
            reaction_rows = [idx for idx in df_percent.index if idx not in sci_rows]
            if reaction_rows:
                styled_percent = styled_percent.highlight_max(
                    subset=pd.IndexSlice[reaction_rows, :], 
                    axis=0, 
                    color='rgba(255, 215, 0, 0.6)'
                )
            
            st.dataframe(styled_percent, use_container_width=True)
            
        except Exception as e:
            st.error(f"表格样式应用失败: {str(e)}")
            st.dataframe(df_percent, use_container_width=True)
        
        # 增强的导出按钮
        csv = df_percent.to_csv().encode('utf-8-sig')
        st.download_button(
            "📥 下载相对值数据 (CSV)", 
            csv, 
            file_name="ROP_percent_multi.csv", 
            mime="text/csv",
            help="下载相对值数据到CSV文件"
        )
    
    with tabm2:
        st.markdown("#### 绝对值数据")
        
        # 添加颜色说明
        st.markdown("""
        <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(240, 248, 255, 0.5); border-radius: 5px;">
            <small><strong>颜色说明：</strong> 
            <span style="background-color: rgba(255, 150, 150, 0.7); padding: 2px 6px; border-radius: 3px;">红色 = 正值(促进)</span>
            <span style="background-color: rgba(150, 150, 255, 0.7); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">蓝色 = 负值(抑制)</span>
            <span style="background-color: rgba(200, 200, 200, 0.3); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">灰色 = 零值</span>
            <br><small style="color: #666; margin-top: 5px; display: block;">注：Production, Consumption, Net_production行不参与颜色映射</small>
            </small>
        </div>
        """, unsafe_allow_html=True)
        
        df_abs = st.session_state['a'].species_rop_display.T.copy()
        sci_rows_abs = ['Production', 'Consumption', 'Net_production']
        
        try:
            # 尝试将所有值转为float，无法转的填为NaN
            df_abs_numeric = df_abs.applymap(lambda x: pd.to_numeric(x, errors='coerce'))
            
            styled_abs = (
                df_abs_numeric.style
                .set_table_styles([
                    {'selector': 'th', 'props': [
                        ('font-weight', 'bold'), 
                        ('text-align', 'center'), 
                        ('background-color', '#eaf3fa'),
                        ('color', 'black')
                    ]},
                    {'selector': 'td', 'props': [
                        ('text-align', 'center'),
                        ('padding', '8px')
                    ]},
                    {'selector': 'table', 'props': [
                        ('border-collapse', 'collapse'),
                        ('border', '1px solid #ddd')
                    ]}
                ])
                .format('{:.2e}')
            )
            
            # 应用颜色映射，排除前三行
            styled_abs = apply_value_based_color_mapping_multi(styled_abs, exclude_rows=sci_rows_abs)
            
            # 高亮最大值（只对反应行）
            reaction_rows_abs = [idx for idx in df_abs_numeric.index if idx not in sci_rows_abs]
            if reaction_rows_abs:
                styled_abs = styled_abs.highlight_max(
                    subset=pd.IndexSlice[reaction_rows_abs, :], 
                    axis=0, 
                    color='rgba(255, 215, 0, 0.6)'
                )
            
            st.dataframe(styled_abs, use_container_width=True)
            
        except Exception as e:
            st.error(f"表格样式应用失败: {str(e)}")
            st.dataframe(df_abs, use_container_width=True)
        
        # 增强的导出按钮
        csv2 = df_abs.to_csv().encode('utf-8-sig')
        st.download_button(
            "📥 下载绝对值数据 (CSV)", 
            csv2, 
            file_name="ROP_absolute_multi.csv", 
            mime="text/csv",
            help="下载绝对值数据到CSV文件"
        )

# ========================================
# 分析说明
# ========================================

with st.expander("ℹ️ 分析说明", expanded=False):
    st.markdown("""
### 多点ROP分析说明
    
    **分析目的**：
    - 研究不同工况条件下反应对目标组分生成贡献的变化趋势
    - 识别在不同条件下的主导反应路径
    - 分析反应重要性的变化规律
    
    **数据解释**：
    - **相对值**：每个反应的贡献占总贡献的百分比
    - **绝对值**：每个反应的实际贡献数值
    - **趋势图**：显示各反应贡献随工况变化的趋势
    
    **应用建议**：
    - 关注相对值较高且变化明显的反应
    - 分析主导反应在不同条件下的转变
    - 结合具体工况条件理解反应机理变化
    """)

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>📈 多点ROP分析 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)