"""
热力学文件处理器
"""
import os
from typing import Dict, List, Optional, Union, Any
import pandas as pd
import numpy as np
import cantera as ct
import streamlit as st
from scipy.optimize import curve_fit
from lmfit import Model

from .exceptions import ThermoFileError
from ..utils.data_processing import validate_temperature_range


class ThermoProcessor:
    """热力学文件处理器"""
    
    def __init__(self, input_file: str):
        """初始化热力学处理器"""
        self.input_file = input_file
        self.ct_species = None
        self.ct_gas = None
        self.reverse_rate_constants = None
        self.Arr_fitted = None
        self._load_thermo_file()
    
    def _load_thermo_file(self):
        """加载热力学文件"""
        if not os.path.exists(self.input_file):
            st.error('文件不存在！')
            raise ThermoFileError(f"热力学文件不存在: {self.input_file}")
        
        print(f"正在加载热力学文件: {self.input_file}")
        result = os.system(f'python -m cantera.ck2yaml --thermo="{self.input_file}" --permissive --output=thermo.yaml')
        
        if result == 0:
            try:
                self.ct_species = ct.Species.list_from_file('thermo.yaml')
                st.success('热力学文件导入成功！')
                print('热力学文件导入成功！')
            except Exception as e:
                st.error(f'热力学文件解析失败：{str(e)}')
                raise ThermoFileError(f"热力学文件解析失败: {str(e)}")
        else:
            st.error('热力学文件导入失败，请检查文件格式！')
            raise ThermoFileError("热力学文件格式错误")
    
    def calculate_reverse_rate(self, gas_processor, selected_reaction: str, 
                             temp_min: float = 300, temp_max: float = 2000) -> Optional[pd.DataFrame]:
        """计算反向反应速率常数"""
        if selected_reaction not in gas_processor.reactions:
            st.error(f"反应 '{selected_reaction}' 不存在于反应列表中")
            return None
        
        reaction_index = gas_processor.reactions.index(selected_reaction)
        
        try:
            # 创建Cantera反应对象
            reaction = ct.Reaction(
                equation=gas_processor.cantera_reactions[reaction_index], 
                rate=gas_processor.Arr_SI_unit[reaction_index]
            )
            
            if not reaction.reversible:
                st.error('该反应不可逆！')
                return None
            
            # 创建气体对象
            self.ct_gas = ct.Solution(
                thermo='ideal-gas', 
                species=self.ct_species, 
                kinetics='gas',
                reactions=[reaction]
            )
            
            # 计算不同温度下的速率常数
            self.reverse_rate_constants = pd.DataFrame()
            reaction_reverse_name = f'{reaction.product_string} <=> {reaction.reactant_string}'
            
            temp_range = np.arange(temp_min, temp_max + 10, 10)
            for T in temp_range:
                self.ct_gas.TP = T, ct.one_atm
                
                # 计算反向速率常数
                reverse_rate = self.ct_gas.reverse_rate_constants[0] / (1000**(len(reaction.products)-1))
                forward_rate = self.ct_gas.forward_rate_constants[0] / (1000**(len(reaction.reactants)-1))
                eq_constant = self.ct_gas.equilibrium_constants[0] * (1000**(len(reaction.reactants)-len(reaction.products)))
                
                self.reverse_rate_constants.loc[T, reaction_reverse_name] = reverse_rate
                self.reverse_rate_constants.loc[T, 'forward'] = forward_rate
                self.reverse_rate_constants.loc[T, 'EQ_constant'] = eq_constant
            
            return self.reverse_rate_constants
            
        except ValueError as e:
            st.error(f'错误！热力学文件中找不到组分信息: {str(e)}')
            return None
        except Exception as e:
            st.error(f'计算反向速率时发生错误: {str(e)}')
            return None
    
    def fit_arrhenius_parameters(self, T_low: float, T_high: float) -> Optional[pd.DataFrame]:
        """拟合Arrhenius参数"""
        if self.reverse_rate_constants is None:
            st.error("请先计算反向速率常数")
            return None
        
        if not validate_temperature_range(T_low, T_high):
            st.error("温度范围无效")
            return None
        
        try:
            # 定义Arrhenius方程
            def arrhenius_equation(T, A, n, Ea):
                return np.log(A) + n * np.log(T) - Ea / (T * 8.314 / 4.184)
            
            # 提取拟合数据
            fitting_data = self.reverse_rate_constants.iloc[:, 0].loc[float(T_low):float(T_high)]
            if len(fitting_data) == 0:
                st.error("在指定温度范围内没有数据点")
                return None
            
            ln_rc_fit = np.array(np.log(fitting_data))
            T_fit = np.array(fitting_data.index)
            
            print(f"拟合数据点数: {len(T_fit)}")
            print(f"温度范围: {T_fit.min():.1f} - {T_fit.max():.1f} K")
            
            # 使用LMFIT进行拟合
            model = Model(arrhenius_equation)
            params = model.make_params(A=10000, n=1, Ea=5000)
            
            # 设置参数边界
            params['A'].min = 1e-10
            params['A'].max = 1e20
            params['n'].min = -5
            params['n'].max = 5
            params['Ea'].min = -50000
            params['Ea'].max = 100000
            
            result = model.fit(ln_rc_fit, params, T=T_fit)
            
            # 保存拟合结果
            self.Arr_fitted = {
                'A': result.params['A'].value,
                'b': result.params['n'].value,
                'Ea': result.params['Ea'].value,
                'fit_quality': result.rsquared if hasattr(result, 'rsquared') else None
            }
            
            # 构造结果DataFrame
            data_fit_origin = pd.DataFrame(index=T_fit, columns=['original', 'fitted'])
            data_fit_origin.loc[:, 'original'] = fitting_data
            data_fit_origin.loc[:, 'fitted'] = np.exp(
                np.log(result.params['A'].value) +
                result.params['n'].value * np.log(T_fit) -
                result.params['Ea'].value / (T_fit * 8.314 / 4.184)
            )
            
            # 显示拟合结果
            st.success("Arrhenius参数拟合成功！")
            st.write(f"A = {self.Arr_fitted['A']:.3e}")
            st.write(f"n = {self.Arr_fitted['b']:.3f}")
            st.write(f"Ea = {self.Arr_fitted['Ea']:.1f} cal/mol")
            
            return data_fit_origin
            
        except Exception as e:
            st.error(f"拟合失败：{str(e)}")
            print(f"拟合错误详情: {e}")
            return None
    
    def get_fitted_parameters(self) -> Optional[Dict[str, float]]:
        """获取拟合的Arrhenius参数"""
        return self.Arr_fitted
    
    def validate_species(self, species_list: List[str]) -> List[str]:
        """验证组分是否存在于热力学数据库中"""
        if self.ct_species is None:
            return []
        
        available_species = [species.name for species in self.ct_species]
        missing_species = [species for species in species_list if species not in available_species]
        
        if missing_species:
            st.warning(f"以下组分在热力学文件中不存在: {missing_species}")
        
        return [species for species in species_list if species in available_species] 