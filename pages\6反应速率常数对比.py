import streamlit as st
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

import numpy as np
import pandas as pd
import sys
import os

# 添加新模块路径（Windows兼容）
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# 导入重构后的模块
from analyzers.rate_constant_analyzer import RateConstantAnalyzer, ArrheniusParameterProcessor
from core.exceptions import ChemkinProcessingError

# 保持对legacy模块的向后兼容
import legacy.solution0606 as sl

# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="反应速率常数对比", page_icon="⚗️")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .error-card {
        background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--warning-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .info-card {
        background: linear-gradient(135deg, rgba(23, 190, 207, 0.1), rgba(31, 119, 180, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--info-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .warning-card {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--secondary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 标签页样式优化 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: var(--surface-color);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 4px var(--shadow-light);
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
    }
    
    /* 输入控件样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 选择框内容区域 */
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 选择框文本 */
    .stSelectbox > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 选择框选项样式 */
    .stSelectbox option {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 下拉选项 */
    .stSelectbox [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 下拉选项悬停效果 */
    .stSelectbox [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 下拉菜单容器 */
    .stSelectbox [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 选择框显示文本 */
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保下拉箭头可见 */
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stTextInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    .stTextArea > div > div > textarea {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 数据表格样式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">⚗️ 反应速率常数对比</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        阿仑尼乌斯参数分析 - 对比不同反应的速率常数和温度依赖性
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if 'a' not in st.session_state:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成前处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理步骤后再进行反应速率常数对比分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 反应选择和基本参数设置
# ========================================

with st.expander("🎛️ 反应选择与基本参数", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        reaction_selected = st.selectbox(
            "**请选择反应**", 
            [None] + st.session_state['a'].reactions,
            help="选择要分析的反应"
        )
    
    with col2:
        reaction_direction = st.radio(
            '**反应方向**', 
            ['正反应', '逆反应'],
            help="选择分析正反应还是逆反应"
        )
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 阿仑尼乌斯参数显示
# ========================================

A_parameters = pd.DataFrame(columns=['A(cm3 mol-1 s-1)', 'n', 'Ea (cal/mol)'])

st.markdown("### 📊 阿仑尼乌斯参数")
placeholder1 = st.empty()
placeholder2 = st.empty()

# ========================================
# 自定义反应速率输入
# ========================================

with st.expander("✏️ 自定义反应速率输入", expanded=False):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    values_rc = st.text_area(
        "**其他反应速率(支持plog)**", 
        placeholder="反应方程(可选不填写) A  n  Ea !注释",
        help="输入格式：反应方程(可选) A n Ea !注释",
        height=100
    )
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 温度范围设置
# ========================================

with st.expander("🌡️ 温度范围设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        step = st.number_input(
            '**显示温度步进**', 
            min_value=10, 
            max_value=1000, 
            value=300,
            step=100,
            help="温度步进间隔"
        )
    
    with col2:
        temp_min, temp_max = st.select_slider(
            '**温度范围 (K)**',
            options=np.arange(300, 3000, 100),
            value=(300, 1000),
            help="选择分析的温度范围"
        )
    
    with col3:
        temp_range = np.arange(temp_min, temp_max, step)
        st.info(f"分析点数: {len(temp_range)} 个温度点")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 逆反应特殊参数
# ========================================

if reaction_direction == '逆反应':
    with st.expander("🧪 热力学文件设置", expanded=True):
        st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
        
        thermofile_path = st.text_input(
            '**热力学文件路径**',
            help="输入热力学文件的完整路径"
        )
        
        if st.button("📂 导入热力学文件"):
            if thermofile_path:
                try:
                    ctfile = sl.ct_file(thermofile_path)
                    st.session_state['c'] = ctfile
                    st.success(f"✅ 热力学文件导入成功: {thermofile_path}")
                except Exception as e:
                    st.error(f"❌ 热力学文件导入失败: {str(e)}")
            else:
                st.warning("⚠️ 请先输入热力学文件路径")
        
        st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 分析计算和结果显示
# ========================================

# 自动进行分析计算
if reaction_selected is not None or values_rc.strip():
    with st.spinner('正在计算反应速率常数...'):
        try:
            if reaction_direction == '正反应':
                # 正反应分析 - 使用重构的分析器
                if reaction_selected is not None:
                    try:
                        reaction_index = st.session_state['a'].reactions.index(reaction_selected)
                        
                        # 使用重构的RateConstantAnalyzer计算正反应速率
                        rc = RateConstantAnalyzer.calculate_forward_rate(
                            reaction_selected, 
                            st.session_state['a'].Arr_all[reaction_index],
                            temp_min, temp_max
                        )
                        
                        # 显示阿仑尼乌斯参数
                        A_parameters.loc['Parameter', 'A(cm3 mol-1 s-1)'] = st.session_state['a'].Arr_all[reaction_index]['A']
                        A_parameters.loc['Parameter', 'n'] = st.session_state['a'].Arr_all[reaction_index]['b']
                        A_parameters.loc['Parameter', 'Ea (cal/mol)'] = st.session_state['a'].Arr_all[reaction_index]['Ea']
                        
                        formatted_df2 = A_parameters.style.format({
                            'A(cm3 mol-1 s-1)': '{:.3e}', 
                            'n': '{:.3f}', 
                            'Ea (cal/mol)': '{:.3e}'
                        })
                        placeholder1.dataframe(formatted_df2, use_container_width=True)
                        
                        # 处理自定义反应速率输入
                        rc_compare = RateConstantAnalyzer.process_rate_constants(values_rc, temp_min, temp_max)
                        rc_compare.insert(0, reaction_selected, rc)
                    except Exception as e:
                        st.error(f"❌ 正反应计算失败: {str(e)}")
                        rc_compare = RateConstantAnalyzer.process_rate_constants(values_rc, temp_min, temp_max)
                else:
                    rc_compare = RateConstantAnalyzer.process_rate_constants(values_rc, temp_min, temp_max)
                
                # 创建反应速率常数对比图
                fig_rc = RateConstantAnalyzer.create_rate_constant_plot(rc_compare)
                
            else:
                # 逆反应分析 - 使用重构的分析器处理自定义输入，但保持热力学计算的legacy代码
                rc_compare = RateConstantAnalyzer.process_rate_constants(values_rc, temp_min, temp_max)
                
                if 'c' not in st.session_state:
                    st.markdown("""
<div class="error-card">
                        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 请先导入热力学文件</h3>
                        <p style="margin: 0.5rem 0 0 0;">
                            逆反应分析需要热力学文件支持，请先导入热力学文件。
                        </p>
                    </div>
                    """, unsafe_allow_html=True)
                    st.stop()
                
                if reaction_selected is not None:
                    try:
                        st.session_state['c'].reversed_rate(
                            st.session_state['a'], 
                            reaction_selected, 
                            temp_min, temp_max
                        )
                        
                        if st.session_state['c'].reverse_rate_constants is None:
                            st.error('❌ 不存在逆反应速率')
                        else:
                            rc_compare.insert(0, reaction_selected, st.session_state['c'].reverse_rate_constants.iloc[:, 0])
                            rc_compare.insert(0, 'Equilibrium constants', st.session_state['c'].reverse_rate_constants.iloc[:, 2])
                        
                        # 拟合逆反应参数
                        if placeholder2.button('🔧 拟合逆反应参数'):
                            try:
                                rc_fit = st.session_state['c'].fit_arr(temp_min, temp_max)
                                A_parameters.loc['Parameter', 'A(cm3 mol-1 s-1)'] = st.session_state['c'].Arr_fitted['A']
                                A_parameters.loc['Parameter', 'n'] = st.session_state['c'].Arr_fitted['b']
                                A_parameters.loc['Parameter', 'Ea (cal/mol)'] = st.session_state['c'].Arr_fitted['Ea']
                                
                                formatted_df2 = A_parameters.style.format({
                                    'A(cm3 mol-1 s-1)': '{:.3e}', 
                                    'n': '{:.3f}', 
                                    'Ea (cal/mol)': '{:.3e}'
                                })
                                placeholder1.dataframe(formatted_df2, use_container_width=True)
                            except Exception as e:
                                st.error(f'❌ 拟合失败！{str(e)}')
                        
                        # 使用重构的绘图函数
                        fig_rc = RateConstantAnalyzer.create_rate_constant_plot(rc_compare.iloc[:, 1:])
                    except Exception as e:
                        st.error(f"❌ 逆反应计算失败: {str(e)}")
                        fig_rc = RateConstantAnalyzer.create_rate_constant_plot(rc_compare)
                else:
                    fig_rc = RateConstantAnalyzer.create_rate_constant_plot(rc_compare)
            
            # 显示结果
            st.markdown("### 📈 反应速率常数对比图")
            st.plotly_chart(fig_rc, use_container_width=True)
            
            st.markdown("### 📊 详细数据表")
            display_rc = rc_compare.T.filter(temp_range).applymap(
                lambda x: f'{x:.2e}' if isinstance(x, (int, float)) else str(x)
            ).style.highlight_max(axis=0)
            st.dataframe(display_rc, use_container_width=True)
            
        except Exception as e:
            st.error(f"❌ 分析计算失败: {str(e)}")
            st.info("请检查参数设置和数据完整性")
else:
    st.markdown("""
<div class="warning-card">
        <h3 style="color: var(--secondary-color); margin: 0;">⚙️ 请选择反应或输入自定义速率</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请选择要分析的反应或在自定义区域输入反应速率数据，分析结果将自动显示。
        </p>
    </div>
    """, unsafe_allow_html=True)

# ========================================
# 分析说明
# ========================================

with st.expander("ℹ️ 分析说明", expanded=False):
    st.markdown("""
### 反应速率常数对比分析说明
    
    **分析目的**：
    - 对比不同反应的速率常数及其温度依赖性
    - 分析阿仑尼乌斯参数(A, n, Ea)的影响
    - 评估反应在不同温度下的相对重要性
    
    **参数说明**：
    - **A**: 指前因子 (cm³ mol⁻¹ s⁻¹)
    - **n**: 温度指数
    - **Ea**: 活化能 (cal/mol)
    - **温度范围**: 分析的温度区间
    
    **正反应 vs 逆反应**：
    - **正反应**: 直接使用机理文件中的阿仑尼乌斯参数
    - **逆反应**: 需要热力学数据计算平衡常数，再获得逆反应速率
    
    **应用建议**：
    - 关注在目标温度范围内速率常数较大的反应
    - 分析温度依赖性强弱（斜率大小）
    - 结合实际工况选择重要反应进行进一步研究
    """)

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>⚗️ 反应速率常数对比 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)

