import streamlit as st
import pandas as pd
import numpy as np
import os
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
from src.optimized import compatibility_wrapper as opt_sl
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme
from achievement_system import achievement_system

# ========================================
# 页面配置和主题应用
# ========================================

# 页面配置
st.set_page_config(
    layout="wide", 
    page_title="ROP分析工具箱V6.0 测试版", 
    page_icon="🔬",
    initial_sidebar_state="expanded"
)

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 额外的自定义样式（补充主题管理器的样式）
st.markdown("""
<style>
    /* 隐藏默认的Streamlit样式 */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* 自定义标题样式 */
    .main-header {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        padding: 1rem 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px var(--shadow-color);
    }
    
    .main-header h1 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .main-header p {
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    /* 状态指示器 */
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-success { background-color: var(--success-color); }
    .status-warning { background-color: var(--warning-color); }
    .status-info { background-color: var(--info-color); }
    
    /* 进度条样式 */
    .progress-container {
        background: var(--surface-color);
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    /* 数据表格样式 */
    .dataframe {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
    
    /* 彩蛋按钮样式增强 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color)) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.6rem 1.5rem !important;
        font-weight: 600 !important;
        box-shadow: 0 2px 4px var(--shadow-color) !important;
        transition: all 0.3s ease !important;
    }
    
    .stButton > button:hover {
        background: linear-gradient(90deg, var(--info-color), var(--primary-color)) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px var(--shadow-hover) !important;
    }
    
    /* 选择框样式增强 */
    .stSelectbox > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
    }
    
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    /* 锁定按钮样式 */
    .stButton > button:disabled {
        background: linear-gradient(90deg, #6c757d, #adb5bd) !important;
        color: #ffffff !important;
        border: 2px solid #495057 !important;
        opacity: 0.7 !important;
        cursor: not-allowed !important;
        font-weight: 600 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }
    
    .stButton > button:disabled:hover {
        background: linear-gradient(90deg, #6c757d, #adb5bd) !important;
        transform: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }
    
    /* 彩蛋特效样式 */
    @keyframes rainbow {
        0% { color: #ff0000; }
        16% { color: #ff8000; }
        33% { color: #ffff00; }
        50% { color: #00ff00; }
        66% { color: #0080ff; }
        83% { color: #8000ff; }
        100% { color: #ff0000; }
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    .rainbow-text {
        animation: rainbow 2s infinite;
        font-weight: bold;
    }
    
    .pulse-effect {
        animation: pulse 1s infinite;
    }
    
    .shake-effect {
        animation: shake 0.5s infinite;
    }
    
    .easter-egg-container {
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
        background-size: 400% 400%;
        animation: gradient 3s ease infinite;
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    
    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* 化学主题特效 */
    @keyframes molecule-bounce {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(90deg); }
        50% { transform: translateY(0px) rotate(180deg); }
        75% { transform: translateY(-5px) rotate(270deg); }
    }

    @keyframes chemical-glow {
        0% { box-shadow: 0 0 5px #4ecdc4; }
        50% { box-shadow: 0 0 20px #4ecdc4, 0 0 30px #4ecdc4; }
        100% { box-shadow: 0 0 5px #4ecdc4; }
    }

    @keyframes h2o-flow {
        0% { transform: translateX(-100%); opacity: 0; }
        50% { opacity: 1; }
        100% { transform: translateX(100%); opacity: 0; }
    }

    .molecule-effect {
        animation: molecule-bounce 2s infinite;
    }

    .chemical-glow {
        animation: chemical-glow 1.5s infinite;
    }

    .h2o-flow {
        animation: h2o-flow 3s infinite;
    }

    .morse-hint {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: 2px dashed #4ecdc4;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
        animation: chemical-glow 2s infinite;
    }

    /* 增强对比度的通知样式 - WCAG AA 合规 */
    .stAlert[data-baseweb="notification"] {
        border-radius: 8px !important;
        border-width: 2px !important;
        font-weight: 500 !important;
    }

    /* 错误信息高对比度 */
    .stAlert[data-baseweb="notification"][kind="error"] {
        background-color: #f8d7da !important;
        border-color: #dc3545 !important;
        color: #721c24 !important;
    }

    /* 警告信息高对比度 */
    .stAlert[data-baseweb="notification"][kind="warning"] {
        background-color: #fff3cd !important;
        border-color: #ffc107 !important;
        color: #856404 !important;
    }

    /* 成功信息高对比度 */
    .stAlert[data-baseweb="notification"][kind="success"] {
        background-color: #d4edda !important;
        border-color: #28a745 !important;
        color: #155724 !important;
    }

    /* 信息提示高对比度 */
    .stAlert[data-baseweb="notification"][kind="info"] {
        background-color: #d1ecf1 !important;
        border-color: #17a2b8 !important;
        color: #0c5460 !important;
    }

    /* 增强按钮禁用状态的对比度 */
    .stButton > button:disabled {
        background: linear-gradient(90deg, #6c757d, #adb5bd) !important;
        color: #ffffff !important;
        border: 2px solid #495057 !important;
        opacity: 0.8 !important;
        cursor: not-allowed !important;
        font-weight: 600 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
    }

    /* 增强进度条文字对比度 */
    .stProgress + div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }

    /* 增强指标文字对比度 */
    .stMetric > div {
        color: var(--text-primary) !important;
        font-weight: 600 !important;
    }

    /* 增强标题对比度 */
    .main-header h1, .main-header p {
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 工具函数
# ========================================

def create_status_card(title, status, details="", icon=""):
    """创建状态卡片"""
    status_class = "success-card" if status == "success" else "warning-card" if status == "warning" else "info-card-blue"
    status_indicator = "status-success" if status == "success" else "status-warning" if status == "warning" else "status-info"
    
    return f"""
    <div class="info-card {status_class}">
        <div style="display: flex; align-items: center;">
            <span class="status-indicator {status_indicator}"></span>
            <strong>{icon} {title}</strong>
        </div>
        {f'<p style="margin: 0.5rem 0 0 20px; color: #666;">{details}</p>' if details else ''}
    </div>
    """

def check_processing_status():
    """检查处理状态"""
    gas_status = "success" if 'a' in st.session_state else "warning"
    chemkin_status = "success" if 'b' in st.session_state else "warning"
    
    return gas_status, chemkin_status

def display_file_info():
    """显示文件信息"""
    if 'a' in st.session_state:
        a = st.session_state['a']
        st.markdown(create_status_card(
            "Gas.out文件", 
            "success", 
            f"组分数量: {len(a.species)} | 反应数量: {len(a.reactions)} | 元素数量: {len(a.elements)}",
            "✅"
        ), unsafe_allow_html=True)
    
    if 'b' in st.session_state:
        b = st.session_state['b']
        st.markdown(create_status_card(
            "CHEMKIN文件", 
            "success", 
            f"工况点: {b.variable_name} {b.variables} {b.variable_unit}",
            "✅"
        ), unsafe_allow_html=True)

# ========================================
# 主程序
# ========================================

# 主标题
st.markdown("""
<div class="main-header">
    <h1>🔬 ROP分析工具箱 V6.0</h1>
    <p>CHEMKIN 反应路径分析工具</p>
</div>
""", unsafe_allow_html=True)

# 侧边栏
with st.sidebar:
    st.markdown("### 🎯 功能导航")
    menu_selection = st.selectbox(
        '', 
        ['🏠 首页', '📊 单点ROP分析', '📈 多点ROP分析', '🔄 元素流向分析', '📋 数据对比', 'ℹ️ 关于'],
        label_visibility="collapsed"
    )
    
    st.markdown("---")
    
    # 状态监控
    st.markdown("### 📋 处理状态")
    gas_status, chemkin_status = check_processing_status()
    
    gas_icon = "✅" if gas_status == "success" else "❌"
    chemkin_icon = "✅" if chemkin_status == "success" else "❌"
    
    st.markdown(f"""
    - {gas_icon} Gas.out文件
    - {chemkin_icon} CHEMKIN文件
    """)
    
    if gas_status == "success" and chemkin_status == "success":
        st.success("🎉 系统就绪")
    else:
        st.warning("⚠️ 请完成文件处理")
    
    st.markdown("---")
    
    # 彩蛋按钮区域
    st.markdown("### 🎈 娱乐时间")

    # 初始化成就系统和计数器
    if 'achievement_system_initialized' not in st.session_state:
        # 增加启动次数
        launch_count = achievement_system.increment_launch_count()
        achievement_system.add_launch_achievement()
        st.session_state.achievement_system_initialized = True
        st.session_state.launch_count = launch_count

    # 获取持久化的点击计数
    persistent_click_count = achievement_system.get_click_count()

    # 显示统计信息
    col_stat1, col_stat2 = st.columns(2)
    with col_stat1:
        st.metric("🚀 启动次数", st.session_state.get('launch_count', 0))
    with col_stat2:
        st.metric("🎯 总点击数", persistent_click_count)
    
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🎈 气球", help="释放彩色气球！", use_container_width=True):
            st.balloons()
            achievement_system.increment_click_count()
            # 检查摩尔斯序列（气球 = .）
            if achievement_system.add_morse_signal('balloon', menu_selection):
                st.success("🧬 恭喜！您发现了H2O的终极秘密！")
                st.balloons()
                st.snow()
            st.rerun()  # 刷新页面以更新计数

    with col2:
        if st.button("❄️ 雪花", help="下雪啦！", use_container_width=True):
            st.snow()
            achievement_system.increment_click_count()
            # 检查摩尔斯序列（雪花 = -）
            if achievement_system.add_morse_signal('snow', menu_selection):
                st.success("🧬 恭喜！您发现了H2O的终极秘密！")
                st.balloons()
                st.snow()
            st.rerun()  # 刷新页面以更新计数
    
    # 更多特效按钮
    special_effects = st.selectbox(
        "🎭 特殊效果",
        ["选择一个特效...", "🎊 庆祝模式", "🌟 成功提示", "⚡ 能量爆发", "🔥 燃烧动力学", "🧪 化学反应", "🌈 彩虹文字", "💫 脉冲效果", "🎯 震动警告"],
        key="special_effects"
    )
    
    if special_effects == "🎊 庆祝模式":
        st.balloons()
        achievement_system.increment_click_count()
        # 庆祝模式触发气球效果，检查摩尔斯序列
        if achievement_system.add_morse_signal('balloon', menu_selection):
            st.success("🧬 恭喜！您发现了H2O的终极秘密！")
            st.balloons()
            st.snow()
        st.markdown("""
        <div class="easter-egg-container">
            <h3 style="text-align: center; color: white; margin: 0;">🎊 恭喜！您发现了隐藏的庆祝模式！</h3>
            <p style="text-align: center; color: white; margin: 0.5rem 0 0 0;">🎯 ROP分析让化学反应路径一目了然！</p>
        </div>
        """, unsafe_allow_html=True)
        st.rerun()

    elif special_effects == "🌟 成功提示":
        achievement_system.increment_click_count()
        st.success("🌟 您是CHEMKIN分析的专家！")
        st.info("💡 继续探索反应路径的奥秘吧！")
        st.rerun()

    elif special_effects == "⚡ 能量爆发":
        achievement_system.increment_click_count()
        st.snow()
        # 雪花效果，检查摩尔斯序列
        if achievement_system.add_morse_signal('snow', menu_selection):
            st.success("🧬 恭喜！您发现了H2O的终极秘密！")
            st.balloons()
            st.snow()
        st.warning("⚡ 高能预警：反应速率分析进行中...")
        st.success("✨ 能量释放完毕！")
        st.rerun()

    elif special_effects == "🔥 燃烧动力学":
        achievement_system.increment_click_count()
        st.balloons()
        # 气球效果，检查摩尔斯序列
        if achievement_system.add_morse_signal('balloon', menu_selection):
            st.success("🧬 恭喜！您发现了H2O的终极秘密！")
            st.balloons()
            st.snow()
        st.error("🔥 燃烧反应激烈进行中...")
        st.success("🎯 燃烧动力学分析完成！")
        st.rerun()

    elif special_effects == "🧪 化学反应":
        achievement_system.increment_click_count()
        st.snow()
        # 雪花效果，检查摩尔斯序列
        if achievement_system.add_morse_signal('snow', menu_selection):
            st.success("🧬 恭喜！您发现了H2O的终极秘密！")
            st.balloons()
            st.snow()
        st.info("🧪 A + B → C + D")
        st.success("⚗️ 反应路径分析成功！")
        st.balloons()
        # 气球效果，再次检查摩尔斯序列
        if achievement_system.add_morse_signal('balloon', menu_selection):
            st.success("🧬 恭喜！您发现了H2O的终极秘密！")
            st.balloons()
            st.snow()
        st.rerun()

    elif special_effects == "🌈 彩虹文字":
        achievement_system.increment_click_count()
        st.markdown("""
        <div style="text-align: center; padding: 1rem;">
            <h2 class="rainbow-text">🌈 彩虹特效激活！</h2>
            <p class="rainbow-text">ROP分析工具箱 - 让数据更精彩！</p>
        </div>
        """, unsafe_allow_html=True)
        st.rerun()

    elif special_effects == "💫 脉冲效果":
        achievement_system.increment_click_count()
        st.markdown("""
        <div style="text-align: center; padding: 1rem;">
            <h2 class="pulse-effect">💫 脉冲能量场激活！</h2>
            <p class="pulse-effect">感受化学反应的律动！</p>
        </div>
        """, unsafe_allow_html=True)
        st.balloons()
        st.rerun()

    elif special_effects == "🎯 震动警告":
        achievement_system.increment_click_count()
        st.markdown("""
        <div style="text-align: center; padding: 1rem;">
            <h2 class="shake-effect">🎯 高能反应检测到！</h2>
            <p class="shake-effect">系统正在分析反应路径...</p>
        </div>
        """, unsafe_allow_html=True)
        st.snow()
        st.rerun()
    
    # 双彩蛋系统
    classic_unlocked = achievement_system.is_easter_egg_unlocked()
    ultimate_unlocked = achievement_system.is_ultimate_easter_egg_unlocked()
    current_clicks = achievement_system.get_click_count()
    current_launches = achievement_system.get_launch_count()

    # 经典彩蛋 - 隐蔽显示
    if classic_unlocked:
        if st.button("🎪 经典彩蛋", help="恭喜！您已解锁经典特效！", use_container_width=True):
            achievement_system.increment_click_count()
            st.balloons()
            st.snow()
            st.markdown("""
            <div class="easter-egg-container">
                <div style="text-align: center; color: white;">
                    <h1 class="rainbow-text pulse-effect">🎪 经典彩蛋激活！</h1>
                    <h3 class="shake-effect">🔬 ROP分析工具箱 V6.1</h3>
                    <p>🏛️ 中科院工程热物理研究所</p>
                    <p>👨‍🔬 田振玉研究员团队</p>
                    <p>💻 王杜 <EMAIL></p>
                    <p class="rainbow-text">🎉 感谢您使用我们的工具！</p>
                    <p class="pulse-effect">🏆 您是坚持不懈的点击大师！</p>
                    <p style="font-size: 0.8em; opacity: 0.8;">解锁方式：累计点击{current_clicks}次</p>
                </div>
            </div>
            """, unsafe_allow_html=True)
            st.success("🎊 您发现了经典彩蛋！恭喜成为ROP分析大师！")
            st.info("🚀 继续探索化学反应的奥秘吧！")
            st.rerun()
    else:
        # 显示神秘的锁定状态，不透露具体方法
        st.button(
            "🔒 神秘的传说",
            help="传说中的经典彩蛋，等待有缘人发现...",
            disabled=True,
            use_container_width=True
        )

    # 终极彩蛋 - 隐蔽显示
    if ultimate_unlocked:
        if st.button("🧬 终极彩蛋", help="🎉 恭喜！您发现了终极秘密！", use_container_width=True):
            achievement_system.increment_click_count()
            st.balloons()
            st.snow()
            st.markdown("""
            <div class="easter-egg-container">
                <div style="text-align: center; color: white;">
                    <h1 class="rainbow-text pulse-effect">🧬 终极化学大师！</h1>
                    <h2 class="shake-effect">💧 H₂O - 生命之源</h2>
                    <p class="pulse-effect">🎯 您破解了摩尔斯电码的秘密！</p>
                    <p>🔬 ROP分析工具箱 V6.1</p>
                    <p>🏛️ 中科院工程热物理研究所</p>
                    <p>👨‍🔬 田振玉研究员团队</p>
                    <p>💻 王杜 <EMAIL></p>
                    <p class="rainbow-text">🧪 您是真正的化学大师！</p>
                    <p class="pulse-effect">🏆 H: .... | 2: ..--- | O: ---</p>
                    <p style="font-size: 0.8em; opacity: 0.8;">解锁方式：在关于页面输入H2O摩尔斯序列</p>
                </div>
            </div>
            """, unsafe_allow_html=True)
            st.success("🎊 您发现了终极彩蛋！您是真正的化学大师！")
            st.info("💧 H₂O的秘密已被您破解！继续探索化学的奥秘吧！")
            st.rerun()
    elif current_launches >= 42:
        # 显示神秘提示，但不透露具体方法
        st.button(
            "🔍 未知的秘密",
            help="古老的传说中隐藏着化学的奥秘...",
            disabled=True,
            use_container_width=True
        )
    else:
        # 显示完全神秘的锁定状态
        st.button(
            "🔒 远古的谜题",
            help="传说中的远古谜题，需要足够的智慧才能接近...",
            disabled=True,
            use_container_width=True
        )
    
    # 成就系统和进度显示
    st.markdown("#### 🏆 成就系统")

    # 隐蔽的进度显示 - 只有解锁后才显示具体信息
    col_prog1, col_prog2 = st.columns(2)

    with col_prog1:
        if classic_unlocked:
            st.markdown("**🎪 经典传说**")
            st.success("✅ 已解锁")
            st.caption("通过坚持不懈的探索获得")
        else:
            st.markdown("**🎪 神秘传说**")
            st.progress(0.3)  # 显示神秘的固定进度
            st.caption("传说正在酝酿中...")

    with col_prog2:
        if ultimate_unlocked:
            st.markdown("**🧬 远古奥秘**")
            st.success("✅ 已解锁")
            st.caption("破解了生命之源的密码")
        else:
            st.markdown("**🧬 远古奥秘**")
            # 根据启动次数显示不同的神秘进度
            if current_launches >= 42:
                st.progress(0.7)
                st.caption("奥秘的大门已经开启...")
            else:
                st.progress(0.1)
                st.caption("远古的秘密沉睡中...")

    # 启动次数成就进度 - 隐蔽显示
    st.markdown("**🚀 成长历程**")
    launch_milestones = [
        (10, "🔧 反应动力学打工仔"),
        (50, "⚗️ 反应动力学小佬"),
        (200, "🧪 反应动力学中佬"),
        (1000, "🏆 反应动力学大佬")
    ]

    # 获取已解锁的成就
    achievements = achievement_system.get_achievements()
    unlocked_titles = [ach['title'] for ach in achievements]

    for milestone, title in launch_milestones:
        if title in unlocked_titles:
            st.success(f"✅ {title} - 已解锁！")
        else:
            # 显示神秘的锁定状态，不透露具体要求
            st.info(f"🔒 {title} - 等待解锁")
            st.caption("通过持续使用工具来解锁更多成就")

    # 显示成就徽章
    achievements = achievement_system.get_achievements()
    if achievements:
        st.markdown("**🎖️ 已获得成就:**")
        for achievement in achievements[-3:]:  # 显示最近3个成就
            st.markdown(f"- {achievement['title']}: {achievement['description']}")

        if len(achievements) > 3:
            with st.expander(f"查看全部 {len(achievements)} 个成就"):
                for achievement in achievements:
                    unlock_date = achievement['unlocked_at'][:10]  # 只显示日期
                    st.markdown(f"**{achievement['title']}** ({unlock_date})")
                    st.caption(achievement['description'])

    # 系统信息
    with st.expander("📊 系统统计"):
        system_info = achievement_system.get_system_info()
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("🚀 总启动次数", system_info['launch_count'])
            st.metric("🎯 总点击次数", system_info['click_count'])
        with col2:
            st.metric("🏆 获得成就", system_info['achievements_count'])
            st.metric("🔍 序列尝试", system_info['sequence_attempts'])
        with col3:
            classic_status = "✅ 已解锁" if system_info['easter_egg_unlocked'] else "🔒 未解锁"
            ultimate_status = "✅ 已解锁" if system_info['ultimate_easter_egg_unlocked'] else "🔒 未解锁"
            st.markdown(f"**🎪 经典彩蛋:** {classic_status}")
            st.markdown(f"**🧬 终极彩蛋:** {ultimate_status}")
            st.caption(f"设备ID: {system_info['computer_id']}")

    # 重置数据按钮（危险操作）
    if current_clicks > 0:
        with st.expander("⚠️ 危险操作"):
            st.warning("重置将清除所有数据，包括启动次数、点击次数和成就记录！")
            if st.button("🔄 重置所有数据", help="清除所有持久化数据", use_container_width=True):
                if achievement_system.reset_data():
                    st.success("🔄 所有数据已重置！")
                    st.rerun()

# ========================================
# 页面内容
# ========================================

if menu_selection == 'ℹ️ 关于':
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown(f"""
        <div class="info-card">
            <h3>🏛️ 中科院工程热物理研究所</h3>
            <h4>田振玉研究员团队</h4>
            <p><strong>程序编写：</strong>王杜 (<EMAIL>)</p>
            <p><strong>版本：</strong>V6.1 现代化版本</p>
            <p><strong>优化：</strong>AI Assistant</p>
            <p><strong>更新时间：</strong>{datetime.now().strftime("%Y-%m-%d %H:%M")}</p>
        </div>
        """, unsafe_allow_html=True)

        # 隐蔽的线索（仅在特定条件下显示，且已解锁后才显示完整信息）
        if achievement_system.is_ultimate_easter_egg_unlocked():
            # 已解锁，显示完整的解锁方式
            st.markdown("""
            <div class="info-card success-card">
                <h4>� 终极秘密已解锁</h4>
                <p>💧 <strong>H₂O</strong> - 您已破解生命之源的密码</p>
                <p>🔬 摩尔斯电码序列：</p>
                <ul>
                    <li><strong>H</strong>: .... (4个短信号)</li>
                    <li><strong>2</strong>: ..--- (2个短信号 + 3个长信号)</li>
                    <li><strong>O</strong>: --- (3个长信号)</li>
                </ul>
                <p style="font-size: 0.8em; opacity: 0.8;">解锁方式：气球(🎈)=短信号，雪花(❄️)=长信号，在此页面60秒内完成序列</p>
            </div>
            """, unsafe_allow_html=True)
        elif achievement_system.get_launch_count() >= 42:
            # 达到条件但未解锁，显示神秘线索
            st.markdown("""
            <div class="info-card info-card-blue" style="border: 2px dashed #4ecdc4; opacity: 0.8;">
                <h4>🔮 古老的传说</h4>
                <p>💧 传说中，生命之源蕴含着神秘的密码...</p>
                <p>🎵 古老的信号在时间中回响</p>
                <p style="font-size: 0.8em; color: #666;">💫 也许，某些符号能够唤醒沉睡的秘密</p>
                <p style="font-size: 0.8em; color: #666;">⏳ 时间是解开谜题的关键</p>
            </div>
            """, unsafe_allow_html=True)

            # 显示当前摩尔斯进度（如果有的话），但不透露目标
            morse_progress = achievement_system.get_morse_progress()
            if morse_progress['current_length'] > 0:
                st.markdown(f"""
                <div class="info-card" style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);">
                    <h4>🎯 神秘的回响</h4>
                    <p><strong>当前信号：</strong>{morse_progress['current_sequence']}</p>
                    <p><strong>信号强度：</strong>{morse_progress['current_length']} 个符号</p>
                    <p><strong>剩余时间：</strong>{morse_progress['time_remaining']:.0f}秒</p>
                </div>
                """, unsafe_allow_html=True)
        
        st.markdown("""
        <div class="info-card info-card-blue">
            <h4>🚀 V6.0 新特性</h4>
            <ul>
                <li>🎨 全新现代化界面设计</li>
                <li>📱 响应式布局，支持多设备</li>
                <li>⚡ 优化的代码架构，提升性能</li>
                <li>📊 增强的数据可视化</li>
                <li>🔧 模块化设计，便于维护</li>
                <li>🎯 改进的用户体验</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # 可以添加logo或其他信息
        st.info("🔬 ROP分析工具箱\n\n专业的CHEMKIN反应路径分析工具")

elif menu_selection == '🏠 首页':
    
    # 欢迎信息
    st.markdown("""
    <div class="info-card info-card-blue">
        <h3>👋 欢迎使用ROP分析工具箱</h3>
        <p>请按照以下步骤进行分析：</p>
        <ol>
            <li><strong>前处理：</strong>上传并处理gas.out文件</li>
            <li><strong>后处理：</strong>上传并处理CHEMKIN结果文件</li>
            <li><strong>分析：</strong>选择相应的分析功能</li>
        </ol>
    </div>
    """, unsafe_allow_html=True)
    
    # 文件处理区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📁 前处理 - Gas.out文件")
        
        # 显示当前状态
        if 'a' in st.session_state:
            st.markdown(create_status_card(
                "已完成前处理", 
                "success", 
                f"✅ {len(st.session_state['a'].species)} 个组分，{len(st.session_state['a'].reactions)} 个反应",
                "🎯"
            ), unsafe_allow_html=True)
        else:
            st.markdown(create_status_card(
                "等待处理", 
                "warning", 
                "请上传gas.out文件",
                "⏳"
            ), unsafe_allow_html=True)
        
        file1 = st.file_uploader(
            "上传gas.out文件", 
            type=['out'],
            help="选择CHEMKIN前处理生成的gas.out文件"
        )
        
        col1_1, col1_2 = st.columns(2)
        with col1_1:
            process_gas_btn = st.button('🚀 开始前处理', use_container_width=True)
        with col1_2:
            if 'a' in st.session_state:
                st.button('✅ 前处理完成', disabled=True, use_container_width=True)
        
        if process_gas_btn:
            if file1:
                if 'b' in st.session_state:
                    st.warning('⚠️ 重新前处理后需要重新后处理！')
                    
                progress_container = st.container()
                with progress_container:
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        status_text.text('正在初始化...')
                        progress_bar.progress(10)
                        
                        a = opt_sl.solution(file1)
                        status_text.text('正在处理gas.out文件...')
                        progress_bar.progress(50)
                        
                        a.process_gas_out(True)
                        status_text.text('正在保存结果...')
                        progress_bar.progress(90)
                        
                        st.session_state['a'] = a
                        progress_bar.progress(100)
                        status_text.text('✅ 前处理完成！')
                        
                        st.success(f"🎉 前处理成功！检测到 **{len(a.species)}** 个组分，**{len(a.reactions)}** 个反应")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"❌ 前处理失败: {str(e)}")
                        progress_bar.empty()
                        status_text.empty()
            else:
                st.error('❌ 请先选择gas.out文件')
    
    with col2:
        st.markdown("### 📊 后处理 - CHEMKIN结果文件")
        
        # 显示当前状态
        if 'b' in st.session_state:
            st.markdown(create_status_card(
                "已完成后处理", 
                "success", 
                f"✅ 工况: {st.session_state['b'].variable_name} {st.session_state['b'].variables}",
                "🎯"
            ), unsafe_allow_html=True)
        else:
            st.markdown(create_status_card(
                "等待处理", 
                "warning", 
                "请先完成前处理，然后上传CHEMKIN结果文件",
                "⏳"
            ), unsafe_allow_html=True)
        
        file2 = st.file_uploader(
            "上传CHEMKIN结果文件", 
            type=['xlsm', 'xlsx'],
            help="选择CHEMKIN后处理生成的Excel文件"
        )
        
        # 性能优化选项
        use_optimized = st.checkbox(
            "🚀 使用性能优化版本", 
            value=True, 
            help="优化版本可将处理速度提升约1.9倍（推荐开启）"
        )
        
        col2_1, col2_2 = st.columns(2)
        with col2_1:
            process_chemkin_btn = st.button('🚀 开始后处理', use_container_width=True)
        with col2_2:
            if 'b' in st.session_state:
                st.button('✅ 后处理完成', disabled=True, use_container_width=True)
        
        if process_chemkin_btn:
            if 'a' not in st.session_state:
                st.error("❌ 请先完成前处理！")
            elif file2:
                progress_container = st.container()
                with progress_container:
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    try:
                        status_text.text('🔍 正在分析文件结构...')
                        progress_bar.progress(5)
                        
                        b = opt_sl.ckfile(file2, use_optimized=use_optimized)
                        
                        status_text.text('📊 正在加载CHEMKIN工作表...')
                        progress_bar.progress(15)
                        
                        b.load_chemkin_file()
                        
                        # 显示检测到的工作表信息
                        sheet_info = []
                        if len(b.soln_sheets) > 0:
                            sheet_info.append(f"Solution表: {len(b.soln_sheets)}个")
                        if len(b.end_point_sheets) > 0:
                            sheet_info.append(f"End_point表: {len(b.end_point_sheets)}个")
                        if len(b.rop_sheets) > 0:
                            sheet_info.append(f"ROP表: {len(b.rop_sheets)}个")
                        if len(b.sensitivity_sheets) > 0:
                            sheet_info.append(f"敏感性表: {len(b.sensitivity_sheets)}个")
                        
                        status_text.text(f'✅ 文件结构分析完成 - {", ".join(sheet_info)}')
                        progress_bar.progress(25)
                        
                        # 显示变量信息
                        if hasattr(b, 'need_extract_endpoints') and b.need_extract_endpoints:
                            status_text.text('⚠️ 检测到缺少end_point表，将从solution表提取端点数据...')
                        else:
                            status_text.text(f'📈 检测到变量: {b.variable_name} ({len(b.variables)}个工况)')
                        progress_bar.progress(35)
                        
                        if use_optimized:
                            status_text.text('🔄 正在合并工作表数据（优化版本）...')
                        else:
                            status_text.text('🔄 正在合并工作表数据（标准版本）...')
                        progress_bar.progress(50)
                        
                        # 为Solution数据收集创建专用进度条（仅用于原版）
                        if not use_optimized:
                            st.session_state.solution_progress = st.progress(0, text='准备收集Solution数据...')
                        
                        # 根据用户选择使用对应版本的combine_sheets方法
                        if use_optimized and hasattr(b, 'combine_sheets_optimized'):
                            b.combine_sheets_optimized(st.session_state['a'], progress_bar=True)
                        else:
                            # 使用标准版本方法
                            b.combine_sheets(st.session_state['a'], True)
                        
                        # 清理临时进度条
                        if not use_optimized and hasattr(st.session_state, 'solution_progress'):
                            st.session_state.solution_progress.empty()
                            del st.session_state.solution_progress
                        
                        status_text.text('💾 正在保存处理结果...')
                        progress_bar.progress(95)
                        
                        st.session_state['b'] = b
                        progress_bar.progress(100)
                        status_text.text('✅ 后处理完成！')
                        
                        # 显示处理结果摘要
                        success_msg = f"🎉 后处理成功！\n\n"
                        success_msg += f"**变量**: {b.variable_name} ({b.variable_unit})\n"
                        success_msg += f"**工况数**: {len(b.variables)}\n"
                        success_msg += f"**工况值**: {b.variables}\n"
                        
                        if hasattr(st.session_state['a'], 'mole_fractions'):
                            success_msg += f"**组分数**: {len(st.session_state['a'].mole_fractions.columns)}\n"
                        if hasattr(st.session_state['a'], 'sensitivity_collect'):
                            success_msg += f"**敏感性数据**: ✅ 已加载\n"
                        if hasattr(b, 'need_extract_endpoints') and b.need_extract_endpoints:
                            success_msg += f"**特殊处理**: 从solution表提取了端点数据"
                        
                        st.success(success_msg)
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"❌ 后处理失败: {str(e)}")
                        progress_bar.empty()
                        status_text.empty()
            else:
                st.error('❌ 请先选择CHEMKIN结果文件')
    
    # 文件信息显示
    if 'a' in st.session_state or 'b' in st.session_state:
        st.markdown("### 📋 文件信息")
        display_file_info()

elif menu_selection == '📊 单点ROP分析':
    st.markdown("### 📊 单点ROP分析")
    
    if 'a' not in st.session_state or 'b' not in st.session_state:
        st.markdown(create_status_card(
            "系统未就绪", 
            "warning", 
            "请先完成文件处理（前处理和后处理）",
            "⚠️"
        ), unsafe_allow_html=True)
    else:
        a = st.session_state['a']
        b = st.session_state['b']
        
        # 创建现代化的标签页
        tab1, tab2 = st.tabs(["🔬 反应ROP", "🧪 组分ROP"])
        
        with tab1:
            st.markdown("#### 反应对组分生成的贡献分析")
            
            # 参数设置区域
            with st.expander("🎛️ 分析参数设置", expanded=True):
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    selected_species = st.selectbox(
                        "目标组分", 
                        a.species, 
                        key="single_species",
                        help="选择要分析的目标组分"
                    )
                
                with col2:
                    selected_temp = st.selectbox(
                        "工况点", 
                        b.variables, 
                        key="single_temp",
                        help="选择分析的工况点"
                    )
                
                with col3:
                    datatype = st.selectbox(
                        "数据类型", 
                        ['integral', 'end'], 
                        key="single_datatype",
                        help="积分值或终点值"
                    )
                
                with col4:
                    threshold = st.number_input(
                        "显示阈值", 
                        min_value=0.001, 
                        max_value=1.0, 
                        value=0.01, 
                        step=0.001, 
                        key="single_threshold",
                        help="低于此阈值的反应将被隐藏"
                    )
            
            # 分析按钮
            col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
            with col_btn2:
                analyze_btn = st.button(
                    "🚀 开始分析", 
                    use_container_width=True,
                    type="primary"
                )
            
            if analyze_btn:
                try:
                    with st.spinner("正在生成反应ROP图表..."):
                        fig = a.plot_ROP_single(b, selected_species, selected_temp, threshold, datatype)
                        
                        # 应用主题配置
                        theme_config = get_plotly_theme()
                        fig.update_layout(**theme_config['layout'])
                        
                        # 显示图表
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # 显示数据表
                        if hasattr(a, 'ROP_percent'):
                            with st.expander("📊 详细数据表", expanded=False):
                                display_data = a.ROP_percent.copy()
                                # 格式化显示
                                display_data.loc['Relative values', :] = display_data.loc['Relative values', :].apply(lambda x: f'{x:.2%}')
                                display_data.loc['Absolute values', :] = display_data.loc['Absolute values', :].apply(lambda x: f'{x:.2e}')
                                st.dataframe(display_data.T, use_container_width=True)
                        
                        st.success("✅ 反应ROP分析完成！")
                        
                except Exception as e:
                    st.error(f"❌ 分析失败: {str(e)}")
        
        with tab2:
            st.markdown("#### 组分间的元素流向分析")
            
            # 参数设置区域
            with st.expander("🎛️ 分析参数设置", expanded=True):
                col1, col2, col3, col4, col5 = st.columns(5)
                
                with col1:
                    selected_species_2 = st.selectbox(
                        "目标组分", 
                        a.species, 
                        key="species_species"
                    )
                
                with col2:
                    selected_temp_2 = st.selectbox(
                        "工况点", 
                        b.variables, 
                        key="species_temp"
                    )
                
                with col3:
                    selected_element = st.selectbox(
                        "目标元素", 
                        a.elements, 
                        key="species_element"
                    )
                
                with col4:
                    datatype_2 = st.selectbox(
                        "数据类型", 
                        ['integral', 'end'], 
                        key="species_datatype"
                    )
                
                with col5:
                    threshold_2 = st.number_input(
                        "显示阈值", 
                        min_value=0.001, 
                        max_value=1.0, 
                        value=0.01, 
                        step=0.001, 
                        key="species_threshold"
                    )
            
            # 分析按钮
            col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
            with col_btn2:
                analyze_species_btn = st.button(
                    "🚀 开始元素流向分析", 
                    use_container_width=True,
                    type="primary"
                )
            
            if analyze_species_btn:
                try:
                    with st.spinner("正在生成组分ROP图表..."):
                        fig = a.plot_ROP_single_species(
                            b, selected_species_2, selected_temp_2, 
                            selected_element, threshold_2, datatype_2
                        )
                        
                        # 应用主题配置
                        theme_config = get_plotly_theme()
                        fig.update_layout(**theme_config['layout'])
                        
                        st.plotly_chart(fig, use_container_width=True)
                        
                        # 显示详细信息
                        if hasattr(a, 'ROP_percent_flux'):
                            with st.expander("📊 元素流向详细数据", expanded=False):
                                display_data = a.ROP_percent_flux.copy()
                                st.dataframe(display_data.T, use_container_width=True)
                        
                        st.success("✅ 元素流向分析完成！")
                        
                except Exception as e:
                    st.error(f"❌ 分析失败: {str(e)}")

elif menu_selection == '📈 多点ROP分析':
    st.markdown("### 📈 多点ROP分析")
    
    if 'a' not in st.session_state or 'b' not in st.session_state:
        st.markdown(create_status_card(
            "系统未就绪", 
            "warning", 
            "请先完成文件处理（前处理和后处理）",
            "⚠️"
        ), unsafe_allow_html=True)
    else:
        a = st.session_state['a']
        b = st.session_state['b']
        
        # 参数设置
        with st.expander("🎛️ 分析参数设置", expanded=True):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                selected_species = st.selectbox(
                    "目标组分", 
                    a.species, 
                    key="multi_species"
                )
            
            with col2:
                datatype = st.selectbox(
                    "数据类型", 
                    ['integral', 'end'], 
                    key="multi_datatype"
                )
            
            with col3:
                threshold = st.number_input(
                    "显示阈值", 
                    min_value=0.001, 
                    max_value=1.0, 
                    value=0.01, 
                    step=0.001, 
                    key="multi_threshold"
                )
        
        # 分析按钮
        col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
        with col_btn2:
            analyze_multi_btn = st.button(
                "🚀 开始多点分析", 
                use_container_width=True,
                type="primary"
            )
        
        if analyze_multi_btn:
            try:
                with st.spinner("正在进行多点ROP分析..."):
                    # 处理多点ROP数据
                    a.process_ROP_multi(b, selected_species, threshold, datatype)
                    
                    # 生成图表
                    fig = a.plot_ROP_multi(b)
                    
                    # 应用主题配置
                    theme_config = get_plotly_theme()
                    fig.update_layout(**theme_config['layout'])
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 显示数据表
                    if hasattr(a, 'ROP_percent2_display'):
                        with st.expander("📊 多点ROP详细数据", expanded=False):
                            st.dataframe(a.ROP_percent2_display, use_container_width=True)
                    
                    st.success("✅ 多点ROP分析完成！")
                    
            except Exception as e:
                st.error(f"❌ 分析失败: {str(e)}")

elif menu_selection == '🔄 元素流向分析':
    st.markdown("### 🔄 元素流向分析")
    
    if 'a' not in st.session_state or 'b' not in st.session_state:
        st.markdown(create_status_card(
            "系统未就绪", 
            "warning", 
            "请先完成文件处理（前处理和后处理）",
            "⚠️"
        ), unsafe_allow_html=True)
    else:
        a = st.session_state['a']
        b = st.session_state['b']
        
        # 创建标签页
        tab1, tab2 = st.tabs(["🌐 全局流向分析", "📊 元素分布分析"])
        
        with tab1:
            st.markdown("#### 全局元素流向矩阵")
            
            with st.expander("🎛️ 分析参数设置", expanded=True):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    selected_element = st.selectbox(
                        "目标元素", 
                        a.elements, 
                        key="flux_element"
                    )
                
                with col2:
                    selected_temp_flux = st.selectbox(
                        "工况点", 
                        b.variables, 
                        key="flux_temp"
                    )
                
                with col3:
                    threshold_flux = st.number_input(
                        "显示阈值", 
                        min_value=0.001, 
                        max_value=1.0, 
                        value=0.01, 
                        step=0.001, 
                        key="flux_threshold"
                    )
            
            col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
            with col_btn2:
                analyze_flux_btn = st.button(
                    "🚀 开始全局流向分析", 
                    use_container_width=True,
                    type="primary"
                )
            
            if analyze_flux_btn:
                try:
                    with st.spinner("正在计算全局元素流向..."):
                        a.flux_all(selected_temp_flux, selected_element, threshold_flux, 'integral', True)
                        
                        # 显示流向矩阵
                        if hasattr(a, 'flux'):
                            st.subheader("🌐 元素流向矩阵")
                            st.dataframe(a.flux, use_container_width=True)
                            
                            if hasattr(a, 'flux_normalized'):
                                st.subheader("📊 归一化流向矩阵")
                                st.dataframe(a.flux_normalized, use_container_width=True)
                        
                        st.success("✅ 全局流向分析完成！")
                        
                except Exception as e:
                    st.error(f"❌ 分析失败: {str(e)}")
        
        with tab2:
            st.markdown("#### 元素在不同组分中的分布")
            
            if hasattr(b, 'mole_fraction_exist') and b.mole_fraction_exist:
                with st.expander("🎛️ 分析参数设置", expanded=True):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        selected_element_dist = st.selectbox(
                            "目标元素", 
                            a.elements, 
                            key="dist_element"
                        )
                    
                    with col2:
                        threshold_dist = st.number_input(
                            "显示阈值", 
                            min_value=0.001, 
                            max_value=1.0, 
                            value=0.05, 
                            step=0.001, 
                            key="dist_threshold"
                        )
                
                col_btn1, col_btn2, col_btn3 = st.columns([1, 2, 1])
                with col_btn2:
                    analyze_dist_btn = st.button(
                        "🚀 开始分布分析", 
                        use_container_width=True,
                        type="primary"
                    )
                
                if analyze_dist_btn:
                    try:
                        with st.spinner("正在分析元素分布..."):
                            # 需要先计算初始元素分布
                            if hasattr(a, 'mole_fractions_max'):
                                reactants = a.mole_fractions_max.T.nlargest(8, a.mole_fractions_max.index[0]).index.tolist()
                                a.ele_ini(b, reactants)
                                
                                # 生成元素分布图
                                fig = a.element_plot(b, selected_element_dist, threshold_dist)
                                
                                # 应用主题配置
                                theme_config = get_plotly_theme()
                                fig.update_layout(**theme_config['layout'])
                                
                                st.plotly_chart(fig, use_container_width=True)
                                
                                # 显示数据表
                                if hasattr(a, 'element_percent_display'):
                                    with st.expander("📊 元素分布详细数据", expanded=False):
                                        st.dataframe(a.element_percent_display, use_container_width=True)
                                
                                st.success("✅ 元素分布分析完成！")
                            else:
                                st.error("❌ 缺少摩尔分数数据")
                                
                    except Exception as e:
                        st.error(f"❌ 分析失败: {str(e)}")
            else:
                st.info("💡 当前CHEMKIN文件不包含摩尔分数数据，无法进行元素分布分析")

elif menu_selection == '📋 数据对比':
    st.markdown("### 📋 数据对比分析")
    st.markdown(create_status_card(
        "功能开发中", 
        "info", 
        "数据对比功能正在开发中，敬请期待！",
        "🚧"
    ), unsafe_allow_html=True)

# ========================================
# 页脚
# ========================================
st.markdown("---")
st.markdown(f"""
<div style="text-align: center; color: #666; padding: 1rem;">
    <p>🔬 ROP分析工具箱 V6.0 | 中科院工程热物理研究所 | 燃烧动力学研究中心
    <p>💻 王杜 <EMAIL> ⚡ 基于Streamlit构建</p>
</div>
""", unsafe_allow_html=True)