import streamlit as st
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

import numpy as np
import pandas as pd
import io

def apply_sensitivity_color_mapping(styler):
    """
    为敏感性分析表格应用基于数值的颜色映射
    正值使用暖色系(红色系)，负值使用冷色系(蓝色系)
    """
    def color_mapping(val):
        """为单个敏感性系数值应用颜色"""
        try:
            numeric_val = pd.to_numeric(val, errors='coerce')
        except:
            return ''
        
        if pd.isna(numeric_val):
            return ''
        
        # 使用更严格的零值检测，考虑浮点数精度问题
        if abs(numeric_val) < 1e-10:  # 非常接近零的值都视为零
            # 零值使用中性灰色
            return 'background-color: rgba(200, 200, 200, 0.3); color: black;'
        elif numeric_val > 0:
            # 正值使用暖色系(红色系) - 促进作用
            return 'background-color: rgba(255, 150, 150, 0.7); color: black;'
        else:  # numeric_val < 0
            # 负值使用冷色系(蓝色系) - 抑制作用
            return 'background-color: rgba(150, 150, 255, 0.7); color: black;'
    
    try:
        # 对整个表格应用颜色映射
        return styler.applymap(color_mapping)
    except Exception as e:
        st.warning(f"敏感性颜色映射应用失败: {e}")
        return styler

# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="敏感性分析", page_icon="🔥")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .error-card {
        background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--warning-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .success-card {
        background: linear-gradient(135deg, rgba(44, 160, 44, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--success-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 标签页样式优化 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
    }
    
    .stTabs [data-baseweb="tab"] {
        background: var(--surface-color);
        border-radius: 8px;
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 4px var(--shadow-light);
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
    }
    
    /* 输入控件样式 */
    .stSelectbox > div > div {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 选择框内容区域 */
    .stSelectbox > div > div > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 选择框文本 */
    .stSelectbox > div > div > div > div {
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 选择框选项样式 */
    .stSelectbox option {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
    }
    
    /* 下拉选项 */
    .stSelectbox [role="option"] {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 下拉选项悬停效果 */
    .stSelectbox [role="option"]:hover {
        background-color: var(--primary-color) !important;
        color: white !important;
    }
    
    /* 下拉菜单容器 */
    .stSelectbox [data-baseweb="menu"] {
        background-color: var(--surface-color) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 8px var(--shadow-color) !important;
    }
    
    /* 选择框显示文本 */
    .stSelectbox [data-baseweb="select"] > div {
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
        border: 1px solid var(--border-color) !important;
    }
    
    /* 确保下拉箭头可见 */
    .stSelectbox [data-baseweb="select"] svg {
        color: var(--text-primary) !important;
    }
    
    .stNumberInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background-color: var(--surface-color) !important;
        color: var(--text-primary) !important;
        font-weight: 500 !important;
    }
    
    /* 数据表格样式 */
    .stDataFrame {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 4px var(--shadow-color);
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        border-radius: 4px;
    }
    
    /* 增强下载按钮样式 - 使用更高对比度的配色 */
    .stDownloadButton > button {
        background: linear-gradient(90deg, #DC2626, #B91C1C) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.6rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 14px !important;
        box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: none !important;
        border: 2px solid transparent !important;
    }
    
    .stDownloadButton > button:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4) !important;
        background: linear-gradient(90deg, #B91C1C, #991B1B) !important;
        border: 2px solid #FECACA !important;
    }
    
    .stDownloadButton > button:active {
        transform: translateY(0px) !important;
        box-shadow: 0 3px 6px rgba(220, 38, 38, 0.3) !important;
        background: linear-gradient(90deg, #991B1B, #7F1D1D) !important;
    }
    
    /* 为下载按钮添加图标效果 */
    .stDownloadButton > button:before {
        content: "📥 ";
        margin-right: 4px;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">🔥 敏感性分析</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        反应敏感性分析 - 研究反应对目标组分的敏感性系数变化趋势
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if ('a' and 'b') not in st.session_state:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行敏感性分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

elif not st.session_state['b'].sensitivity_exist:
    st.markdown("""
<div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少敏感性数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有敏感性分析数据，无法进行敏感性分析。请检查CHEMKIN输出设置，确保启用了敏感性分析功能。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ 分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # 检查是否有敏感性组分数据
        if hasattr(st.session_state['a'], 'sensitivity_species_output'):
            available_species = st.session_state['a'].sensitivity_species_output
            st.success(f"✅ 检测到敏感性数据，可分析组分: {len(available_species)} 个")
        else:
            available_species = ['请先处理敏感性数据']
            st.warning("⚠️ 未检测到敏感性数据")
            
        selected_species_sens = st.selectbox(
            "**目标组分**：", 
            options=available_species, 
            key='sens1',
            help="选择要分析敏感性的目标组分"
        )
    
    with col2:
        # 选择分析模式 - 使用radio按钮，终点值为默认
        analysis_mode = st.radio(
            "**分析模式**：",
            options=['endpoint', 'gradient_max'],
            format_func=lambda x: "终点值" if x == 'endpoint' else "梯度最大值处",
            index=0,  # 默认选择终点值
            key='analysis_mode',
            help="选择敏感性分析模式：终点值或梯度最大值处",
            horizontal=True  # 水平排列
        )
        
        # 根据模式显示梯度变量选择器
        if analysis_mode == 'gradient_max':
            # 优先从ChemkinSolution获取梯度变量，如果没有则从GasSolution获取
            available_gradient_vars = []
            
            # 先尝试从ChemkinSolution获取（更准确）
            if hasattr(st.session_state['b'], 'get_available_gradient_variables'):
                try:
                    available_gradient_vars = st.session_state['b'].get_available_gradient_variables()
                    st.info("✅ 从CHEMKIN数据获取梯度变量")
                except Exception as e:
                    st.warning(f"从CHEMKIN数据获取梯度变量失败: {e}")
            
            # 如果失败，则从GasSolution获取
            if not available_gradient_vars and hasattr(st.session_state['a'], 'get_available_gradient_variables'):
                try:
                    available_gradient_vars = st.session_state['a'].get_available_gradient_variables()
                    st.info("✅ 从solution数据获取梯度变量")
                except Exception as e:
                    st.warning(f"从solution数据获取梯度变量失败: {e}")
            
            if not available_gradient_vars:
                st.error("❌ 未检测到可用的梯度变量")
                st.warning("""
                **可能的原因**：
                - solution数据中不包含Temperature、Pressure、Density等物理变量
                - 数据格式不符合预期
                - 数据加载不完整
                
                **建议**：
                - 检查CHEMKIN输出设置，确保包含物理变量数据
                - 尝试使用"终点模式"进行分析
                """)
                gradient_variable = None
            else:
                # 过滤掉敏感性数据列（额外保险措施）
                filtered_vars = [var for var in available_gradient_vars if '_Sens_' not in var]
                if not filtered_vars:
                    st.error("❌ 所有梯度变量都被识别为敏感性数据，这不正确")
                    gradient_variable = None
                else:
                    gradient_variable = st.selectbox(
                        "**梯度变量**：",
                        options=filtered_vars,
                        key='grad_var',
                        help="选择用于计算梯度最大值的变量（仅显示solution数据中的物理变量）"
                    )
        else:
            # 终点模式不需要梯度变量
            gradient_variable = 'Temperature'  # 默认值，但不会使用
            st.info("终点模式：分析各工况点的最后一个时间点")
    
    with col3:
        # 先检查是否有敏感性数据来确定阈值范围
        max_threshold = 1.0  # 默认值
        if hasattr(st.session_state['a'], 'sensitivity_matrix') and not st.session_state['a'].sensitivity_matrix.empty:
            # 从已有的敏感性矩阵中获取最大值
            max_sens_value = st.session_state['a'].sensitivity_matrix.abs().max().max()
            max_threshold = max(max_sens_value, 0.1)  # 确保最小为0.1
        
        threshold_sens = st.number_input(
            '**显示阈值**', 
            min_value=0.0, 
            max_value=max_threshold, 
            value=min(0.01, max_threshold/10),  # 设置为最大值的1/10或0.01
            format="%.3f",
            help=f"设置敏感性系数显示阈值，当前数据最大值约为 {max_threshold:.3f}"
        )
    
    with col4:
        st.markdown("**分析说明**")
        st.info("""
        敏感性分析将计算各反应对目标组分的敏感性系数，
        并在选定变量梯度最大值处进行评估。
        """)
    
    # 检查是否需要选择横坐标变量（没有end_point sheets的情况）
    if hasattr(st.session_state['b'], 'need_extract_endpoints') and st.session_state['b'].need_extract_endpoints:
        st.markdown("---")
        st.markdown("##### 🎯 横坐标变量选择")
        st.info("检测到没有end_point数据表，请选择横坐标变量和数据点类型：")
        
        col1_axis, col2_axis = st.columns(2)
        
        with col1_axis:
            # 获取可用的横坐标变量
            x_vars_last = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'last')
            x_vars_first = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'first')
            
            # 合并两种数据点的变量选项
            all_x_vars = set(x_vars_last.keys()) | set(x_vars_first.keys())
            
            if all_x_vars:
                selected_x_var = st.selectbox(
                    "**横坐标变量**：",
                    options=list(all_x_vars),
                    help="选择用作横坐标的变量",
                    key="sensitivity_x_var"
                )
            else:
                st.error("未找到可用的横坐标变量")
                selected_x_var = None
        
        with col2_axis:
            point_type = st.radio(
                "**数据点类型**：",
                options=['last', 'first'],
                format_func=lambda x: '最后一点' if x == 'last' else '第一点',
                help="选择使用每个工况的第一点还是最后一点数据",
                key="sensitivity_point_type"
            )
        
        # 更新横坐标变量设置
        if selected_x_var and point_type:
            x_vars_data = x_vars_last if point_type == 'last' else x_vars_first
            if selected_x_var in x_vars_data:
                # 临时更新variable信息用于绘图
                st.session_state['a'].temp_variable_name = selected_x_var
                st.session_state['a'].temp_variable_unit = ''
                st.session_state['a'].temp_variables = x_vars_data[selected_x_var]
                
                # 显示选择的横坐标数据
                st.success(f"已选择：{selected_x_var} ({point_type}点)")
                st.text(f"数据值: {x_vars_data[selected_x_var]}")
            else:
                st.error(f"所选变量 {selected_x_var} 在 {point_type} 点数据中不可用")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 分析计算和结果显示（自动分析）
# ========================================

if selected_species_sens != '请先处理敏感性数据':
    # 检查是否可以进行分析
    if analysis_mode == 'gradient_max' and gradient_variable is None:
        st.error("❌ 梯度最大值模式需要有效的梯度变量，请检查数据或切换到终点模式")
        st.stop()
    
    with st.spinner('正在进行敏感性分析，请稍候...'):
        try:
            # 处理敏感性数据
            st.session_state['a'].process_sensitivity_multi(
                chemkin_file=st.session_state['b'], 
                target_species=selected_species_sens, 
                gradient_variable=gradient_variable if gradient_variable else 'Temperature',
                threshold=threshold_sens,
                mode=analysis_mode
            )
            
            # 显示成功消息
            st.markdown("""
<div class="success-card">
                <h3 style="color: var(--success-color); margin: 0;">✅ 敏感性分析完成</h3>
                <p style="margin: 0.5rem 0 0 0;">
                    敏感性分析已完成，结果如下所示。
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # 生成图表
            fig_sensitivity = st.session_state['a'].plot_sensitivity_multi(
                chemkin_file=st.session_state['b'], 
                target_species=selected_species_sens
            )
            
            if fig_sensitivity:
                # 显示图表
                st.markdown("### 📈 敏感性分析趋势图")
                st.plotly_chart(fig_sensitivity, use_container_width=True)
                
                # 显示数据表格
                st.markdown("### 📊 敏感性分析详细数据")
                
                if hasattr(st.session_state['a'], 'sensitivity_matrix'):
                    tabs1, tabs2 = st.tabs(['📊 敏感性系数矩阵', '📈 梯度信息'])
                    
                    with tabs1:
                        st.markdown("#### 敏感性系数数据")
                        
                        # 添加颜色说明
                        st.markdown("""
                        <div style="margin-bottom: 10px; padding: 10px; background-color: rgba(240, 248, 255, 0.5); border-radius: 5px;">
                            <small><strong>颜色说明：</strong> 
                            <span style="background-color: rgba(255, 150, 150, 0.7); padding: 2px 6px; border-radius: 3px;">红色 = 正敏感性(促进生成)</span>
                            <span style="background-color: rgba(150, 150, 255, 0.7); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">蓝色 = 负敏感性(促进消耗)</span>
                            <span style="background-color: rgba(200, 200, 200, 0.3); padding: 2px 6px; border-radius: 3px; margin-left: 10px;">灰色 = 零值</span>
                            <br><small style="color: #666; margin-top: 5px; display: block;">敏感性系数：正值表示反应促进目标组分生成，负值表示促进消耗</small>
                            </small>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # 创建美化的数据表
                        df_sens = st.session_state['a'].sensitivity_matrix.T.copy()
                        
                        try:
                            styled_sens = (
                                df_sens.style
                                .set_table_styles([
                                    {'selector': 'th', 'props': [
                                        ('font-weight', 'bold'), 
                                        ('text-align', 'center'), 
                                        ('background-color', '#eaf3fa'),
                                        ('color', 'black')
                                    ]},
                                    {'selector': 'td', 'props': [
                                        ('text-align', 'center'),
                                        ('padding', '8px')
                                    ]},
                                    {'selector': 'table', 'props': [
                                        ('border-collapse', 'collapse'),
                                        ('border', '1px solid #ddd')
                                    ]}
                                ])
                                .format('{:.3e}')
                            )
                            
                            # 应用敏感性颜色映射
                            styled_sens = apply_sensitivity_color_mapping(styled_sens)
                            
                            # 高亮最大和最小值
                            styled_sens = (styled_sens
                                .highlight_max(axis=1, color='rgba(255, 215, 0, 0.6)')
                                .highlight_min(axis=1, color='rgba(0, 255, 255, 0.6)')
                            )
                            
                            st.dataframe(styled_sens, use_container_width=True)
                            
                        except Exception as e:
                            st.error(f"表格样式应用失败: {str(e)}")
                            st.dataframe(df_sens, use_container_width=True)
                        
                        # 增强的导出按钮
                        csv_sens = df_sens.to_csv().encode('utf-8-sig')
                        st.download_button(
                            "📥 下载敏感性数据 (CSV)", 
                            csv_sens, 
                            file_name=f"sensitivity_{selected_species_sens}_{gradient_variable}.csv", 
                            mime="text/csv",
                            help="下载敏感性系数数据到CSV文件"
                        )
                    
                    with tabs2:
                        st.markdown("#### 分析模式信息")
                        if analysis_mode == 'gradient_max':
                            st.info(f"""
                            **当前分析模式**: 梯度最大值处
                            **梯度变量**: {gradient_variable}
                            
                            **分析说明**: 
                            - 敏感性系数在各工况点{gradient_variable}梯度最大值处进行计算
                            - 正值表示反应对目标组分生成有促进作用
                            - 负值表示反应对目标组分生成有抑制作用
                            - 绝对值越大表示敏感性越强
                            """)
                        else:
                            st.info(f"""
                            **当前分析模式**: 终点值
                            
                            **分析说明**: 
                            - 敏感性系数在各工况点的最后一个时间点进行计算
                            - 正值表示反应对目标组分生成有促进作用
                            - 负值表示反应对目标组分生成有抑制作用
                            - 绝对值越大表示敏感性越强
                            """)
                        
                        # 显示详细信息（仅在梯度模式下显示梯度信息）
                        if analysis_mode == 'gradient_max' and hasattr(st.session_state['a'], 'soln_collect'):
                            st.markdown("##### 各工况点梯度信息")
                            gradient_info = []
                            
                            for condition, soln_data in st.session_state['a'].soln_collect.items():
                                gradient_cols = [col for col in soln_data.columns if gradient_variable in col]
                                if gradient_cols:
                                    gradient_col = gradient_cols[0]
                                    gradient_values = soln_data[gradient_col]
                                    gradient = np.gradient(gradient_values, soln_data.index)
                                    max_gradient_idx = np.argmax(np.abs(gradient))
                                    
                                    gradient_info.append({
                                        '工况点': condition,
                                        f'{gradient_variable}范围': f"{gradient_values.min():.2f} - {gradient_values.max():.2f}",
                                        '最大梯度位置': f"索引 {max_gradient_idx}",
                                        f'最大梯度处{gradient_variable}': f"{gradient_values.iloc[max_gradient_idx]:.2f}",
                                        '最大梯度值': f"{gradient[max_gradient_idx]:.3e}"
                                    })
                            
                            if gradient_info:
                                gradient_df = pd.DataFrame(gradient_info)
                                
                                # 为梯度信息表格添加样式
                                try:
                                    styled_gradient = (
                                        gradient_df.style
                                        .set_table_styles([
                                            {'selector': 'th', 'props': [
                                                ('font-weight', 'bold'), 
                                                ('text-align', 'center'), 
                                                ('background-color', '#f0f8ff'),
                                                ('color', 'black')
                                            ]},
                                            {'selector': 'td', 'props': [
                                                ('text-align', 'center'),
                                                ('padding', '8px')
                                            ]},
                                            {'selector': 'table', 'props': [
                                                ('border-collapse', 'collapse'),
                                                ('border', '1px solid #ddd')
                                            ]}
                                        ])
                                    )
                                    st.dataframe(styled_gradient, use_container_width=True)
                                except Exception as e:
                                    st.warning(f"梯度表格样式应用失败: {e}")
                                    st.dataframe(gradient_df, use_container_width=True)
                        
                        elif analysis_mode == 'endpoint':
                            st.markdown("##### 终点模式信息")
                            st.info("""
                            在终点模式下，敏感性系数直接从各工况点的最后一个时间点提取，
                            无需计算梯度。这种模式适用于分析反应在最终状态的敏感性特征。
                            """)
            else:
                st.error("生成敏感性分析图表失败")
                
        except Exception as e:
            st.error(f"敏感性分析过程中出现错误: {str(e)}")
            st.markdown("""
<div class="error-card">
                <h3 style="color: var(--warning-color); margin: 0;">❌ 分析失败</h3>
                <p style="margin: 0.5rem 0 0 0;">
                    请检查数据完整性，确保敏感性数据和对应的solution数据都存在。
                </p>
            </div>
            """, unsafe_allow_html=True)

# ========================================
# 分析说明
# ========================================

with st.expander("ℹ️ 敏感性分析说明", expanded=False):
    st.markdown("""
### 敏感性分析原理
    
    **分析目的**：
    - 识别对目标组分影响最大的反应
    - 研究不同工况条件下反应敏感性的变化
    - 为机理简化和优化提供指导
    
    **计算方法**：
    - 在各工况点选定变量梯度最大值处计算敏感性系数
    - 敏感性系数定义为：S = ∂ln(目标组分)/∂ln(反应速率常数)
    - 使用有限差分或直接微分方法计算
    
    **结果解释**：
    - **正敏感性系数**：反应促进目标组分的生成
    - **负敏感性系数**：反应抑制目标组分的生成
    - **绝对值大小**：反映敏感性强度
    
    **应用建议**：
    - 关注绝对值较大的反应进行机理验证
    - 分析敏感性系数随工况的变化趋势
    - 结合ROP分析理解反应机理
    """)

# ========================================
# 技术细节
# ========================================

with st.expander("🔧 技术实现细节", expanded=False):
    st.markdown("""
### 敏感性分析技术细节
    
    **梯度计算**：
    - 使用numpy.gradient计算数值梯度
    - 在梯度绝对值最大点评估敏感性
    - 支持多种物理变量作为梯度参考
    
    **数据处理流程**：
    1. 从敏感性工作表中提取数据
    2. 匹配对应的solution数据  
    3. 根据分析模式选择数据点：
       - 梯度最大值模式：计算选定变量的梯度，在梯度最大值处提取敏感性系数
       - 终点模式：直接在各工况点的最后一个时间点提取敏感性系数
    4. 从敏感性列名中提取反应编号并映射到完整反应方程式
    5. 构建敏感性矩阵并排序
    
    **可视化特性**：
    - 正负敏感性系数使用不同线型
    - 支持交互式图表和数据导出
    - 提供详细的hover信息
    
    **性能优化**：
    - 阈值过滤减少计算量
    - 向量化操作提高计算效率
    - 内存优化处理大规模数据
    """)

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>🔥 敏感性分析 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)

