([('Test_Minimal.exe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\build\\test_minimal\\Test_Minimal.exe',
   'EXECUTABLE'),
  ('pyarrow\\arrow_flight.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_compute.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_compute.dll',
   'BINARY'),
  ('pyarrow\\arrow.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow.dll',
   'BINARY'),
  ('pyarrow\\parquet.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\parquet.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_parquet_encryption.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.dll',
   'BINARY'),
  ('pyarrow\\arrow_python_flight.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python_flight.dll',
   'BINARY'),
  ('pyarrow\\arrow_dataset.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_dataset.dll',
   'BINARY'),
  ('pyarrow\\arrow_python.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python.dll',
   'BINARY'),
  ('pyarrow\\arrow_substrait.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_substrait.dll',
   'BINARY'),
  ('pyarrow\\arrow_acero.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_acero.dll',
   'BINARY'),
  ('python310.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\python310.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy.libs\\libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_compute.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_compute.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\markupsafe\\_speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_avif.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\json.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\join.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\index.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_substrait.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_substrait.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_s3fs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_s3fs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet_encryption.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet_encryption.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_parquet.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_orc.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_orc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_json.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_json.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_hdfs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_hdfs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_gcsfs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_gcsfs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_fs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_fs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_flight.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_flight.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet_encryption.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_parquet.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_parquet.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset_orc.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_orc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_dataset.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_csv.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_csv.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_acero.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_acero.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\_feather.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_feather.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pyarrow\\lib.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_fblas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_flapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\cython_lapack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\cython_blas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_decomp_update.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_direct.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_qmc_cy.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_sobol.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_hausdorff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\ndimage\\_nd_image.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\ndimage\\_ni_label.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_interpnd.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_bspl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_ppoly.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_dierckx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\interpolate\\_fitpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_stats_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_mvn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_biasedurn.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\stats\\_stats.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\cython_special.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_voronoi.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_qhull.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\spatial\\_ckdtree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_lsap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_cython_nnls.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_slsqp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_zeros.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_minpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_cobyla.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\optimize\\_group_columns.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\integrate\\_lsoda.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\integrate\\_dop.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\integrate\\_vode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\integrate\\_quadpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\integrate\\_odepack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_gufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_special_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_comb.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_specfun.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_ufuncs.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\_csparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\sparse\\_sparsetools.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\_lib\\_fpumode.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\_lib\\messagestream.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rpds\\rpds.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\rpds\\rpds.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('google\\_upb\\_message.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\google\\_upb\\_message.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'D:\\anaconda3\\envs\\streamlit_env\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('tornado\\speedups.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\tornado\\speedups.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('pyarrow.libs\\msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow.libs\\msvcp140-a118642f3ae8774fb9dc223e15c4a52e.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\anaconda3\\envs\\streamlit_env\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll', 'D:\\anaconda3\\Library\\bin\\liblzma.dll', 'BINARY'),
  ('LIBBZ2.dll', 'D:\\anaconda3\\Library\\bin\\LIBBZ2.dll', 'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi-8.dll', 'D:\\anaconda3\\Library\\bin\\ffi-8.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\anaconda3\\Library\\bin\\sqlite3.dll', 'BINARY'),
  ('scipy\\special\\libsf_error_state.dll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\scipy\\special\\libsf_error_state.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll', 'D:\\anaconda3\\envs\\streamlit_env\\python3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\envs\\streamlit_env\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('plotly-6.2.0.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('plotly-6.2.0.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\METADATA',
   'DATA'),
  ('plotly-6.2.0.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\RECORD',
   'DATA'),
  ('plotly-6.2.0.dist-info\\REQUESTED',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('plotly-6.2.0.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\WHEEL',
   'DATA'),
  ('plotly-6.2.0.dist-info\\entry_points.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\entry_points.txt',
   'DATA'),
  ('plotly-6.2.0.dist-info\\licenses\\LICENSE.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('plotly-6.2.0.dist-info\\top_level.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly-6.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('plotly\\labextension\\package.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\labextension\\package.json',
   'DATA'),
  ('plotly\\labextension\\static\\340.2a23c8275d47a2531dae.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\labextension\\static\\340.2a23c8275d47a2531dae.js',
   'DATA'),
  ('plotly\\labextension\\static\\remoteEntry.5153b2c003c011c482e3.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\labextension\\static\\remoteEntry.5153b2c003c011c482e3.js',
   'DATA'),
  ('plotly\\labextension\\static\\style.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\labextension\\static\\style.js',
   'DATA'),
  ('plotly\\package_data\\datasets\\carshare.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\carshare.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\election.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\election.geojson.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\election.geojson.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\experiment.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\experiment.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\gapminder.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\gapminder.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\iris.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\iris.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\medals.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\medals.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\stocks.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\stocks.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\tips.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\tips.csv.gz',
   'DATA'),
  ('plotly\\package_data\\datasets\\wind.csv.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\datasets\\wind.csv.gz',
   'DATA'),
  ('plotly\\package_data\\plotly.min.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\plotly.min.js',
   'DATA'),
  ('plotly\\package_data\\templates\\ggplot2.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\ggplot2.json',
   'DATA'),
  ('plotly\\package_data\\templates\\gridon.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\gridon.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\plotly.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_dark.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\plotly_dark.json',
   'DATA'),
  ('plotly\\package_data\\templates\\plotly_white.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\plotly_white.json',
   'DATA'),
  ('plotly\\package_data\\templates\\presentation.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\presentation.json',
   'DATA'),
  ('plotly\\package_data\\templates\\seaborn.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\seaborn.json',
   'DATA'),
  ('plotly\\package_data\\templates\\simple_white.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\simple_white.json',
   'DATA'),
  ('plotly\\package_data\\templates\\xgridoff.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\xgridoff.json',
   'DATA'),
  ('plotly\\package_data\\templates\\ygridoff.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\templates\\ygridoff.json',
   'DATA'),
  ('plotly\\package_data\\widgetbundle.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\package_data\\widgetbundle.js',
   'DATA'),
  ('plotly\\validators\\_validators.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\plotly\\validators\\_validators.json',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\INSTALLER',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\METADATA',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\RECORD',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\REQUESTED',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\REQUESTED',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\WHEEL',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\entry_points.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\entry_points.txt',
   'DATA'),
  ('streamlit-1.47.0.dist-info\\top_level.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit-1.47.0.dist-info\\top_level.txt',
   'DATA'),
  ('streamlit\\proto\\Alert_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Alert_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\AppPage_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\AppPage_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ArrowNamedDataSet_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ArrowNamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ArrowVegaLiteChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ArrowVegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Arrow_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Arrow_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\AudioInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\AudioInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Audio_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Audio_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\AuthRedirect_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\AuthRedirect_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\AutoRerun_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\AutoRerun_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\BackMsg_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\BackMsg_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Balloons_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Balloons_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Block_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Block_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\BokehChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\BokehChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ButtonGroup_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ButtonGroup_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Button_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Button_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\CameraInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\CameraInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ChatInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ChatInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Checkbox_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Checkbox_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ClientState_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ClientState_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Code_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Code_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ColorPicker_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ColorPicker_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Common_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Common_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Components_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Components_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\DataFrame_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\DataFrame_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\DateInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\DateInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\DeckGlJsonChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\DeckGlJsonChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Delta_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Delta_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\DocString_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\DocString_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\DownloadButton_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\DownloadButton_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Element_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Element_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Empty_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Empty_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Exception_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Exception_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Favicon_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Favicon_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\FileUploader_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\FileUploader_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ForwardMsg_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ForwardMsg_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\GapSize_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\GapSize_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\GitInfo_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\GitInfo_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\GraphVizChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\GraphVizChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Heading_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Heading_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\HeightConfig_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\HeightConfig_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Html_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Html_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\IFrame_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\IFrame_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Image_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Image_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Json_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Json_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\LabelVisibilityMessage_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\LabelVisibilityMessage_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\LinkButton_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\LinkButton_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Logo_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Logo_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Markdown_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Markdown_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Metric_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Metric_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\MetricsEvent_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\MetricsEvent_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\MultiSelect_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\MultiSelect_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\NamedDataSet_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\NamedDataSet_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Navigation_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Navigation_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\NewSession_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\NewSession_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\NumberInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\NumberInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PageConfig_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PageConfig_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PageInfo_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PageInfo_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PageLink_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PageLink_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PageNotFound_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PageNotFound_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PageProfile_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PageProfile_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PagesChanged_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PagesChanged_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\ParentMessage_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\ParentMessage_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\PlotlyChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\PlotlyChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Progress_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Progress_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Radio_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Radio_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\RootContainer_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\RootContainer_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Selectbox_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Selectbox_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\SessionEvent_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\SessionEvent_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\SessionStatus_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\SessionStatus_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Skeleton_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Skeleton_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Slider_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Slider_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Snow_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Snow_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Spinner_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Spinner_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\TextArea_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\TextArea_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\TextInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\TextInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Text_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Text_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\TimeInput_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\TimeInput_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Toast_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Toast_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\VegaLiteChart_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\VegaLiteChart_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\Video_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\Video_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\WidgetStates_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\WidgetStates_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\WidthConfig_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\WidthConfig_pb2.pyi',
   'DATA'),
  ('streamlit\\proto\\openmetrics_data_model_pb2.pyi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\proto\\openmetrics_data_model_pb2.pyi',
   'DATA'),
  ('streamlit\\py.typed',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\py.typed',
   'DATA'),
  ('streamlit\\static\\favicon.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\favicon.png',
   'DATA'),
  ('streamlit\\static\\index.html',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\index.html',
   'DATA'),
  ('streamlit\\static\\manifest.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\manifest.json',
   'DATA'),
  ('streamlit\\static\\static\\css\\index.CJVRHjQZ.css',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\css\\index.CJVRHjQZ.css',
   'DATA'),
  ('streamlit\\static\\static\\css\\index.CsLB_Bnz.css',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\css\\index.CsLB_Bnz.css',
   'DATA'),
  ('streamlit\\static\\static\\css\\index.DzuxGC_t.css',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\css\\index.DzuxGC_t.css',
   'DATA'),
  ('streamlit\\static\\static\\js\\ErrorOutline.esm.bg5MMAri.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\ErrorOutline.esm.bg5MMAri.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\FileDownload.esm.ZD5PIl3I.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\FileDownload.esm.ZD5PIl3I.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\FileHelper.B6jszxAn.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\FileHelper.B6jszxAn.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\FormClearHelper.DuzI-rET.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\FormClearHelper.DuzI-rET.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\Hooks.qKXFrFZm.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\Hooks.qKXFrFZm.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\InputInstructions.7NdlV9ze.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\InputInstructions.7NdlV9ze.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\ProgressBar.d6X6RRog.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\ProgressBar.d6X6RRog.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\RenderInPortalIfExists.DEmedSWH.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\RenderInPortalIfExists.DEmedSWH.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\Toolbar.D8wuuqZd.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\Toolbar.D8wuuqZd.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\UploadFileInfo.C-jY39rj.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\UploadFileInfo.C-jY39rj.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\base-input.Dl5fJ2xw.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\base-input.Dl5fJ2xw.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\checkbox.DCoT7yTn.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\checkbox.DCoT7yTn.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\createDownloadLinkElement.DZMwyjvU.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\createDownloadLinkElement.DZMwyjvU.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\createSuper.DIDXPRra.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\createSuper.DIDXPRra.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\data-grid-overlay-editor.Bxxjm6-o.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\data-grid-overlay-editor.Bxxjm6-o.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\downloader.DKv2kGmf.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\downloader.DKv2kGmf.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\es6.JcohG4tM.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\es6.JcohG4tM.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\iframeResizer.contentWindow.3rtAterA.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\iframeResizer.contentWindow.3rtAterA.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.0dTVhOfX.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.0dTVhOfX.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.4yEdNndV.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.4yEdNndV.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.5n6QoSUA.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.5n6QoSUA.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.6nngkCAa.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.6nngkCAa.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.94f3Fw7s.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.94f3Fw7s.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.B4UgLnfD.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.B4UgLnfD.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.B61tWuMK.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.B61tWuMK.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.B7u9Flh8.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.B7u9Flh8.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BASsihSP.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BASsihSP.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BL9HK8jG.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BL9HK8jG.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BO22lkaO.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BO22lkaO.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BQWRZ6al.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BQWRZ6al.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BQXddtYi.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BQXddtYi.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BTGIlECR.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BTGIlECR.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BUnHfDxu.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BUnHfDxu.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BVTVGhCW.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BVTVGhCW.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BWQlvt-p.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BWQlvt-p.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BhB2h-IN.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BhB2h-IN.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BjbAdGl1.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BjbAdGl1.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BlIoUPom.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BlIoUPom.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BqMXJWvY.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BqMXJWvY.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.Brmdn6cI.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.Brmdn6cI.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.BteAxTUz.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.BteAxTUz.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.ByH19tU7.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.ByH19tU7.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.CH_JROvL.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.CH_JROvL.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.CRMaL4_D.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.CRMaL4_D.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.CitqAWcf.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.CitqAWcf.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.CnNERm7h.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.CnNERm7h.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.DBhELRFO.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.DBhELRFO.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.DNTjV0h_.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.DNTjV0h_.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.Df2TZ_h-.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.Df2TZ_h-.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.DmB_1wAI.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.DmB_1wAI.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.GID7Wiqx.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.GID7Wiqx.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.Wj4EUfiO.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.Wj4EUfiO.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.hprQgsHY.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.hprQgsHY.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.mJxNfDOA.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.mJxNfDOA.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\index.qkxYEW65.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\index.qkxYEW65.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\input.vCGI-j0z.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\input.vCGI-j0z.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\inputUtils.CptNuJwn.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\inputUtils.CptNuJwn.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\memory.CAr59PeX.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\memory.CAr59PeX.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\mergeWith.KUX-f18q.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\mergeWith.KUX-f18q.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\number-overlay-editor.7_qeQYbF.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\number-overlay-editor.7_qeQYbF.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\possibleConstructorReturn.ChgdWjjy.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\possibleConstructorReturn.ChgdWjjy.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\sandbox.6mITyM_H.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\sandbox.6mITyM_H.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\sprintf.D7DtBTRn.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\sprintf.D7DtBTRn.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\threshold.DjX0wlsa.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\threshold.DjX0wlsa.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\timepicker.BlKW9Kob.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\timepicker.BlKW9Kob.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\timer.CAwTRJ_g.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\timer.CAwTRJ_g.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\toConsumableArray.DkNUwUhg.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\toConsumableArray.DkNUwUhg.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\uniqueId.BW6icrs1.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\uniqueId.BW6icrs1.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\useBasicWidgetState.oPxte5Mj.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\useBasicWidgetState.oPxte5Mj.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\useOnInputChange.CBaDeGVQ.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\useOnInputChange.CBaDeGVQ.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\useTextInputAutoExpand.s7ZXp9QC.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\useTextInputAutoExpand.s7ZXp9QC.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\value.CgPGBV_l.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\value.CgPGBV_l.js',
   'DATA'),
  ('streamlit\\static\\static\\js\\withFullScreenWrapper.DkugyWpW.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\js\\withFullScreenWrapper.DkugyWpW.js',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_AMS-Regular.BQhdFMY1.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_AMS-Regular.BQhdFMY1.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_AMS-Regular.DMm9YOAa.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_AMS-Regular.DMm9YOAa.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_AMS-Regular.DRggAlZN.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_AMS-Regular.DRggAlZN.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.ATXxdsX0.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.BEiXGLvX.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Bold.Dq_IR9rO.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.CTRA-rTL.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.Di6jR-x-.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Caligraphic-Regular.wX97UBjC.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.BdnERNNW.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.BsDP51OF.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.BsDP51OF.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Bold.CL6g_b3V.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.CB_wures.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.CB_wures.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.CTYiF6lA.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Fraktur-Regular.Dxdc4cR9.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Bold.Cx986IdX.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Bold.Cx986IdX.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Bold.Jm3AIy58.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Bold.Jm3AIy58.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Bold.waoOVXN0.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Bold.waoOVXN0.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.DxDJ3AOS.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.DzxPMmG6.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.SpSLRI95.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-BoldItalic.SpSLRI95.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Italic.3WenGoN9.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Italic.3WenGoN9.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Italic.BMLOBm91.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Italic.BMLOBm91.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Italic.NWA7e6Wa.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Italic.NWA7e6Wa.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Regular.B22Nviop.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Regular.B22Nviop.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Regular.Dr94JaBh.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Regular.Dr94JaBh.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Main-Regular.ypZvNtVU.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Main-Regular.ypZvNtVU.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.B3XSjfu4.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.CZnvNsCZ.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-BoldItalic.iY-2wyZ7.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-Italic.DA0__PXp.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-Italic.DA0__PXp.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-Italic.flOr_0UB.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-Italic.flOr_0UB.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Math-Italic.t53AETM-.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Math-Italic.t53AETM-.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.CFMepnvq.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.D1sUS0GD.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Bold.DbIhKOiC.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.C3H0VqGB.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.DN2j7dab.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.DN2j7dab.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Italic.YYjJ1zSn.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.BNo7hRIc.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.CS6fqUqJ.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_SansSerif-Regular.DDBCnlJ7.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Script-Regular.C5JkGWo-.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Script-Regular.C5JkGWo-.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Script-Regular.D3wIWfF6.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Script-Regular.D3wIWfF6.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Script-Regular.D5yQViql.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Script-Regular.D5yQViql.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size1-Regular.C195tn64.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size1-Regular.C195tn64.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size1-Regular.Dbsnue_I.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size1-Regular.Dbsnue_I.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size1-Regular.mCD8mA8B.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size1-Regular.mCD8mA8B.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size2-Regular.B7gKUWhC.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size2-Regular.B7gKUWhC.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size2-Regular.Dy4dx90m.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size2-Regular.Dy4dx90m.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size2-Regular.oD1tc_U0.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size2-Regular.oD1tc_U0.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size3-Regular.CTq5MqoE.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size3-Regular.CTq5MqoE.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size3-Regular.DgpXs0kz.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size3-Regular.DgpXs0kz.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size4-Regular.BF-4gkZK.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size4-Regular.BF-4gkZK.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size4-Regular.DWFBv043.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size4-Regular.DWFBv043.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Size4-Regular.Dl5lxZxV.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.C0xS9mPB.woff',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.CO6r4hn1.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\KaTeX_Typewriter-Regular.D3Ib7_Hf.ttf',
   'DATA'),
  ('streamlit\\static\\static\\media\\MaterialSymbols-Rounded.DsbC8sYI.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\MaterialSymbols-Rounded.DsbC8sYI.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceCodeVF-Italic.ttf.Ba1oaZG1.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceCodeVF-Upright.ttf.BjWn63N-.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceSansVF-Italic.ttf.Bt9VkdQ3.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceSansVF-Upright.ttf.BsWL4Kly.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceSerifVariable-Italic.ttf.CVdzAtxO.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\SourceSerifVariable-Roman.ttf.mdpVL9bi.woff2',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-0.Czj7AKwE.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-0.Czj7AKwE.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-1.CNvFFrND.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-1.CNvFFrND.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-2.DTvC6B1t.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-2.DTvC6B1t.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-3.CgSk4tbL.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-3.CgSk4tbL.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-4.mbtFrzxf.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-4.mbtFrzxf.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\balloon-5.CSwkUfRA.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\balloon-5.CSwkUfRA.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\fireworks.B4d-_KUe.gif',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\fireworks.B4d-_KUe.gif',
   'DATA'),
  ('streamlit\\static\\static\\media\\flake-0.DgWaVvm5.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\flake-0.DgWaVvm5.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\flake-1.B2r5AHMK.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\flake-1.B2r5AHMK.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\flake-2.BnWSExPC.png',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\flake-2.BnWSExPC.png',
   'DATA'),
  ('streamlit\\static\\static\\media\\snowflake.JU2jBHL8.svg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\streamlit\\static\\static\\media\\snowflake.JU2jBHL8.svg',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\tensorflow\\convert.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\benchmark.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\decimal.h',
   'DATA'),
  ('pyarrow\\_hdfs.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_hdfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_ops.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\concurrency.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\concurrency.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\adapter.h',
   'DATA'),
  ('pyarrow\\_feather.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_feather.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_middleware.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logging.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\string-to-double.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\thread_pool.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\thread_pool.h',
   'DATA'),
  ('pyarrow\\tests\\pyarrow_cython_example.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\pyarrow_cython_example.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\test_common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_data_inline.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_data_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\type_fwd.h',
   'DATA'),
  ('pyarrow\\_csv.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_csv.pxd',
   'DATA'),
  ('pyarrow\\tensor.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tensor.pxi',
   'DATA'),
  ('pyarrow\\_compute.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_compute.pyx',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.orc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.orc',
   'DATA'),
  ('pyarrow\\lib_api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\discovery.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\discovery.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\file_reader.h',
   'DATA'),
  ('pyarrow\\public-api.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\public-api.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compression.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\compression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\test_nodes.h',
   'DATA'),
  ('pyarrow\\_dlpack.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dlpack.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.cc',
   'DATA'),
  ('pyarrow\\array.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\array.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function_options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function_options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\hdfs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-conversion.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\buffer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\status.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\status.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\writer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison_inc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison_inc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\io_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\io_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\schema.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\schema.h',
   'DATA'),
  ('pyarrow\\arrow_dataset.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_dataset.lib',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_in_memory_kms.h',
   'DATA'),
  ('pyarrow\\lib.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\lib.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\buffer_builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\buffer_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\future_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\future_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_primitive.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\simd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\simd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_builders.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\from_string.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\from_string.h',
   'DATA'),
  ('pyarrow\\gandiva.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\gandiva.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\relation.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_dict.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_dict.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\ipc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\parallel.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\parallel.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3fs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\dictionary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\serde.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\platform.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\platform.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.cc',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.some-named-index.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\json.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\json.h',
   'DATA'),
  ('pyarrow\\_cuda.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_cuda.pxd',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.orc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\local_wrap_kms_client.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\validate.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\validate.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_convert.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\queue.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\queue.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\rapidjson_defs.h',
   'DATA'),
  ('pyarrow\\lib.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\lib.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\strptime.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\strptime.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_time.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_time.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\benchmark_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\math_constants.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\math_constants.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\device.h',
   'DATA'),
  ('pyarrow\\io.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\io.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\s3_test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\converter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\extension_type.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\types.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\types.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_encryption_key.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\function.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\function.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_acero.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\registry.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\registry.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\ieee.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\interfaces.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\interfaces.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\page_index.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\page_index.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\file_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\file_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\stdio.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\stdio.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\byte_size.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\byte_size.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_definitions.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\partition_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\partition_util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\size_statistics.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\size_statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_test.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.cc',
   'DATA'),
  ('pyarrow\\_s3fs.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_s3fs.pyx',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.all-named-index.parquet',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\helpers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\column_writer.h',
   'DATA'),
  ('pyarrow\\config.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\config.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunk_resolver.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\chunk_resolver.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\tpch_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\invalid_row.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\fixed_shape_tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\helpers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\ordering.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\ordering.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\mman.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\mman.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\api.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_python.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_python.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_union.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_union.h',
   'DATA'),
  ('pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\feather\\v0.17.0.version.2-compression.lz4.feather',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\crypto_factory.h',
   'DATA'),
  ('pyarrow\\_substrait.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_substrait.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\datetime.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\stream_writer.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\table.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\table.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\pythoncapi_compat.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types_async.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types_async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\basic_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\udf.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\regex.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\regex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\util.h',
   'DATA'),
  ('pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\parquet\\v0.7.1.column-metadata-handling.parquet',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\sparse_tensor.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\sparse_tensor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\csv.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\uri.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\uri.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\result.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\result.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\double-to-string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\memory.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\memory.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_types.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\parser.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\parser.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\utf8.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\utf8.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\functional.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\functional.h',
   'DATA'),
  ('pyarrow\\scalar.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\scalar.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hashing.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\hashing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\macros.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\macros.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\list_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\list_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\types.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\types.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_traits.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\feather.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\feather.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_aggregate.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_scanner.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\column_scanner.h',
   'DATA'),
  ('pyarrow\\includes\\common.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\common.pxd',
   'DATA'),
  ('pyarrow\\benchmark.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\benchmark.pxi',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_to_arrow.cc',
   'DATA'),
  ('pyarrow\\_cuda.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_cuda.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\matchers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\matchers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\future.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\future.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_init.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\mockfs.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\statistics.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_type_inline.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_type_inline.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\decimal.jsn.gz',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_fixup.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\dlpack_abi.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\date.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\util.h',
   'DATA'),
  ('pyarrow\\_flight.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_flight.pyx',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cpu_info.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\cpu_info.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_unwrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\ios.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_util.h',
   'DATA'),
  ('pyarrow\\_orc.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_orc.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\datum.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\datum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\type_fwd.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.emptyFile.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\visibility.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_fs.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_fs.pxd',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\benchmark.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\benchmark.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\concatenate.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\concatenate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset_writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\formatting.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\formatting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_visit.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\caching.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\caching.h',
   'DATA'),
  ('pyarrow\\tests\\bound_function_visit_strings.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\bound_function_visit_strings.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\config.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\config.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\stream_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\stream_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem_library.h',
   'DATA'),
  ('pyarrow\\pandas-shim.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\pandas-shim.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\mutex.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\mutex.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\union_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\union_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\options.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_python_internal.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\filesystem.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_feather.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_feather.pxd',
   'DATA'),
  ('pyarrow\\include\\parquet\\hasher.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\hasher.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet_encryption.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_parquet_encryption.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\map_node.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\map_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_init.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_init.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\io.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_base.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_base.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_material.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_material.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\asof_join_node.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_toolkit.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_substrait.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_substrait.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz_private.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\transform.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\transform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\tz.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_binary.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_binary.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\windows_compatibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_auth.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_auth.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\gtest_util.h',
   'DATA'),
  ('pyarrow\\_dataset.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\row\\grouper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\async_test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\parquet_encryption.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_plan_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_cookie_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\float16.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\float16.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\span.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\span.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\diff.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\diff.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\compressed.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\compressed.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\fixed_width_test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_iterator.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\stl_iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\range.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\range.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_block_counter.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\parquet_encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\ProducerConsumerQueue.h',
   'DATA'),
  ('pyarrow\\arrow_python_parquet_encryption.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python_parquet_encryption.lib',
   'DATA'),
  ('pyarrow\\_csv.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_csv.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visitor.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_parquet.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\opaque.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\opaque.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\exec_plan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\query_context.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\query_context.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\plan.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\plan.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_comparison.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\level_comparison.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encoding.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encoding.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_base.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_base.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_nested.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\path_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\logger.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\logger.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.cc',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\client_tracing_middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\random.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\binary_view_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\pretty_print.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\pretty_print.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl_allocator.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\stl_allocator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\exec.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\exec.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\delimiting.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\delimiting.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_scalar_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor\\converter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\tensor\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_orc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\adapters\\orc\\options.h',
   'DATA'),
  ('pyarrow\\lib.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_uint128.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\generator.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\generator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util_overflow.h',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_random.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\dataset.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\dataset.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\util.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\column_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\uuid.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\uuid.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\memory_pool_test.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\memory_pool_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\chunked_array.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\chunked_array.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\projector.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\projector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\diy-fp.h',
   'DATA'),
  ('pyarrow\\_acero.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_acero.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_decoder.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\key_metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\file.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\file.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\accumulation_queue.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\two_level_cache_with_expiration.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.testDate1900.orc',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension_type.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\test_common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\debug.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\debug.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\visit_array_inline.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visit_array_inline.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\buffered.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\buffered.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\executor_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\executor_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\azurefs.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\partition.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\partition.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\value_parsing.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\value_parsing.h',
   'DATA'),
  ('pyarrow\\includes\\libparquet_encryption.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libparquet_encryption.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\column_builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\column_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\checked_cast.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\checked_cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\order_by_impl.h',
   'DATA'),
  ('pyarrow\\memory.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\memory.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\io.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\io.h',
   'DATA'),
  ('pyarrow\\_json.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_json.pyx',
   'DATA'),
  ('pyarrow\\arrow_acero.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_acero.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\transport_server.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\transport_server.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\schema.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\schema.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fast-dtoa.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\test_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\compare.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_fwd.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\time_series_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_binary.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_binary.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\debug-trap.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\async.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\type_traits.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\task_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\task_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\column_page.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\column_page.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\inference.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\server_auth.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\server_auth.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_flight_server.h',
   'DATA'),
  ('pyarrow\\compat.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\compat.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\converter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\converter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunker.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\extension_type.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\align_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\align_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_dict.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_dict.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\process.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\process.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\otel_logging.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\async.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\async.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_primitive.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_primitive.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_decimal.h',
   'DATA'),
  ('pyarrow\\includes\\__init__.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\chunked_builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_reader.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\aggregate_node.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\cancel.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\cancel.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\properties.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\properties.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ree_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\ree_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\api.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\iterators.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_parquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_json.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_json.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_fixup.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\statistics.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\statistics.h',
   'DATA'),
  ('pyarrow\\arrow.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\strtod.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\device_allocation_type_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\iterators.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\iterators.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\windows_compatibility.h',
   'DATA'),
  ('pyarrow\\__init__.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\__init__.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\launder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\launder.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\extension_type.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\extension_type.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compare.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compare.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\portable-snippets\\safe-math.h',
   'DATA'),
  ('pyarrow\\_fs.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_fs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\type_traits.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\middleware.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\middleware.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_ipc.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\python_to_arrow.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\tensor.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\tensor.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\vendored\\CMakeLists.txt',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\scanner.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\scanner.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\visibility.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\decimal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\decimal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\level_conversion_inc.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\level_conversion_inc.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\type_traits.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\type_traits.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\test_common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\test_common.h',
   'DATA'),
  ('pyarrow\\_fs.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_fs.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\visitor_generate.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\visitor_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_nested.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_nested.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\string_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\string_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\helpers.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\helpers.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_run_end.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\math.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\math.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\schema_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\schema_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\filesystem.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\filesystem.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\kms_client_factory.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\fixed-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\pyarrow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\pyarrow.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\arrow_to_pandas.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\TestOrcFile.test1.jsn.gz',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\utils.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\bloom_filter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\bloom_filter.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\endian.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\endian.h',
   'DATA'),
  ('pyarrow\\tests\\extensions.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\extensions.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\parquet_encryption_config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_convert.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\schema.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\schema.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\cached-powers.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\record_batch.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\record_batch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\chunker.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\chunker.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_wrapper.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\stl.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\stl.h',
   'DATA'),
  ('pyarrow\\_parquet.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet.pxd',
   'DATA'),
  ('pyarrow\\arrow_flight.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\pcg_random.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\pcg_random.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\platform.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_auth_handlers.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\platform.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\platform.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\prefetch.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\prefetch.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\test_encryption_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime\\visibility.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\bridge.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\bridge.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\kernel.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\kernel.h',
   'DATA'),
  ('pyarrow\\arrow_python_flight.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python_flight.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\gdb.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\numpy_internal.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\csv\\converter.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\csv\\converter.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\ipc.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\ipc.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\test_common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\encryption.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\encryption.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\builder_adaptive.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\reader.h',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\tracing.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\tracing.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_generate.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\type.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\type.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.cc',
   'DATA'),
  ('pyarrow\\includes\\libgandiva.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libgandiva.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\object_parser.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\object_parser.h',
   'DATA'),
  ('pyarrow\\includes\\libparquet.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libparquet.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\config.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\config.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\api_vector.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\api_vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\iterator.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\iterator.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table_builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\table_builder.h',
   'DATA'),
  ('pyarrow\\_gcsfs.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_gcsfs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\reader.h',
   'DATA'),
  ('pyarrow\\_compute.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_compute.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\lib_api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\lib_api.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\geospatial\\statistics.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\geospatial\\statistics.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\flight.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\flight.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_run_reader.h',
   'DATA'),
  ('pyarrow\\ipc.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\ipc.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\hdfs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\hdfs.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\dataset\\file_base.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\dataset\\file_base.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\reader.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\reader.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\xxhasher.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\xxhasher.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\test_common.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\test_common.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\options.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bitmap_writer.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\api\\writer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\api\\writer.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\backpressure_handler.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\csv.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\csv.h',
   'DATA'),
  ('pyarrow\\types.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\types.pxi',
   'DATA'),
  ('pyarrow\\_dataset.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset.pyx',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_system_key_material_store.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\benchmark_util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\udf.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\udf.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\crc32.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\crc32.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\exception.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\exception.h',
   'DATA'),
  ('pyarrow\\_parquet.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet.pyx',
   'DATA'),
  ('pyarrow\\_pyarrow_cpp_tests.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_pyarrow_cpp_tests.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\aligned_storage.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\util.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\util.cc',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\concurrent_map.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\key_value_metadata.h',
   'DATA'),
  ('pyarrow\\_azurefs.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_azurefs.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\parser.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\parser.h',
   'DATA'),
  ('pyarrow\\arrow_compute.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_compute.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\time.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\time.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\pcg\\pcg_extras.hpp',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\bit_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\bit_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\io\\slow.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\io\\slow.h',
   'DATA'),
  ('pyarrow\\_dataset_parquet.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_parquet.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\base64.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\base64.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\testing\\uniform_real.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\util.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\python_test.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\python_test.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\inference.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\inference.h',
   'DATA'),
  ('pyarrow\\_dataset_orc.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_dataset_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\async_generator_fwd.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\acero\\hash_join.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\acero\\hash_join.h',
   'DATA'),
  ('pyarrow\\_json.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_json.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\flight\\test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\flight\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\ipc\\message.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\ipc\\message.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\scalar.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\scalar.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\table.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\table.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_cuda.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_cuda.pxd',
   'DATA'),
  ('pyarrow\\_orc.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_orc.pyx',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\gcsfs.h',
   'DATA'),
  ('pyarrow\\tests\\data\\orc\\README.md',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\tests\\data\\orc\\README.md',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\unreachable.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\unreachable.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\xxhash.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\metadata.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\metadata.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\pyarrow_lib.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\array_run_end.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\array_run_end.h',
   'DATA'),
  ('pyarrow\\device.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\device.pxi',
   'DATA'),
  ('pyarrow\\include\\arrow\\builder.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\builder.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\engine\\substrait\\extension_set.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\rows_to_batches.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\algorithm.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\algorithm.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\cast.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\cast.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\json\\api.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\json\\api.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\int_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\int_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\secure_string.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\secure_string.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\array\\data.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\array\\data.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\arrow\\test_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\arrow\\test_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\ubsan.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\ubsan.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\vector.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\vector.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\numpy_interop.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\parquet_version.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\parquet_version.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\printer.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\printer.h',
   'DATA'),
  ('pyarrow\\arrow_python.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_python.lib',
   'DATA'),
  ('pyarrow\\arrow_substrait.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\arrow_substrait.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\double-conversion\\bignum-dtoa.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\filesystem\\localfs.h',
   'DATA'),
  ('pyarrow\\builder.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\builder.pxi',
   'DATA'),
  ('pyarrow\\include\\parquet\\type_fwd.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\type_fwd.h',
   'DATA'),
  ('pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\parquet\\encryption\\file_key_material_store.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\flight.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\flight.cc',
   'DATA'),
  ('pyarrow\\includes\\libarrow_dataset.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_dataset.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\hash_util.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\hash_util.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\expression.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\expression.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\python\\io.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\python\\io.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\c\\abi.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\c\\abi.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\platform.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\platform.h',
   'DATA'),
  ('pyarrow\\_acero.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_acero.pxd',
   'DATA'),
  ('pyarrow\\include\\arrow\\vendored\\datetime.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\vendored\\datetime.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\datetime.cc',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\datetime.cc',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\task_group.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\task_group.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\util\\small_vector.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\util\\small_vector.h',
   'DATA'),
  ('pyarrow\\includes\\libarrow_flight.pxd',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\includes\\libarrow_flight.pxd',
   'DATA'),
  ('pyarrow\\parquet.lib',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\parquet.lib',
   'DATA'),
  ('pyarrow\\include\\arrow\\extension\\bool8.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\extension\\bool8.h',
   'DATA'),
  ('pyarrow\\include\\arrow\\compute\\initialize.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\include\\arrow\\compute\\initialize.h',
   'DATA'),
  ('pyarrow\\src\\arrow\\python\\gdb.h',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\src\\arrow\\python\\gdb.h',
   'DATA'),
  ('pyarrow\\error.pxi',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\error.pxi',
   'DATA'),
  ('pyarrow\\_parquet_encryption.pyx',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\pyarrow\\_parquet_encryption.pyx',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('typing_extensions-4.14.1.dist-info\\licenses\\LICENSE',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\typing_extensions-4.14.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('typing_extensions-4.14.1.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\typing_extensions-4.14.1.dist-info\\RECORD',
   'DATA'),
  ('typing_extensions-4.14.1.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\typing_extensions-4.14.1.dist-info\\METADATA',
   'DATA'),
  ('typing_extensions-4.14.1.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\typing_extensions-4.14.1.dist-info\\INSTALLER',
   'DATA'),
  ('typing_extensions-4.14.1.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\typing_extensions-4.14.1.dist-info\\WHEEL',
   'DATA'),
  ('altair\\jupyter\\js\\index.js',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\altair\\jupyter\\js\\index.js',
   'DATA'),
  ('altair\\jupyter\\js\\README.md',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\altair\\jupyter\\js\\README.md',
   'DATA'),
  ('altair\\vegalite\\v5\\schema\\vega-themes.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\altair\\vegalite\\v5\\schema\\vega-themes.json',
   'DATA'),
  ('altair\\py.typed',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\altair\\py.typed',
   'DATA'),
  ('altair\\vegalite\\v5\\schema\\vega-lite-schema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\altair\\vegalite\\v5\\schema\\vega-lite-schema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft6\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft6\\metaschema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\validation',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\validation',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\applicator',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\applicator',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\vocabularies\\applicator',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies\\applicator',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\metaschema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\vocabularies\\core',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies\\core',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\vocabularies\\content',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies\\content',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format-assertion',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format-assertion',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft4\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft4\\metaschema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\vocabularies\\validation',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies\\validation',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\content',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\content',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft3\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft3\\metaschema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format-annotation',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format-annotation',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\metaschema.json',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\unevaluated',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\unevaluated',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft201909\\vocabularies\\meta-data',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft201909\\vocabularies\\meta-data',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\meta-data',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\meta-data',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\core',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\core',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft202012\\vocabularies\\format',
   'DATA'),
  ('jsonschema_specifications\\schemas\\draft7\\metaschema.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema_specifications\\schemas\\draft7\\metaschema.json',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\INSTALLER',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\RECORD',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\WHEEL',
   'DATA'),
  ('jsonschema\\benchmarks\\issue232\\issue.json',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema\\benchmarks\\issue232\\issue.json',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\licenses\\COPYING',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\licenses\\COPYING',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\METADATA',
   'DATA'),
  ('jsonschema-4.25.0.dist-info\\entry_points.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\jsonschema-4.25.0.dist-info\\entry_points.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\packaging_env\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('base_library.zip',
   'D:\\BaiduSyncdisk\\py\\NEW_ROP\\build\\test_minimal\\base_library.zip',
   'DATA')],)
