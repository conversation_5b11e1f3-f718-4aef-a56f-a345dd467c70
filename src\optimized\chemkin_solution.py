"""
优化的CHEMKIN文件处理器
基于原始ckfile类，但进行了结构优化和功能拆分
"""
import copy
import numpy as np
import pandas as pd
import streamlit as st
from tqdm import tqdm


class ChemkinSolution:
    """优化的CHEMKIN文件处理器"""
    
    def __init__(self, input_file):
        """
        初始化CHEMKIN文件处理器
        
        Args:
            input_file: CHEMKIN结果文件路径或文件对象
        """
        self.xlsx = pd.ExcelFile(input_file)
        self.soln_sheets = None
        self.end_point_sheets = None
        self.rop_sheets = None
        self.max_sheets = None
        self.sensitivity_sheets = None
        self.variables = None
        self.num_variable = 0
        self.variable_name = None
        self.variable_unit = None
        
        # 功能标志
        self.temperature_exist = None
        self.net_reaction_rate_exist = False
        self.mole_fraction_exist = False
        self.rop_line = False
        self.sensitivity_exist = False
        self.preprocessing = False
        self.ROP_exist = False
        self.need_extract_endpoints = False  # 是否需要从solution sheets提取端点数据
    
    def load_chemkin_file(self):
        """加载和分析CHEMKIN文件结构"""
        self._analyze_sheet_structure()
        self._extract_variables()
        self._check_data_availability()
        self.preprocessing = True
        
        print(f'导入文件成功\n 导入文件覆盖工况点包括：{self.variable_name}:{self.variables} ({self.variable_unit})')
        st.info(f'导入文件覆盖工况点包括：{self.variable_name} {self.variables} {self.variable_unit}', icon="🤖")
    
    def _analyze_sheet_structure(self):
        """分析工作表结构"""
        sheets = self.xlsx.sheet_names
        self.soln_sheets = [sheet for sheet in sheets if 'soln' in sheet]
        self.end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
        self.rop_sheets = [sheet for sheet in sheets if 'rop' in sheet]
        self.max_sheets = [sheet for sheet in sheets if 'max_point' in sheet]
        self.sensitivity_sheets = [sheet for sheet in sheets if 'sen' in sheet]
    
    def _extract_variables(self):
        """提取变量信息"""
        if len(self.end_point_sheets) != 0:
            self._extract_from_endpoint()
        else:
            self._extract_from_solution()
    
    def _extract_from_endpoint(self):
        """从endpoint工作表提取变量"""
        endPointsI = self.end_point_sheets[0]
        endPoints = self.xlsx.parse(endPointsI.rstrip())
        
        import re
        varible_columns = [re.search('.*_C1_.*', name) for name in endPoints.columns
                          if re.search('.*_C1_.*', name) is not None]
        
        self.num_variable = 0
        for varible_column in varible_columns:
            potential_variable = list(endPoints.loc[:, varible_column[0]])
            if len(potential_variable) != 1 and len(np.unique(potential_variable)) == 1:
                pass
            else:
                self.variables = potential_variable
                self.num_variable += 1
        
        if self.num_variable > 1:
            print("注意！当前输出结果有不止一个变量，最好分开输出")
        
        self.variable_name = varible_column[0].strip().split('_')[0]
        self.variable_unit = varible_column[0].strip().split('_')[-1]
    
    def _extract_from_solution(self):
        """从solution工作表提取变量（多点情况，没有end_point sheets）"""
        print("检测到没有end_point sheets，将从solution sheets提取端点数据")
        
        # 从soln_sheets中提取工况信息
        if len(self.soln_sheets) == 0:
            print("警告：没有找到solution sheets")
            self.variables = ['unknown']
            self.variable_name = 'unknown'
            self.variable_unit = 'unknown'
            return
        
        # 分析solution sheets的工况数量
        conditions = []
        for sheet in self.soln_sheets:
            if '#' in sheet:
                condition_id = sheet.split('#')[-1].split('_')[0]
                if condition_id not in conditions:
                    conditions.append(condition_id)
        
        # 如果只有一个工况，设为单点
        if len(conditions) <= 1:
            self.variables = ['Single_Point']
            self.variable_name = 'Single'
            self.variable_unit = 'Point'
            print("检测到单个工况，设置为单点模式")
        else:
            # 多个工况，从数字ID创建工况标识
            try:
                condition_nums = [int(c) for c in conditions]
                condition_nums.sort()
                self.variables = condition_nums
                self.variable_name = 'Condition'
                self.variable_unit = 'ID'
                print(f"检测到 {len(condition_nums)} 个工况: {condition_nums}")
            except ValueError:
                # 如果不是数字，直接使用字符串
                self.variables = sorted(conditions)
                self.variable_name = 'Condition'
                self.variable_unit = 'Name'
                print(f"检测到 {len(conditions)} 个工况: {conditions}")
        
        # 设置标志，表示需要从端点提取数据
        self.need_extract_endpoints = True
    
    def _extract_endpoints_from_solution(self, gas_out_file, progress_callback=None):
        """从solution sheets提取第一点和最后一点数据"""
        if progress_callback:
            progress_callback(0, 100, "开始从solution sheets提取端点数据")
        print("开始从solution sheets提取端点数据...")
        
        # 存储端点数据的字典
        first_point_data = {}
        last_point_data = {}
        
        # 按工况分组处理
        condition_sheets = {}
        for sheet in self.soln_sheets:
            if '#' in sheet:
                condition_id = sheet.split('#')[-1].split('_')[0]
            else:
                condition_id = '1'
            
            if condition_id not in condition_sheets:
                condition_sheets[condition_id] = []
            condition_sheets[condition_id].append(sheet)
        
        if progress_callback:
            progress_callback(10, 100, f"发现 {len(condition_sheets)} 个工况组")
        print(f"发现 {len(condition_sheets)} 个工况组")
        
        for i, (condition_id, sheets) in enumerate(condition_sheets.items()):
            progress = 10 + (i / len(condition_sheets)) * 60  # 10%-70%的进度用于处理工况
            if progress_callback:
                progress_callback(progress, 100, f"处理工况 {condition_id}")
            print(f"处理工况 {condition_id}...")
            
            # 读取该工况的所有sheets
            condition_data = []
            for sheet in sheets:
                sheet_data = self.xlsx.parse(sheet, index_col=0)
                condition_data.append(sheet_data)
            
            if condition_data:
                # 合并同一工况的所有数据
                if len(condition_data) == 1:
                    merged_data = condition_data[0]
                else:
                    merged_data = pd.concat(condition_data, axis=1)
                
                # 提取第一点和最后一点
                first_point = merged_data.iloc[0]  # 第一行
                last_point = merged_data.iloc[-1]  # 最后一行
                
                first_point_data[condition_id] = first_point
                last_point_data[condition_id] = last_point
                
                if progress_callback:
                    progress_callback(progress + 5, 100, f"工况 {condition_id}: {len(merged_data)} 个时间点，{len(merged_data.columns)} 个变量")
                print(f"  工况 {condition_id}: {len(merged_data)} 个时间点，{len(merged_data.columns)} 个变量")
        
        # 转换为DataFrame格式，类似end_point sheets的结构
        if first_point_data:
            # 创建first_point和last_point的DataFrame
            first_df_list = []
            last_df_list = []
            
            for condition_id, data in first_point_data.items():
                # 为每个工况创建一行数据
                first_row = pd.DataFrame([data.values], 
                                       columns=data.index, 
                                       index=[condition_id])
                first_df_list.append(first_row)
            
            for condition_id, data in last_point_data.items():
                last_row = pd.DataFrame([data.values], 
                                      columns=data.index, 
                                      index=[condition_id])
                last_df_list.append(last_row)
            
            # 合并所有工况的数据
            first_points_df = pd.concat(first_df_list, axis=0)
            last_points_df = pd.concat(last_df_list, axis=0)
            
            # 排序索引以确保一致性
            first_points_df = first_points_df.sort_index()
            last_points_df = last_points_df.sort_index()
            
            if progress_callback:
                progress_callback(70, 100, f"数据整理完成: 第一点数据 {first_points_df.shape}, 最后一点数据 {last_points_df.shape}")
            print(f"提取完成:")
            print(f"  第一点数据: {first_points_df.shape}")
            print(f"  最后一点数据: {last_points_df.shape}")
            
            # 存储到gas_out_file中，模拟end_point sheets的结构
            gas_out_file.first_points = first_points_df
            gas_out_file.last_points = last_points_df
            
            # 处理摩尔分数数据（如果存在）
            mole_cols = [col for col in last_points_df.columns if 'Mole_fraction_' in col]
            if mole_cols:
                # 重新组织摩尔分数数据，避免NaN值
                mole_data_first = {}
                mole_data_last = {}
                
                # 按工况提取摩尔分数数据
                for condition_id in first_points_df.index:
                    condition_mole_cols = [col for col in mole_cols if f'_Run#{condition_id}_' in col]
                    
                    for col in condition_mole_cols:
                        # 提取组分名称
                        species = col.split('Mole_fraction_')[1].split(f'_Run#{condition_id}_')[0]
                        
                        if species not in mole_data_first:
                            mole_data_first[species] = {}
                            mole_data_last[species] = {}
                        
                        mole_data_first[species][condition_id] = first_points_df.loc[condition_id, col]
                        mole_data_last[species][condition_id] = last_points_df.loc[condition_id, col]
                
                # 转换为DataFrame
                if mole_data_last:
                    mole_fractions_first = pd.DataFrame(mole_data_first).T
                    mole_fractions_last = pd.DataFrame(mole_data_last).T
                    
                    # 设置变量索引
                    try:
                        condition_indices = [int(idx) for idx in mole_fractions_last.columns]
                        mole_fractions_first.columns = condition_indices
                        mole_fractions_last.columns = condition_indices
                    except ValueError:
                        # 如果不能转换为整数，保持原样
                        pass
                    
                    # 不修改index，保持组分名称
                    
                    gas_out_file.mole_fractions = mole_fractions_last  # 默认使用最后一点
                    gas_out_file.mole_fractions_first = mole_fractions_first
                    
                    if progress_callback:
                        progress_callback(80, 100, f"摩尔分数数据处理完成: {len(mole_fractions_last.index)} 个组分")
                    print(f"  摩尔分数数据: {len(mole_fractions_last.index)} 个组分")
                    
                    # 设置可能的反应物（从最大摩尔分数中选择）
                    if len(mole_fractions_last) > 0:
                        gas_out_file.possible_reactants = mole_fractions_last.nlargest(
                            8, mole_fractions_last.columns[0]).index.tolist()
                else:
                    print(f"  摩尔分数数据: 0 个组分（数据解析失败）")
            
            # 处理反应速率数据（如果存在）
            rate_cols = [col for col in last_points_df.columns if 'Net_rxn_rate_' in col]
            if rate_cols:
                reaction_rates_first = first_points_df[rate_cols].copy()
                reaction_rates_last = last_points_df[rate_cols].copy()
                
                gas_out_file.reaction_rates = reaction_rates_last
                gas_out_file.reaction_rates_first = reaction_rates_first
                
                if progress_callback:
                    progress_callback(90, 100, f"反应速率数据处理完成: {len(rate_cols)} 个反应")
                print(f"  反应速率数据: {len(rate_cols)} 个反应")
            
            # 设置数据可用性标志
            self.mole_fraction_exist = len(mole_cols) > 0
            self.net_reaction_rate_exist = len(rate_cols) > 0
            
            if progress_callback:
                progress_callback(100, 100, "端点数据提取完成!")
            print("端点数据提取完成!")
        else:
            print("警告：未能提取到端点数据")
    
    def _check_data_availability(self):
        """检查数据可用性"""
        if len(self.end_point_sheets) != 0:
            endPointsI = self.end_point_sheets[0]
            endPoints = self.xlsx.parse(endPointsI.rstrip())
            
            # 检查反应速率
            if np.sum(endPoints.columns.str.contains('Net_rxn_rate_')) == 0:
                print('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
                st.warning('注意！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
            else:
                self.net_reaction_rate_exist = True
            
            # 检查摩尔分数
            if np.sum(endPoints.columns.str.contains('Mole_fraction')) != 0:
                self.mole_fraction_exist = True
            
            # 检查ROP
            if np.sum(endPoints.columns.str.contains('_ROP_')) != 0:
                self.ROP_exist = True
            
            # 检查敏感性分析
            if np.sum(endPoints.columns.str.contains('_Sens_')) != 0:
                self.sensitivity_exist = True
        
            # 检查温度
            if np.sum(endPoints.columns.str.contains('Temperature')) != 0:
                self.temperature_exist = True
                
        # 额外检查：如果有敏感性工作表，也设置为True
        if len(self.sensitivity_sheets) > 0:
            self.sensitivity_exist = True
        else:
            # 单点情况
            if len(self.soln_sheets) > 0:
                firstpoints = self.xlsx.parse(self.soln_sheets[0].rstrip())
                if np.sum(firstpoints.columns.str.contains('Net_rxn_rate_')) == 0:
                    print('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
                    st.error('错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析')
                else:
                    self.net_reaction_rate_exist = True
    
    def combine_sheets(self, gas_out_file, progress_bar=False, mole_only=False):
        """
        合并工作表并计算ROP
        
        Args:
            gas_out_file: Gas文件处理器实例
            progress_bar: 是否显示进度条
            mole_only: 是否只处理摩尔分数
        """
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在合并Sheets')
            progress_bar2 = st.progress(0, text='正在积分ROP值')
            progress_bar3 = st.progress(0, text='正在处理敏感性数据')
        else:
            progress_bar1 = None
            progress_bar2 = None
            progress_bar3 = None
        
        print("正在合并Sheets")
        
        # 处理端点数据提取（没有end_point sheets的情况）
        if hasattr(self, 'need_extract_endpoints') and self.need_extract_endpoints:
            print("正在从solution sheets提取端点数据")
            
            # 创建端点提取的进度回调
            def endpoint_progress_callback(step, total, message):
                if progress_bar1:
                    progress = int((step / total) * 100)
                    progress_bar1.progress(progress / 100, text=f"🔄 {message}")
            
            self._extract_endpoints_from_solution(gas_out_file, endpoint_progress_callback)
        
        # 处理摩尔分数数据
        if self.mole_fraction_exist and len(self.end_point_sheets) != 0:
            print("正在合并摩尔分数数据")
            for i, sheet in enumerate(tqdm(self.end_point_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_end_raw = self.xlsx.parse(sheet, index_col=0)
                if i == 0:
                    new_sheet = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                else:
                    sheet_end = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                    new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
            
            gas_out_file.mole_fractions = copy.deepcopy(new_sheet)
            gas_out_file.mole_fractions.columns = gas_out_file.mole_fractions.columns.str.replace(
                ' Mole_fraction_', '').str.replace('_end_point_()', '', regex=False)
            gas_out_file.mole_fractions.index = self.variables

            # 关键修复：从mole_fractions计算mole_fractions_max，而不是依赖max_sheets
            gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
            gas_out_file.mole_fractions_max.index = ['max_values']  # 单行，表示最大值

            # 计算possible_reactants
            gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
                8, 'max_values').index.tolist()
        
        # 先处理完整solution数据（敏感性分析需要）
        if len(self.soln_sheets) > 0:
            print("正在收集完整Solution数据（用于敏感性分析）")
            soln_full_collect = {}
            soln_no_p = ''
            working_conditions = 1
            
            # 使用tqdm显示进度
            
            for i, sheet in enumerate(tqdm(self.soln_sheets, desc="正在收集Solution数据", unit='sheets')):
                # 优化：只读取一次，避免重复操作
                sheet_soln_raw = self.xlsx.parse(sheet, index_col=0)
                # 保留所有数据（包括温度、压力、摩尔分数等）以供敏感性分析使用
                sheet_soln_full = sheet_soln_raw
                
                if '#' in sheet:
                    soln_no_c = sheet.split('#')[-1].split('_')[0]
                else:
                    soln_no_c = '1'
                
                if i == 0:
                    new_sheet_full = sheet_soln_full
                
                if soln_no_c == soln_no_p:
                    # 优化：使用更高效的concat方式
                    new_sheet_full = pd.concat([new_sheet_full, sheet_soln_full], axis=1, sort=False)
                
                if soln_no_c != soln_no_p and i != 0:
                    soln_full_collect[self.variables[working_conditions - 1]] = new_sheet_full
                    new_sheet_full = sheet_soln_full
                    working_conditions += 1
                
                soln_no_p = soln_no_c
                
                # 添加Streamlit进度显示（如果在Web环境中）
                if progress_bar and 'st' in globals():
                    try:
                        # 这个progress_bar应该是专门用于Solution数据收集的
                        if hasattr(st.session_state, 'solution_progress'):
                            st.session_state.solution_progress.progress(
                                (i + 1) / len(self.soln_sheets),
                                f"收集Solution数据: {i + 1}/{len(self.soln_sheets)} sheets"
                            )
                    except:
                        pass  # 如果进度条不可用，静默继续
            
            soln_full_collect[self.variables[working_conditions - 1]] = new_sheet_full
            gas_out_file.soln_collect = soln_full_collect  # 存储完整的solution数据
            print('完整Solution数据收集完毕!')
        
        # 处理敏感性数据
        if self.sensitivity_exist and len(self.sensitivity_sheets) > 0:
            sen_no_p = ''
            sen_collect = {}
            working_conditions = 1
            print("正在合并敏感性数据")
            for i, sheet in enumerate(tqdm(self.sensitivity_sheets, desc="正在处理敏感性工作表", unit='sheets')):
                sheet_sen_raw = self.xlsx.parse(sheet, index_col=0)
                # 保留敏感性数据列，排除时间列
                sheet_sen = sheet_sen_raw.iloc[:, sheet_sen_raw.columns.str.contains('_Sens_')]
                
                if '#' in sheet:
                    sen_no_c = sheet.split('#')[-1].split('_')[0]
                else:
                    sen_no_c = '1'
                
                if i == 0:
                    new_sheet = sheet_sen
                
                if sen_no_c == sen_no_p:
                    new_sheet = pd.concat([new_sheet, sheet_sen], axis=1)
                
                if sen_no_c != sen_no_p and i != 0:
                    sen_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_sen
                    working_conditions += 1
                
                sen_no_p = sen_no_c
                
                if progress_bar:
                    progress_bar3.progress(
                        (i + 1) / len(self.sensitivity_sheets),
                        f"正在处理敏感性数据 工况点：{(i + 1)}/{len(self.sensitivity_sheets)} "
                    )
            
            sen_collect[self.variables[working_conditions - 1]] = new_sheet
            print('敏感性数据处理完毕!')
            
            # 存储敏感性数据集合
            gas_out_file.sensitivity_collect = sen_collect
            
            # 提取敏感性分析的目标组分
            if sen_collect:
                first_sheet = list(sen_collect.values())[0]
                # 从列名中提取目标组分 (例如: ' OH_Sens_...' -> 'OH')
                sensitivity_species = set()
                for col in first_sheet.columns:
                    if '_Sens_' in col:
                        species = col.strip().split('_Sens_')[0].strip()
                        sensitivity_species.add(species)
                
                gas_out_file.sensitivity_species_output = list(sensitivity_species)
                print(f'检测到敏感性分析目标组分: {gas_out_file.sensitivity_species_output}')
            else:
                gas_out_file.sensitivity_species_output = []
                print('未检测到敏感性分析数据')
        
        # 处理反应速率数据（如果存在）
        if self.net_reaction_rate_exist:
            soln_no_p = ''
            soln_collect = {}
            working_conditions = 1
            
            # 合并solution工作表
            for i, sheet in enumerate(tqdm(self.soln_sheets, desc="正在处理solution工作表", unit='sheets')):
                sheet_soln_raw = self.xlsx.parse(sheet, index_col=0)
                # 找到列标题中包含'Net'的列，即净反应速率
                sheet_soln = sheet_soln_raw.iloc[:, sheet_soln_raw.columns.str.contains('Net')]
                
                if '#' in sheet:
                    soln_no_c = sheet.split('#')[-1].split('_')[0]
                else:
                    soln_no_c = '1'
                
                if i == 0:
                    new_sheet = sheet_soln
                
                if soln_no_c == soln_no_p:
                    new_sheet = pd.concat([new_sheet, sheet_soln], axis=1)
                
                if soln_no_c != soln_no_p and i != 0:
                    soln_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_soln
                    working_conditions += 1
                
                soln_no_p = soln_no_c
                
                if progress_bar:
                    progress_bar1.progress(
                        (i + 1) / len(self.soln_sheets),
                        f'正在合并Sheets:{i + 1}/{len(self.soln_sheets)}'
                    )
            
            soln_collect[self.variables[working_conditions - 1]] = new_sheet
            print('Solution数据处理完毕!')
            
            # 计算ROP积分
            stoichi = copy.deepcopy(gas_out_file.stoichimetric)
            
            # 从solution数据中提取反应速率列
            first_sheet = soln_collect[self.variables[0]]
            net_rate_columns = [col for col in first_sheet.columns if 'Net' in col]
            
            # 检查反应速率输出完整性
            if len(net_rate_columns) != len(gas_out_file.reaction_index):
                st.warning("注意当前反应速率输出不完全，可能存在反应没有路径连接")
                
                exist_reaction_index = []
                all_reaction = set([i for i in range(1, len(gas_out_file.reaction_index) + 1)])
                
                for no in net_rate_columns:
                    exist_reaction_index.append(int(no.split('#')[1].split('_')[0]))
                
                no_reaction_index = list(all_reaction - set(exist_reaction_index))
                for r in no_reaction_index:
                    print(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                    st.warning(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                    stoichi.drop(gas_out_file.stoichimetric.index[r - 1], inplace=True)
            
            # 计算积分
            int_rate = {}
            int_rop = {}
            end_rate = {}
            end_rop = {}
            
            print('正在积分ROP值')
            
            try:
                for i, temp in enumerate(tqdm(soln_collect, desc="正在积分ROP", unit='温度点')):
                    # 使用净反应速率数据进行ROP计算
                    rate = soln_collect[temp]
                    
                    # 积分反应速率
                    int_rate[temp] = [np.trapz(rate.loc[:, j], rate.index) for j in rate.columns]
                    end_rate[temp] = rate.iloc[-1, :].tolist()
                    
                    # 计算ROP
                    int_rop[temp] = stoichi.apply(lambda x: np.multiply(x, int_rate[temp]), axis=0)
                    end_rop[temp] = stoichi.apply(lambda x: np.multiply(x, end_rate[temp]), axis=0)
                    
                    if progress_bar:
                        progress_bar2.progress(
                            (i + 1) / len(soln_collect),
                            f"正在积分ROP 工况点：{(i + 1)}/{len(soln_collect)} "
                        )
                
                print('处理完毕！')
                gas_out_file.rop_species_output = gas_out_file.species
                
            except ValueError:
                print("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
                st.error("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
                int_rop = None
            
            # 存储结果
            gas_out_file.integral_ROP = int_rop
            gas_out_file.end_ROP = end_rop
            
        elif self.net_reaction_rate_exist == False and self.ROP_exist == True:
            print("没有导出Net_reaction_rate,只能通过ROP进行分析，需要导出ROP是点选'ALL")
            
            rop_no_p = ''
            rop_collect = {}
            working_conditions = 1
            
            # 处理ROP工作表
            for i, sheet in enumerate(tqdm(self.rop_sheets, desc="正在处理工作表", unit='sheets')):
                sheet_rop_raw = self.xlsx.parse(sheet, index_col=0)
                sheet_rop = sheet_rop_raw.iloc[:, ~sheet_rop_raw.columns.str.contains('Total')]
                rop_no_c = sheet.split('#')[-1].split('_')[0]
                
                if i == 0:
                    new_sheet = sheet_rop
                
                if rop_no_c == rop_no_p:
                    new_sheet = pd.concat([new_sheet, sheet_rop], axis=1)
                
                if rop_no_c != rop_no_p and i != 0:
                    rop_collect[self.variables[working_conditions - 1]] = new_sheet
                    new_sheet = sheet_rop
                    working_conditions += 1
                
                rop_no_p = rop_no_c
                
                if progress_bar:
                    progress_bar2.progress(
                        (i + 1) / len(self.rop_sheets),
                        f"正在积分ROP 工况点：{(i + 1)}/{len(self.rop_sheets)} "
                    )
            
            rop_collect[self.variables[working_conditions - 1]] = new_sheet
            print('处理完毕!')
            
            # 整理ROP数据
            int_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)
            end_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)
            
            gas_out_file.rop_species_output = [
                a for a in set([s.strip().split('_')[0] for s in rop_collect[self.variables[0]].columns])
            ]
            
            for temp in tqdm(rop_collect, desc="正在积分ROP", unit='温度点'):
                rop = rop_collect[temp]
                int_rop_raw2.loc[temp, :] = [np.trapz(rop.loc[:, j], rop.index) for j in rop.columns]
                end_rop_raw2.loc[temp, :] = rop.iloc[-1, :].tolist()
            
            # 按组分整理ROP数据
            int_rop2 = {}
            end_rop2 = {}
            
            for species in tqdm(gas_out_file.rop_species_output, desc="正在处理组分信息", unit='组分'):
                int_rop2[species] = int_rop_raw2.loc[:, int_rop_raw2.columns.str.contains(f' {species}_ROP', regex=False)]
                end_rop2[species] = end_rop_raw2.loc[:, end_rop_raw2.columns.str.contains(f' {species}_ROP', regex=False)]
                
                # 更新列名
                reaction_rop = [
                    gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] + "#" + a[1].split('_')[0]
                    for a in int_rop2[species].columns.str.split('#')
                ]
                reaction_rop_end = [
                    gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] + "#" + a[1].split('_')[0]
                    for a in end_rop2[species].columns.str.split('#')
                ]
                
                int_rop2[species].columns = reaction_rop
                end_rop2[species].columns = reaction_rop_end
            
            print('处理完毕!')
            
            # 存储结果
            gas_out_file.integral_ROP = int_rop2
            gas_out_file.end_ROP = end_rop2
    
    def get_available_gradient_variables(self):
        """获取可用的梯度变量列表"""
        print("=" * 60)
        print("检测可用的梯度变量...")
        print("=" * 60)
        
        # 从第一个solution sheet中获取完整的列信息
        if len(self.soln_sheets) > 0:
            first_sheet = self.xlsx.parse(self.soln_sheets[0], index_col=0)
            columns = first_sheet.columns.tolist()
            print(f"从 {self.soln_sheets[0]} 读取到 {len(columns)} 个列")
            print("前10个列名:")
            for i, col in enumerate(columns[:10]):
                print(f"  {i+1:2d}. {col}")
            if len(columns) > 10:
                print(f"  ... 还有{len(columns)-10}个列")
        else:
            print("错误：没有找到solution工作表")
            return []
        
        # 扩展的物理变量和摩尔分数
        common_variables = ['Temperature', 'Pressure', 'Density', 'Enthalpy', 'Entropy']
        
        print(f"\n搜索常见物理变量: {common_variables}")
        
        # 添加摩尔分数变量（排除反应速率）
        mole_fraction_vars = []
        print("\n搜索摩尔分数变量...")
        for col in columns:
            if 'Mole_fraction_' in col and '_Run#' in col:
                # 排除反应速率相关的列
                if 'rxn_rate' not in col.lower() and 'rate' not in col.lower():
                    try:
                        species = col.split('Mole_fraction_')[1].split('_Run#')[0]
                        mole_fraction_vars.append(f'Mole_fraction_{species}')
                        print(f"  发现摩尔分数: {species} -> Mole_fraction_{species}")
                    except Exception as e:
                        print(f"  解析失败: {col} -> {e}")
        
        # 去除重复的摩尔分数变量
        mole_fraction_vars = list(set(mole_fraction_vars))
        print(f"去重后摩尔分数变量: {mole_fraction_vars}")
        
        # 合并所有可能的变量
        all_variables = common_variables + mole_fraction_vars
        available_vars = []
        
        print(f"\n检查所有候选变量: {all_variables}")
        print("-" * 40)
        
        for var in all_variables:
            # 排除反应速率相关的列和敏感性数据列
            matching_cols = [col for col in columns 
                           if var in col 
                           and 'rxn_rate' not in col.lower() 
                           and 'rate' not in col.lower()
                           and '_Sens_' not in col]
            
            if matching_cols:
                available_vars.append(var)
                print(f"✅ {var}: 找到 {len(matching_cols)} 个匹配列")
                for col in matching_cols[:3]:  # 只显示前3个
                    print(f"    - {col}")
                if len(matching_cols) > 3:
                    print(f"    ... 还有{len(matching_cols)-3}个")
            else:
                print(f"❌ {var}: 未找到匹配列")
                # 对于摩尔分数变量，显示更多调试信息
                if 'Mole_fraction_' in var:
                    print(f"    调试: 搜索包含 '{var}' 的列:")
                    for col in columns:
                        if var.replace('Mole_fraction_', '') in col:
                            print(f"      候选: {col}")
                    print(f"    原始搜索条件: {var} in column")
        
        print("-" * 40)
        print(f"最终可用梯度变量: {available_vars}")
        print("=" * 60)
        
        if not available_vars:
            print("⚠️  警告：没有找到任何可用的梯度变量！")
            print("可能的原因:")
            print("  1. solution数据中不包含Temperature、Pressure等物理变量")
            print("  2. 摩尔分数列名格式不符合预期")
            print("  3. 所有变量都被误识别为反应速率")
        
        return available_vars
    
    def get_available_x_axis_variables(self, gas_out_file, point_type='last'):
        """
        获取可用作横坐标的变量列表（用于没有end_point sheets的情况）
        
        Args:
            gas_out_file: Gas文件处理器实例
            point_type: 使用的数据点类型，'first'或'last'
        
        Returns:
            dict: {变量名: 变量值列表} 的字典
        """
        print("=" * 60)
        print(f"获取可用的横坐标变量 (使用{point_type}点数据)")
        print("=" * 60)
        
        available_x_vars = {}
        
        # 检查是否有提取的端点数据
        if point_type == 'last' and hasattr(gas_out_file, 'last_points'):
            data_df = gas_out_file.last_points
        elif point_type == 'first' and hasattr(gas_out_file, 'first_points'):
            data_df = gas_out_file.first_points
        elif hasattr(gas_out_file, 'mole_fractions') and gas_out_file.mole_fractions is not None:
            # 如果有正常的摩尔分数数据，从中提取
            print("从正常的摩尔分数数据中提取横坐标变量")
            data_df = gas_out_file.mole_fractions.T  # 转置：工况为行，组分为列
            # 添加温度等物理变量（如果有的话）
            temp_data = pd.DataFrame(index=data_df.index)

            # 确保variables长度匹配
            if len(self.variables) == len(data_df.index):
                temp_data['Temperature'] = self.variables  # 使用工况变量作为温度
            else:
                # 如果长度不匹配，使用索引编号
                temp_data['Temperature'] = range(len(data_df.index))

            # 合并数据
            data_df = pd.concat([temp_data, data_df], axis=1)
        elif hasattr(gas_out_file, 'soln_full_collect') and gas_out_file.soln_full_collect:
            # 关键修复：从完整的solution数据中提取横坐标变量（没有end_point_sheets的情况）
            print("从完整的solution数据中提取横坐标变量（没有end_point_sheets）")

            # 使用第一个工况的数据结构作为模板
            first_condition = list(gas_out_file.soln_full_collect.keys())[0]
            template_data = gas_out_file.soln_full_collect[first_condition]

            # 提取每个工况的最后一点数据作为端点数据
            endpoint_data = {}
            for condition, soln_data in gas_out_file.soln_full_collect.items():
                if point_type == 'last':
                    endpoint_data[condition] = soln_data.iloc[-1]  # 最后一行
                else:  # first
                    endpoint_data[condition] = soln_data.iloc[0]   # 第一行

            # 转换为DataFrame
            data_df = pd.DataFrame(endpoint_data).T  # 转置：工况为行，变量为列
            print(f"从solution数据提取的端点数据形状: {data_df.shape}")
            print(f"工况列表: {list(data_df.index)}")
            print(f"变量列表（前10个）: {list(data_df.columns[:10])}")
        else:
            print("未找到端点数据或摩尔分数数据")
            return available_x_vars
        
        print(f"端点数据形状: {data_df.shape}")
        print(f"工况数量: {len(data_df)}")
        
        # 候选变量类型
        candidate_vars = ['Temperature', 'Pressure', 'Density', 'Enthalpy', 'Entropy']
        
        # 检查物理变量
        for var in candidate_vars:
            # 收集该变量在所有工况中的值
            var_values = []
            for condition_id in data_df.index:
                # 关键修复：处理不同的数据源和工况ID格式
                matching_cols = []

                # 方法1：直接匹配列名（如果列名就是变量名）
                if var in data_df.columns:
                    matching_cols = [var]

                # 方法2：匹配包含Run#的列名（有end_point_sheets的情况）
                if not matching_cols:
                    # 确保condition_id是字符串格式
                    condition_str = str(condition_id)
                    matching_cols = [col for col in data_df.columns
                                   if isinstance(col, str) and var in col and f'_Run#{condition_str}_' in col]

                # 方法3：模糊匹配（只要包含变量名）
                if not matching_cols:
                    matching_cols = [col for col in data_df.columns
                                   if isinstance(col, str) and var in col]

                if matching_cols:
                    col = matching_cols[0]
                    val = data_df.loc[condition_id, col]
                    if not pd.isna(val):
                        var_values.append(val)

            if var_values:
                # 检查是否有有效的变化
                unique_values = len(set([round(v, 6) for v in var_values]))  # 四舍五入避免浮点精度问题
                if unique_values > 1:  # 至少有2个不同的值
                    clean_var_name = var
                    available_x_vars[clean_var_name] = var_values
                    print(f"✅ {clean_var_name}: {unique_values} 个不同值，范围 {min(var_values):.3f} - {max(var_values):.3f}")
                else:
                    print(f"❌ {var}: 所有值相同 ({var_values[0]:.3f})")
            else:
                print(f"❌ {var}: 未找到数据")
        
        # 检查摩尔分数变量
        # 关键修复：确保列名是字符串类型
        mole_cols = [col for col in data_df.columns if isinstance(col, str) and 'Mole_fraction_' in col]
        print(f"\n检查摩尔分数变量 ({len(mole_cols)} 个候选)")
        
        # 按组分组织摩尔分数数据
        species_data = {}

        # 建立工况序号到工况值的映射
        condition_index_to_value = {}
        for i, condition_value in enumerate(data_df.index):
            condition_index_to_value[str(i + 1)] = condition_value  # 序号从1开始

        for col in mole_cols:
            try:
                if '_Run#' in col:
                    species = col.split('Mole_fraction_')[1].split('_Run#')[0]
                    condition_index = col.split('_Run#')[1].split('_')[0]  # 序号（如"1", "2"）

                    if species not in species_data:
                        species_data[species] = {}

                    # 关键修复：将序号映射到实际的工况值
                    if condition_index in condition_index_to_value:
                        condition_value = condition_index_to_value[condition_index]
                        val = data_df.loc[condition_value, col]
                        if not pd.isna(val):
                            species_data[species][condition_value] = val
                    else:
                        print(f"  警告：未找到序号 {condition_index} 对应的工况值")
            except Exception as e:
                print(f"解析摩尔分数变量失败: {col} -> {e}")
        
        # 检查每个组分的变化
        for species, condition_values in list(species_data.items())[:10]:  # 限制检查前10个组分
            if len(condition_values) > 1:  # 至少有2个工况的数据
                values = list(condition_values.values())
                unique_values = len(set([round(v, 8) for v in values]))  # 四舍五入避免浮点精度问题
                
                if unique_values > 1:
                    var_name = f'Mole_fraction_{species}'
                    available_x_vars[var_name] = values
                    print(f"✅ {var_name}: {unique_values} 个不同值，范围 {min(values):.6f} - {max(values):.6f}")
                else:
                    print(f"❌ Mole_fraction_{species}: 所有值相同 ({values[0]:.6f})")
            else:
                print(f"❌ Mole_fraction_{species}: 数据不足 (只有 {len(condition_values)} 个工况)")
        
        print("-" * 60)
        print(f"总共找到 {len(available_x_vars)} 个可用横坐标变量")
        for var_name in available_x_vars.keys():
            print(f"  - {var_name}")
        print("=" * 60)
        
        return available_x_vars
    
    def get_sensitivity_gradients(self, gas_out_file, target_species, gradient_variable='Temperature', mode='gradient_max'):
        """
        计算敏感性数据在指定位置的值
        
        Args:
            gas_out_file: Gas文件处理器实例
            target_species: 目标组分名称
            gradient_variable: 用于计算梯度的变量名称（如Temperature, Pressure等）
            mode: 分析模式，'gradient_max'(梯度最大值处) 或 'endpoint'(终点)
        
        Returns:
            dict: 各工况点在指定位置的敏感性数据
        """
        print("=" * 60)
        print(f"开始提取敏感性梯度数据")
        print(f"  目标组分: {target_species}")
        print(f"  梯度变量: {gradient_variable}")
        print(f"  分析模式: {mode}")
        print("=" * 60)
        
        if not hasattr(gas_out_file, 'sensitivity_collect'):
            print("❌ 错误：未找到敏感性数据，请先处理敏感性数据")
            return None
        
        print(f"敏感性数据工况: {list(gas_out_file.sensitivity_collect.keys())}")
        
        gradient_sens = {}
        
        for condition, sen_data in gas_out_file.sensitivity_collect.items():
            print(f"\n处理工况: {condition}")
            print(f"  敏感性数据形状: {sen_data.shape}")
            
            # 获取目标组分的敏感性列
            target_cols = [col for col in sen_data.columns if target_species in col and '_Sens_' in col]
            print(f"  找到 {len(target_cols)} 个 {target_species} 敏感性列")
            
            if not target_cols:
                print(f"  ⚠️  警告：未找到组分 {target_species} 的敏感性数据")
                # 调试：显示前几个列名
                print(f"  可用敏感性列示例:")
                sens_cols = [col for col in sen_data.columns if '_Sens_' in col]
                for i, col in enumerate(sens_cols[:5]):
                    print(f"    {i+1}. {col}")
                if len(sens_cols) > 5:
                    print(f"    ... 还有{len(sens_cols)-5}个敏感性列")
                continue
            
            if mode == 'endpoint':
                # 终点模式：直接取最后一点
                data_idx = -1
                gradient_sens[condition] = sen_data.iloc[data_idx][target_cols]
                print(f"  ✅ 终点模式: 使用索引 {data_idx}")
                
            elif mode == 'gradient_max':
                # 梯度最大值模式：需要计算梯度
                # 优先使用完整数据（包含所有物理变量）
                soln_data = None
                if hasattr(gas_out_file, 'soln_full_collect') and condition in gas_out_file.soln_full_collect:
                    soln_data = gas_out_file.soln_full_collect[condition]
                    print(f"  使用完整Solution数据")
                elif hasattr(gas_out_file, 'soln_collect') and condition in gas_out_file.soln_collect:
                    soln_data = gas_out_file.soln_collect[condition]
                    print(f"  使用标准Solution数据")

                if soln_data is None:
                    print(f"  ❌ 警告：未找到工况 {condition} 对应的solution数据")
                    if hasattr(gas_out_file, 'soln_collect'):
                        print(f"  可用solution工况: {list(gas_out_file.soln_collect.keys())}")
                    if hasattr(gas_out_file, 'soln_full_collect'):
                        print(f"  可用完整solution工况: {list(gas_out_file.soln_full_collect.keys())}")
                    continue
                
                print(f"  Solution数据形状: {soln_data.shape}")
                print(f"  Solution列数: {len(soln_data.columns)}")
                
                # 关键修复：建立工况值到序号的映射（摩尔分数变量也需要）
                conditions_list = list(gas_out_file.sensitivity_collect.keys())
                if condition in conditions_list:
                    condition_index = conditions_list.index(condition) + 1  # 序号从1开始
                else:
                    condition_index = condition  # 回退到原值

                # 查找梯度变量列 - 支持更灵活的匹配，考虑工况编号
                gradient_cols = []
                if 'Mole_fraction_' in gradient_variable:
                    # 对于摩尔分数变量，需要更精确的匹配
                    species_name = gradient_variable.replace('Mole_fraction_', '').replace('_()', '')
                    print(f"  搜索摩尔分数变量: {species_name}")

                    # 关键修复：使用condition_index而不是condition
                    expected_mole_pattern = f"Mole_fraction_{species_name}_Run#{condition_index}_()"
                    print(f"  期望的摩尔分数列名: {expected_mole_pattern} (工况值{condition}->序号{condition_index})")

                    # 精确匹配
                    gradient_cols = [col for col in soln_data.columns
                                   if col.strip() == expected_mole_pattern
                                   and 'rxn_rate' not in col.lower() and 'rate' not in col.lower()]

                    # 如果精确匹配失败，尝试模糊匹配
                    if not gradient_cols:
                        print(f"  精确匹配失败，尝试模糊匹配...")
                        gradient_cols = [col for col in soln_data.columns
                                       if f'Mole_fraction_{species_name}' in col and f'_Run#{condition_index}_' in col
                                       and 'rxn_rate' not in col.lower() and 'rate' not in col.lower()]
                else:
                    # 对于其他变量（Temperature、Pressure等）
                    print(f"  搜索物理变量: {gradient_variable}")

                    # 提取基础变量名（如Temperature）和单位（如(K)）
                    if '(' in gradient_variable:
                        base_var_name = gradient_variable.split('_(')[0]  # Temperature
                        unit_part = '(' + gradient_variable.split('_(')[1]  # (K)
                    else:
                        base_var_name = gradient_variable.replace('_()', '')
                        unit_part = '()'

                    print(f"  基础变量名: {base_var_name}, 单位: {unit_part}")

                    # 构造匹配模式：基础变量名_Run#序号_单位
                    expected_pattern = f"{base_var_name}_Run#{condition_index}_{unit_part}"
                    print(f"  期望的列名模式: {expected_pattern} (工况值{condition}->序号{condition_index})")

                    # 精确匹配
                    gradient_cols = [col for col in soln_data.columns
                                   if col.strip() == expected_pattern
                                   and 'rxn_rate' not in col.lower() and 'rate' not in col.lower()]

                    # 如果精确匹配失败，尝试模糊匹配
                    if not gradient_cols:
                        print(f"  精确匹配失败，尝试模糊匹配...")
                        gradient_cols = [col for col in soln_data.columns
                                       if base_var_name in col and f'_Run#{condition_index}_' in col and unit_part in col
                                       and 'rxn_rate' not in col.lower() and 'rate' not in col.lower()]
                
                print(f"  找到 {len(gradient_cols)} 个梯度变量候选列:")
                for i, col in enumerate(gradient_cols):
                    print(f"    {i+1}. {col}")
                
                if not gradient_cols:
                    print(f"  ❌ 警告：未找到变量 {gradient_variable} 的数据列")
                    print(f"  可用solution列示例:")
                    for i, col in enumerate(soln_data.columns[:10]):
                        print(f"    {i+1:2d}. {col}")
                    if len(soln_data.columns) > 10:
                        print(f"    ... 还有{len(soln_data.columns)-10}个列")
                    continue
                
                gradient_col = gradient_cols[0]
                gradient_values = soln_data[gradient_col]
                print(f"  使用梯度列: {gradient_col}")
                print(f"  梯度数据范围: {gradient_values.min():.3f} - {gradient_values.max():.3f}")
                
                # 检查梯度变量是否为常数
                gradient_range = gradient_values.max() - gradient_values.min()
                is_constant = gradient_range < 1e-10  # 很小的阈值判断为常数
                
                if is_constant:
                    print(f"  ⚠️  {gradient_variable}为常数值({gradient_values.iloc[0]:.3f})，使用最后一点数据")
                    data_idx = -1  # 使用最后一点
                else:
                    # 计算梯度 - 使用第一列（通常是Time）作为x轴
                    if len(soln_data.columns) > 0:
                        # 获取第一列作为x轴（通常是Time或其他自变量）
                        first_col = soln_data.columns[0]
                        x_axis_data = soln_data[first_col]
                        print(f"  使用x轴: {first_col}")
                        print(f"  x轴数据范围: {x_axis_data.min():.3f} - {x_axis_data.max():.3f}")
                        
                        # 检查x轴数据是否单调递增
                        x_diffs = np.diff(x_axis_data)
                        if np.all(x_diffs > 0):
                            print(f"  x轴数据单调递增，使用Time轴计算梯度")
                            gradient = np.gradient(gradient_values, x_axis_data)
                        else:
                            print(f"  x轴数据非单调，使用数值索引计算梯度")
                            gradient = np.gradient(gradient_values, np.arange(len(gradient_values)))
                    else:
                        print(f"  没有额外列，使用数值索引计算梯度")
                        gradient = np.gradient(gradient_values, np.arange(len(gradient_values)))
                    
                    data_idx = np.argmax(np.abs(gradient))
                    
                    print(f"  梯度最大值索引: {data_idx}")
                    print(f"  梯度最大值: {gradient[data_idx]:.3e}")
                    print(f"  对应的{gradient_variable}值: {gradient_values.iloc[data_idx]:.3f}")
                    
                    # 显示x轴对应值
                    if len(soln_data.columns) > 0:
                        x_axis_value = soln_data[soln_data.columns[0]].iloc[data_idx]
                        print(f"  对应的{soln_data.columns[0]}值: {x_axis_value:.3f}")
                
                # 获取指定位置的敏感性数据
                gradient_sens[condition] = sen_data.iloc[data_idx][target_cols]
                print(f"  ✅ 成功提取敏感性数据")
            
            else:
                print(f"  ❌ 错误：不支持的模式 {mode}")
                continue
        
        print("-" * 60)
        print(f"敏感性梯度数据提取完成")
        print(f"成功提取 {len(gradient_sens)} 个工况的敏感性数据")
        if gradient_sens:
            first_condition = list(gradient_sens.keys())[0]
            print(f"每个工况的敏感性数据数量: {len(gradient_sens[first_condition])}")
        print("=" * 60)
        
        return gradient_sens
    
    # ========================================
    # DRGEP分析方法
    # ========================================
    
    def calculate_drgep_importance_coefficients(self, gas_out_file, target_species, 
                                              data_type='integral', progress_callback=None):
        """
        计算DRGEP重要性系数
        
        Args:
            gas_out_file: Gas文件处理器实例
            target_species: 目标组分列表
            data_type: 数据类型 ('integral' 或 'endpoint')
            progress_callback: 进度回调函数
        
        Returns:
            dict: 各工况条件下的重要性系数
        """
        print("=" * 60)
        print("开始DRGEP重要性系数计算")
        print(f"  目标组分: {target_species}")
        print(f"  数据类型: {data_type}")
        print("=" * 60)
        
        if progress_callback:
            progress_callback(0, 100, "初始化DRGEP计算")
        
        # 获取净反应速率数据
        net_rate_data_dict = self._extract_net_reaction_rates(gas_out_file, data_type, progress_callback)
        
        # 获取化学计量矩阵和组分名称
        stoich_matrix = gas_out_file.stoichimetric
        species_names = gas_out_file.species
        
        print(f"数据检查:")
        print(f"  化学计量矩阵形状: {stoich_matrix.shape}")
        print(f"  组分数量: {len(species_names)}")
        print(f"  工况数量: {len(net_rate_data_dict)}")
        
        if progress_callback:
            progress_callback(20, 100, "数据验证完成")
        
        # 计算各工况的重要性系数
        all_importance_coeffs = {}
        total_conditions = len(net_rate_data_dict)
        
        for i, (condition, net_rates) in enumerate(net_rate_data_dict.items()):
            progress = 20 + (i / total_conditions) * 70  # 20-90%的进度
            if progress_callback:
                progress_callback(progress, 100, f"处理工况 {condition}")
            
            print(f"\n处理工况: {condition}")
            print(f"  净反应速率数据长度: {len(net_rates)}")
            
            try:
                # 创建邻接矩阵
                adjacency_matrix = self._create_drgep_matrix_from_net_rates(
                    species_names, stoich_matrix, net_rates
                )
                
                # 计算重要性系数
                importance_coefficients = self._calculate_importance_coefficients(
                    species_names, target_species, adjacency_matrix
                )
                
                all_importance_coeffs[condition] = importance_coefficients
                print(f"  ✅ 成功计算 {len(importance_coefficients)} 个组分的重要性系数")
                
            except Exception as e:
                print(f"  ❌ 工况 {condition} 计算失败: {e}")
                import traceback
                traceback.print_exc()
                # 创建默认系数（避免程序崩溃）
                importance_coefficients = {sp: 0.0 for sp in species_names}
                for target in target_species:
                    if target in importance_coefficients:
                        importance_coefficients[target] = 1.0
                all_importance_coeffs[condition] = importance_coefficients
        
        if progress_callback:
            progress_callback(100, 100, "DRGEP计算完成")
        
        print(f"\nDRGEP计算总结:")
        print(f"  成功处理工况: {len(all_importance_coeffs)}")
        print(f"  目标组分数量: {len(target_species)}")
        
        # 显示统计信息
        if all_importance_coeffs:
            first_condition = list(all_importance_coeffs.keys())[0]
            max_coeffs = {sp: max([all_importance_coeffs[cond].get(sp, 0.0) for cond in all_importance_coeffs.keys()]) 
                         for sp in species_names}
            significant_species = sum(1 for coeff in max_coeffs.values() if coeff > 0.01)
            print(f"  重要性系数>0.01的组分: {significant_species}/{len(species_names)}")
        
        print("=" * 60)
        return all_importance_coeffs
    
    def _extract_net_reaction_rates(self, gas_out_file, data_type, progress_callback=None):
        """
        从已有的combine_sheets数据中提取净反应速率数据
        
        Args:
            gas_out_file: Gas文件处理器实例
            data_type: 数据类型 ('integral' 或 'endpoint')
            progress_callback: 进度回调函数
        
        Returns:
            dict: 各工况条件下的净反应速率数据
        """
        print(f"从已有数据提取净反应速率 (类型: {data_type})")
        
        if progress_callback:
            progress_callback(5, 100, "从已有数据提取净反应速率")
        
        net_rate_data_dict = {}
        
        # 检查是否已有solution数据（combine_sheets已处理）
        if hasattr(gas_out_file, 'soln_collect') and gas_out_file.soln_collect:
            print("使用combine_sheets已处理的solution数据")
            
            for condition, soln_data in gas_out_file.soln_collect.items():
                # 获取净反应速率列
                net_rate_cols = [col for col in soln_data.columns if 'Net_rxn_rate_' in col]
                net_rates = {}
                
                for col in net_rate_cols:
                    if '_GasRxn#' in col:
                        reaction_no = col.split('_GasRxn#')[1].split('_')[0]
                        try:
                            reaction_num = int(reaction_no)
                            if data_type == 'endpoint':
                                # 终点数据：取最后一行
                                net_rates[reaction_num] = soln_data[col].iloc[-1]
                            else:  # integral
                                # 积分数据：使用梯形积分
                                integrated_rate = np.trapz(soln_data[col], soln_data.index)
                                net_rates[reaction_num] = integrated_rate
                        except (ValueError, IndexError):
                            continue
                
                if net_rates:  # 只添加有数据的工况
                    net_rate_data_dict[condition] = net_rates
        
        # 如果没有solution数据，尝试从end_point sheets提取（仅用于终点数据）
        elif data_type == 'endpoint' and hasattr(self, 'end_point_sheets') and self.end_point_sheets:
            print("从end_point sheets提取终点净反应速率")
            
            # 合并所有end_point sheets的数据
            all_net_rate_cols = set()
            all_conditions = set()
            sheet_data = {}
            
            # 读取所有end_point sheets
            for sheet_name in self.end_point_sheets:
                print(f"  读取sheet: {sheet_name}")
                df = self.xlsx.parse(sheet_name, index_col=0)
                sheet_data[sheet_name] = df
                
                # 收集所有净反应速率列
                net_rate_cols = [col for col in df.columns if 'Net_rxn_rate_' in col]
                all_net_rate_cols.update(net_rate_cols)
                all_conditions.update(df.index)
            
            print(f"找到 {len(all_net_rate_cols)} 个净反应速率列")
            print(f"找到 {len(all_conditions)} 个工况条件")
            
            # 按工况组织数据，合并所有sheets的数据
            for condition in all_conditions:
                net_rates = {}
                
                # 遍历所有sheets，收集该工况的所有净反应速率数据
                for sheet_name, df in sheet_data.items():
                    if condition in df.index:
                        for col in df.columns:
                            if 'Net_rxn_rate_' in col and '_GasRxn#' in col:
                                reaction_no = col.split('_GasRxn#')[1].split('_')[0]
                                try:
                                    reaction_num = int(reaction_no)
                                    net_rates[reaction_num] = df.loc[condition, col]
                                except ValueError:
                                    continue
                
                if net_rates:  # 只添加有数据的工况
                    net_rate_data_dict[condition] = net_rates
        
        else:
            error_msg = f"没有找到{data_type}净反应速率数据"
            if data_type == 'integral':
                error_msg += "，请确保已运行combine_sheets处理solution数据"
            raise ValueError(error_msg)
        
        print(f"成功提取 {len(net_rate_data_dict)} 个工况的净反应速率数据")
        if net_rate_data_dict:
            first_condition = list(net_rate_data_dict.keys())[0]
            first_data = net_rate_data_dict[first_condition]
            print(f"每个工况的反应数量: {len(first_data)}")
            print(f"反应编号范围: {min(first_data.keys())} - {max(first_data.keys())}")
        
        return net_rate_data_dict
    
    def _create_drgep_matrix_from_net_rates(self, species_names, stoich_matrix, net_rates):
        """
        从净反应速率创建DRGEP邻接矩阵
        
        Args:
            species_names: 组分名称列表
            stoich_matrix: 化学计量矩阵 (反应 × 组分)
            net_rates: 净反应速率字典 {反应编号: 速率值}
        
        Returns:
            numpy.ndarray: DRGEP邻接矩阵 (组分 × 组分)
        """
        n_species = len(species_names)
        
        print(f"  创建DRGEP矩阵:")
        print(f"    化学计量矩阵形状: {stoich_matrix.shape}")
        print(f"    净反应速率数量: {len(net_rates)}")
        
        # 创建反应速率数组
        reaction_rates = np.zeros(len(stoich_matrix))
        
        # 填充反应速率数据
        for reaction_idx, reaction in enumerate(stoich_matrix.index):
            # 从反应名称中提取反应编号
            reaction_number = None
            if '#' in reaction:
                try:
                    reaction_number = int(reaction.split('#')[-1])
                except ValueError:
                    reaction_number = reaction_idx + 1
            else:
                reaction_number = reaction_idx + 1
            
            # 从净反应速率字典中获取速率
            if reaction_number in net_rates:
                reaction_rates[reaction_idx] = net_rates[reaction_number]
        
        # 找到有效反应（非零反应速率）
        valid_reactions = np.where(np.abs(reaction_rates) > 1e-15)[0]
        
        print(f"    有效反应数量: {len(valid_reactions)}/{len(reaction_rates)}")
        print(f"    反应速率范围: {reaction_rates.min():.3e} - {reaction_rates.max():.3e}")
        
        if len(valid_reactions) > 0:
            # 获取净化学计量矩阵 (反应 × 组分)
            net_stoich = stoich_matrix.values  # (n_reactions × n_species)
            
            # 转置以匹配cantera格式 (组分 × 反应)
            net_stoich_t = net_stoich.T  # (n_species × n_reactions)
            
            # 计算基础速率：组分净产生/消耗速率
            base_rates = net_stoich_t[:, valid_reactions] * reaction_rates[valid_reactions]  # (n_species × n_valid_reactions)
            
            print(f"    基础速率矩阵形状: {base_rates.shape}")
            print(f"    基础速率范围: {base_rates.min():.3e} - {base_rates.max():.3e}")
            
            # 计算分母：每个组分的总消耗速率和产生速率的最大值
            denominator_dest = np.sum(np.maximum(0.0, -base_rates), axis=1)  # 总消耗速率
            denominator_prod = np.sum(np.maximum(0.0, base_rates), axis=1)   # 总产生速率
            denominator = np.maximum(denominator_prod, denominator_dest)
            
            print(f"    分母计算 - 产生: {denominator_prod.min():.3e}-{denominator_prod.max():.3e}")
            print(f"    分母计算 - 消耗: {denominator_dest.min():.3e}-{denominator_dest.max():.3e}")
            
            # 计算分子：组分A与组分B的相互作用
            numerator = np.zeros((n_species, n_species))
            
            # 获取反应标志矩阵
            flags = np.where(np.abs(net_stoich) > 1e-15, 1, 0)  # (n_reactions × n_species)
            
            for sp_b in range(n_species):
                # 找到涉及组分B的有效反应
                reactions_with_sp_b = valid_reactions[flags[valid_reactions, sp_b] == 1]
                
                if len(reactions_with_sp_b) > 0:
                    # 计算所有组分在涉及组分B的反应中的总净产生/消耗速率
                    valid_indices = np.isin(valid_reactions, reactions_with_sp_b)
                    numerator[:, sp_b] = np.sum(np.abs(base_rates[:, valid_indices]), axis=1)
            
            print(f"    分子矩阵范围: {numerator.min():.3e} - {numerator.max():.3e}")
            
            # 计算最终邻接矩阵
            with np.errstate(divide='ignore', invalid='ignore'):
                adjacency_matrix = np.where(denominator[:, np.newaxis] > 1e-15, 
                                          numerator / denominator[:, np.newaxis], 0.0)
            
            # 限制邻接矩阵的值在合理范围内（0到1之间）
            adjacency_matrix = np.clip(adjacency_matrix, 0.0, 1.0)
            
        else:
            print(f"    警告：没有有效反应，创建零矩阵")
            adjacency_matrix = np.zeros((n_species, n_species))
        
        # 对角线置零（避免自循环）
        np.fill_diagonal(adjacency_matrix, 0.0)
        
        print(f"    最终邻接矩阵形状: {adjacency_matrix.shape}")
        print(f"    最终邻接矩阵范围: {adjacency_matrix.min():.3e} - {adjacency_matrix.max():.3e}")
        print(f"    非零元素: {np.count_nonzero(adjacency_matrix)}/{adjacency_matrix.size}")
        
        return adjacency_matrix
    
    def _calculate_importance_coefficients(self, species_names, target_species, adjacency_matrix):
        """
        从邻接矩阵计算重要性系数
        
        Args:
            species_names: 组分名称列表
            target_species: 目标组分列表
            adjacency_matrix: DRGEP邻接矩阵
        
        Returns:
            dict: 重要性系数字典
        """
        import networkx as nx
        from collections import deque
        from heapq import heappush, heappop
        from itertools import count
        
        # 创建有向图
        graph = nx.DiGraph(adjacency_matrix)
        name_mapping = {i: sp for i, sp in enumerate(species_names)}
        nx.relabel_nodes(graph, name_mapping, copy=False)
        
        print(f"    图构建:")
        print(f"      节点数: {graph.number_of_nodes()}")
        print(f"      边数: {graph.number_of_edges()}")
        
        # 使用修改的Dijkstra算法计算重要性系数
        overall_coefficients = {}
        
        for target in target_species:
            if target not in graph.nodes():
                print(f"      警告：目标组分 {target} 不在图中")
                continue
            
            # 计算从目标组分到所有其他组分的最大路径
            coefficients = self._modified_dijkstra(graph, target)
            coefficients[target] = 1.0  # 确保目标组分的重要性为1.0
            
            # 更新总体系数（取最大值）
            for sp in coefficients:
                overall_coefficients[sp] = max(overall_coefficients.get(sp, 0.0), coefficients[sp])
        
        # 确保所有组分都有系数
        importance_coefficients = {sp: overall_coefficients.get(sp, 0.0) for sp in species_names}
        
        # 统计信息
        non_zero_coeffs = sum(1 for coeff in importance_coefficients.values() if coeff > 1e-10)
        max_coeff = max(importance_coefficients.values()) if importance_coefficients else 0.0
        
        print(f"    重要性系数计算:")
        print(f"      非零系数: {non_zero_coeffs}/{len(species_names)}")
        print(f"      最大系数: {max_coeff:.3e}")
        
        return importance_coefficients
    
    def _modified_dijkstra(self, graph, source):
        """
        修改的Dijkstra算法，用于DRGEP重要性系数计算
        参考原始drgep.py中的mod_dijkstra实现
        
        Args:
            graph: NetworkX有向图
            source: 源节点
        
        Returns:
            dict: 从源节点到各节点的最大路径值
        """
        from itertools import count
        from heapq import heappush, heappop
        
        # 获取邻接表
        if graph.is_directed():
            successors = graph.succ
        else:
            successors = graph.adj
        
        # 初始化
        dist = {}  # 最终距离字典
        seen = {source: 1.0}  # 已见过的节点，源节点初始化为1.0
        c = count()  # 计数器，用于堆排序时的唯一标识
        fringe = []  # 优先队列
        
        # 将源节点加入队列，使用负值实现最大堆，初始距离为-1.0
        heappush(fringe, (-1.0, next(c), source))
        
        while fringe:
            # 弹出距离最小的节点（负值最小 = 正值最大）
            (neg_d, _, v) = heappop(fringe)
            d = -neg_d  # 转换回正值
            
            # 如果节点已处理且当前距离较小，跳过
            if v in dist and d < dist[v]:
                continue
            
            # 记录最终距离
            dist[v] = d
            
            # 处理邻居节点
            for u, edge_data in successors[v].items():
                # 获取边权重
                weight = edge_data.get('weight', 1.0)
                if weight is None or weight <= 0:
                    continue
                
                # 计算新的距离（乘法）- DRGEP使用乘法而不是加法
                vu_dist = dist[v] * weight
                
                # 如果找到更大的路径值，更新
                if u not in seen or vu_dist > seen[u]:
                    seen[u] = vu_dist
                    # 使用负值来实现最大堆（找最大路径）
                    heappush(fringe, (-vu_dist, next(c), u))
        
        return dist 