"""
配置文件 - 集中管理所有常量和配置参数
"""
from typing import Dict, List
import os

# 应用基本信息
APP_TITLE = "ROP分析工具箱V5.0 IET CC"
APP_VERSION = "5.0"
AUTHOR_INFO = "中科院工程热物理研究所田振玉研究员团队\n程序编写：王杜 <EMAIL>  V5.0"

# 文件路径配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TEST_DATA_DIR = os.path.join(BASE_DIR, "TEST")
PAGES_DIR = os.path.join(BASE_DIR, "pages")

# 支持的文件类型
SUPPORTED_GAS_OUT_EXTENSIONS = ['out']
SUPPORTED_CHEMKIN_EXTENSIONS = ['xlsm', 'xlsx']
SUPPORTED_THERMO_EXTENSIONS = ['dat', 'txt']

# 数据处理配置
DEFAULT_THRESHOLD = 0.001  # 默认阈值
DEFAULT_ROP_THRESHOLD = 0.01  # 默认ROP阈值
MAX_DISPLAY_REACTIONS = 50  # 最大显示反应数
PROGRESS_BAR_UPDATE_INTERVAL = 10  # 进度条更新间隔

# 可视化配置
PLOT_CONFIG = {
    'width': 1000,
    'height': 400,
    'font_size': 12,
    'title_font_size': 20,
    'font_color': "RebeccaPurple",
    'line_width': 2,
    'line_width_main': 3
}

# 数值格式配置
NUMBER_FORMATS = {
    'scientific': '{:.2e}',
    'percentage': '{:.2%}',
    'decimal_3': '{:.3f}',
    'decimal_2': '{:.2f}'
}

# 错误消息
ERROR_MESSAGES = {
    'no_preprocessing': '请先进行前处理！',
    'no_postprocessing': '注意重新前处理后必须后处理！否者结果会出现错误',
    'no_gas_file': '没有选择前处理文件',
    'no_chemkin_file': '没有选择后处理文件',
    'preprocessing_failed': '前处理失败！请检查上述报错信息',
    'postprocessing_failed': '后处理失败！请检查上述报错信息',
    'no_reaction_rate': '错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析',
    'integration_failed': '反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..',
    'file_not_exists': '文件不存在！',
    'thermo_import_failed': '热力学文件导入失败，请检查！'
}

# 成功消息
SUCCESS_MESSAGES = {
    'preprocessing_success': '处理成功',
    'postprocessing_success': '后处理成功',
    'thermo_import_success': '导入成功！'
}

# 警告消息
WARNING_MESSAGES = {
    'incomplete_reaction_rate': '注意当前反应速率输出不完全，可能存在反应没有路径连接',
    'no_net_reaction_rate': '注意！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析'
}

# 菜单配置
MENU_OPTIONS = ['首页', '帮助', '关于', '加油!']

# 页面配置
PAGE_CONFIG = {
    'layout': 'wide',
    'page_title': APP_TITLE,
    'page_icon': '🧪'
}

# ROP分析类型
ROP_TYPES = {
    'integral': '积分ROP',
    'end': 'EndPointROP'
}

ROP_TYPE_DESCRIPTIONS = {
    'integral': '将ROP沿时间或距离积分，适合流动管或火焰',
    'end': '取稳态最后一点ROP，适合JSR'
}