"""
CHEMKIN结果文件处理器
"""
import copy
import re
from typing import Dict, List, Optional, Union, IO, Any
import pandas as pd
import numpy as np
from tqdm import tqdm
import streamlit as st

from .exceptions import ChemkinFileError
from ..utils.data_processing import check_dataframe_validity


class ChemkinProcessor:
    """CHEMKIN结果文件处理器"""
    
    def __init__(self, input_file: Union[str, IO]):
        """初始化CHEMKIN处理器"""
        self._init_attributes()
        self._load_file(input_file)
    
    def _init_attributes(self):
        """初始化所有属性"""
        self.temperature_exist = None
        self.xlsx = None
        self.soln_sheets = None
        self.end_point_sheets = None
        self.rop_sheets = None
        self.max_sheets = None
        self.sensitivity_sheets = None
        self.variables = None
        self.num_variable = 0
        self.variable_name = None
        self.variable_unit = None
        self.net_reaction_rate_exist = False
        self.mole_fraction_exist = False
        self.rop_line = False
        self.sensitivity_exist = False
        self.preprocessing = False
        self.ROP_exist = False
    
    def _load_file(self, input_file: Union[str, IO]):
        """加载CHEMKIN文件"""
        try:
            self.xlsx = pd.ExcelFile(input_file)
        except Exception as e:
            raise ChemkinFileError(f"无法加载CHEMKIN文件: {str(e)}")
    
    def load_chemkin_file(self):
        """分析CHEMKIN文件结构"""
        sheets = self.xlsx.sheet_names
        self.soln_sheets = [sheet for sheet in sheets if 'soln' in sheet]
        self.end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
        self.rop_sheets = [sheet for sheet in sheets if 'rop' in sheet]
        self.max_sheets = [sheet for sheet in sheets if 'max_point' in sheet]
        self.sensitivity_sheets = [sheet for sheet in sheets if 'sen' in sheet]
        
        self._analyze_end_point_sheets()
        self._check_data_availability()
        self.preprocessing = True
    
    def _analyze_end_point_sheets(self):
        """分析end_point工作表"""
        if len(self.end_point_sheets) == 0:
            self._handle_no_endpoint_sheets()
            return
        
        end_points_sheet = self.end_point_sheets[0]
        end_points = self.xlsx.parse(end_points_sheet.rstrip())
        
        # 检查变量列
        variable_columns = [re.search('.*_C1_.*', name) for name in end_points.columns
                           if re.search('.*_C1_.*', name) is not None]
        
        self.num_variable = 0
        for variable_column in variable_columns:
            potential_variable = list(end_points.loc[:, variable_column[0]])
            if len(potential_variable) == 1 or len(np.unique(potential_variable)) == 1:
                continue
            else:
                self.variables = potential_variable
                self.num_variable += 1
        
        if self.num_variable > 1:
            print("注意！当前输出结果有不止一个变量，最好分开输出")
            st.warning("注意！当前输出结果有不止一个变量，最好分开输出")
        
        if variable_columns:
            self.variable_name = variable_columns[0][0].strip().split('_')[0]
            self.variable_unit = variable_columns[0][0].strip().split('_')[-1]
    
    def _handle_no_endpoint_sheets(self):
        """处理没有end_point工作表的情况"""
        self.variables = ['unknown']
        if self.soln_sheets:
            first_points = self.xlsx.parse(self.soln_sheets[0].rstrip())
            self._check_required_columns(first_points)
    
    def _check_data_availability(self):
        """检查数据可用性"""
        if self.end_point_sheets:
            end_points = self.xlsx.parse(self.end_point_sheets[0].rstrip())
            self._check_required_columns(end_points)
            
            print(f'导入文件成功\n 导入文件覆盖工况点包括：{self.variable_name}:{self.variables} {self.variable_unit}')
            st.info(f'导入文件覆盖工况点包括：{self.variable_name} {self.variables} {self.variable_unit}', icon="🤖")
    
    def _check_required_columns(self, dataframe: pd.DataFrame):
        """检查必需的数据列"""
        if np.sum(dataframe.columns.str.contains('Net_rxn_rate_')) == 0:
            error_msg = '错误！！！文件中不包含reaction rate信息,无法进行全部组分的ROP分析'
            print(error_msg)
            st.warning(error_msg)
        else:
            self.net_reaction_rate_exist = True
        
        self.mole_fraction_exist = np.sum(dataframe.columns.str.contains('Mole_fraction')) != 0
        self.ROP_exist = np.sum(dataframe.columns.str.contains('_ROP_')) != 0
        self.sensitivity_exist = np.sum(dataframe.columns.str.contains('_Sensitivity_')) != 0
        self.temperature_exist = np.sum(dataframe.columns.str.contains('Temperature')) != 0
    
    def combine_sheets(self, gas_out_file, show_progress: bool = False, mole_only: bool = False):
        """合并工作表数据"""
        if show_progress:
            progress_bar1 = st.progress(0, text='正在合并Sheets')
            progress_bar2 = st.progress(0, text='正在积分ROP值')
            progress_bar3 = st.progress(0, text='正在处理敏感性分析')
        
        print("正在合并Sheets")
        
        # 处理摩尔分数数据
        if self.mole_fraction_exist and len(self.end_point_sheets) != 0:
            self._combine_mole_fraction_sheets(gas_out_file, show_progress, progress_bar1 if show_progress else None)
        
        # 处理敏感性分析数据
        if self.sensitivity_exist:
            self._combine_sensitivity_sheets(gas_out_file, show_progress, progress_bar3 if show_progress else None)
        
        # 处理反应速率数据
        if self.net_reaction_rate_exist:
            self._combine_solution_sheets(gas_out_file, show_progress, progress_bar1 if show_progress else None, progress_bar2 if show_progress else None)
        
        if show_progress:
            if progress_bar1: progress_bar1.empty()
            if progress_bar2: progress_bar2.empty()
            if progress_bar3: progress_bar3.empty()
    
    def _combine_mole_fraction_sheets(self, gas_out_file, show_progress: bool, progress_bar):
        """合并摩尔分数工作表"""
        print("正在合并摩尔分数数据")
        
        # 处理end_point数据
        for i, sheet in enumerate(tqdm(self.end_point_sheets, desc="正在处理工作表", unit='sheets')):
            sheet_end_raw = self.xlsx.parse(sheet, index_col=0)
            mole_frac_cols = sheet_end_raw.columns.str.contains(' Mole_fraction_')
            
            if i == 0:
                new_sheet = sheet_end_raw.iloc[:, mole_frac_cols]
            else:
                sheet_end = sheet_end_raw.iloc[:, mole_frac_cols]
                new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
        
        gas_out_file.mole_fractions = copy.deepcopy(new_sheet)
        gas_out_file.mole_fractions.columns = (
            gas_out_file.mole_fractions.columns
            .str.replace(' Mole_fraction_', '')
            .str.replace('_end_point_()', '', regex=False)
        )
        gas_out_file.mole_fractions.index = self.variables
        
        # 处理max_point数据
        if self.max_sheets:
            self._process_max_point_sheets(gas_out_file)
    
    def _process_max_point_sheets(self, gas_out_file):
        """处理最大值点数据"""
        for i, sheet in enumerate(tqdm(self.max_sheets, desc="正在处理最大值工作表", unit='sheets')):
            sheet_max_raw = self.xlsx.parse(sheet, index_col=0)
            mole_frac_cols = sheet_max_raw.columns.str.contains(' Mole_fraction_')
            
            if i == 0:
                new_sheet = sheet_max_raw.iloc[:, mole_frac_cols]
            else:
                sheet_max = sheet_max_raw.iloc[:, mole_frac_cols]
                new_sheet = pd.concat([new_sheet, sheet_max], axis=1)
        
        gas_out_file.mole_fractions_max = copy.deepcopy(new_sheet)
        gas_out_file.mole_fractions_max.columns = (
            gas_out_file.mole_fractions_max.columns
            .str.replace(' Mole_fraction_', '')
            .str.replace('_max_point_()', '', regex=False)
        )
        gas_out_file.mole_fractions_max.index = self.variables
        gas_out_file.possible_reactants = (
            gas_out_file.mole_fractions_max.T
            .nlargest(8, gas_out_file.mole_fractions_max.index[0])
            .index.tolist()
        )
    
    def _combine_sensitivity_sheets(self, gas_out_file, show_progress: bool, progress_bar):
        """合并敏感性分析工作表"""
        sen_no_p = ''
        sen_collect = {}
        working_conditions = 1
        
        print("正在合并敏感性分析数据")
        for i, sheet in enumerate(tqdm(self.sensitivity_sheets, desc="正在处理敏感性工作表", unit='sheets')):
            sheet_sen_raw = self.xlsx.parse(sheet, index_col=0)
            sheet_sen = sheet_sen_raw.iloc[:, ~sheet_sen_raw.columns.str.contains('Sen')]
            sen_no_c = sheet.split('#')[-1].split('_')[0]
            
            if i == 0:
                new_sheet = sheet_sen
            
            if sen_no_c == sen_no_p:
                new_sheet = pd.concat([new_sheet, sheet_sen], axis=1)
            elif sen_no_c != sen_no_p and i != 0:
                sen_collect[self.variables[working_conditions - 1]] = new_sheet
                new_sheet = sheet_sen
                working_conditions += 1
            
            sen_no_p = sen_no_c
            if progress_bar:
                progress_bar.progress((i + 1) / len(self.sensitivity_sheets),
                                    f"正在处理敏感性分析：{(i + 1)}/{len(self.sensitivity_sheets)}")
        
        sen_collect[self.variables[working_conditions - 1]] = new_sheet
        print('敏感性分析数据处理完毕!')
    
    def _combine_solution_sheets(self, gas_out_file, show_progress: bool, progress_bar1, progress_bar2):
        """合并solution工作表"""
        soln_no_p = ''
        soln_collect = {}
        working_conditions = 1
        
        for i, sheet in enumerate(tqdm(self.soln_sheets, desc="正在处理solution工作表", unit='sheets')):
            sheet_soln_raw = self.xlsx.parse(sheet, index_col=0)
            sheet_soln = sheet_soln_raw.iloc[:, sheet_soln_raw.columns.str.contains('Net')]
            
            if '#' in sheet:
                soln_no_c = sheet.split('#')[-1].split('_')[0]
            else:
                soln_no_c = '1'
            
            if i == 0:
                new_sheet = sheet_soln
            
            if soln_no_c == soln_no_p:
                new_sheet = pd.concat([new_sheet, sheet_soln], axis=1)
            elif soln_no_c != soln_no_p and i != 0:
                soln_collect[self.variables[working_conditions - 1]] = new_sheet
                new_sheet = sheet_soln
                working_conditions += 1
            
            soln_no_p = soln_no_c
            if progress_bar1:
                progress_bar1.progress((i + 1) / len(self.soln_sheets),
                                     f'正在合并Sheets:{i + 1}/{len(self.soln_sheets)}')
        
        soln_collect[self.variables[working_conditions - 1]] = new_sheet
        
        # 存储到两个地方：gas_out_file和self
        gas_out_file.soln_collect = soln_collect
        self.soln_collect = soln_collect  # ROPAnalyzer需要访问这个
        print('Solution数据处理完毕!')
        
        # 计算ROP积分值（仿照原始实现）
        self._calculate_rop_integrals(gas_out_file, soln_collect, progress_bar2)
    
    def _calculate_rop_integrals(self, gas_out_file, soln_collect, progress_bar):
        """计算ROP积分值"""
        import copy
        import numpy as np
        from tqdm import tqdm
        
        stoichi = copy.deepcopy(gas_out_file.stoichimetric)
        
        # 检查反应速率输出是否完整
        if len(soln_collect[self.variables[0]].columns) != len(gas_out_file.reaction_index):
            st.warning("注意当前反应速率输出不完全，可能存在反应没有路径连接")
            
            exist_reaction_index = []
            all_reaction = set([i for i in range(1, len(gas_out_file.reaction_index) + 1)])
            
            for no in soln_collect[self.variables[0]].columns:
                exist_reaction_index.append(int(no.split('#')[1].split('_')[0]))
            no_reaction_index = list(all_reaction - set(exist_reaction_index))
            for r in no_reaction_index:
                print(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                st.warning(f'注意反应{gas_out_file.stoichimetric.index[r - 1]} 没有反应速率输出，其反应物可能路径不全！')
                stoichi.drop(gas_out_file.stoichimetric.index[r - 1], inplace=True)
        
        int_rate = dict()  # 类似的建立积分速率和积分ROP的储存字典
        int_rop = dict()
        end_rate = dict()
        end_rop = dict()
        
        print('正在积分ROP值')
        try:
            for i, temp in enumerate(tqdm(soln_collect, desc="正在积分ROP", unit='温度点')):
                rate = soln_collect[temp]  # 对每一个温度点的rate进行积分
                
                int_rate[temp] = [np.trapz(rate.loc[:, j], rate.index) for j in rate.columns]
                end_rate[temp] = rate.iloc[-1, :].tolist()
                int_rop[temp] = stoichi.apply(lambda x: np.multiply(x, int_rate[temp]), axis=0)
                end_rop[temp] = stoichi.apply(lambda x: np.multiply(x, end_rate[temp]), axis=0)
                gas_out_file.rop_species_output = gas_out_file.species
                
                if progress_bar:
                    progress_bar.progress((i + 1) / len(soln_collect),
                                        f"正在积分ROP 工况点：{(i + 1)}/{len(soln_collect)} ")
            print('处理完毕！')
        except ValueError:
            int_rop = None
            print("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
            st.error("反应积分失败！，请在CHEMKIN后处理文件导出时勾选：Get species zero..")
        
        gas_out_file.integral_ROP = int_rop
        gas_out_file.end_ROP = end_rop 