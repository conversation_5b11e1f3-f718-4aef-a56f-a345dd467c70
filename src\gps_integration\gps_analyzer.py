#!/usr/bin/env python3
"""
GPS分析器集成模块
整合GPS和GPSA算法到当前项目中
"""

import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional

# 添加GPS模块路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
gps_dir = project_root / "GPS"
sys.path.insert(0, str(gps_dir))

try:
    from final_faithful_gps import FinalFaithfulGPS, FinalFaithfulGPSA
except ImportError as e:
    print(f"❌ 无法导入GPS模块: {e}")
    raise

from .data_converter import GPSDataConverter


class IntegratedGPSAnalyzer:
    """集成的GPS分析器"""
    
    def __init__(self):
        self.data_converter = GPSDataConverter()
        self.gps = FinalFaithfulGPS()
        self.gpsa = FinalFaithfulGPSA(self.gps)
        self.current_data = None
        self.gps_results = None
        self.gpsa_results = {}
    
    def load_project_data(self, gas_obj, condition_key: str = None, use_end_point: bool = False):
        """加载项目数据并转换为GPS格式"""
        print("📥 加载项目数据...")
        
        try:
            self.current_data = self.data_converter.prepare_gps_input_data(
                gas_obj=gas_obj,
                condition_key=condition_key,
                use_end_point=use_end_point
            )
            
            # 验证数据
            if not self.data_converter.validate_gps_data(self.current_data):
                raise ValueError("GPS数据验证失败")
            
            print("✅ 项目数据加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def build_flux_graph(self, traced_element: str = 'C'):
        """构建通量图"""
        if not self.current_data:
            raise ValueError("请先加载项目数据")
        
        print(f"🔗 构建通量图 (追踪元素: {traced_element})...")
        
        flux_graph = self.gps.build_flux_graph(
            species_names=self.current_data['species_names'],
            species_elemental_composition=self.current_data['species_elemental_composition'],
            reaction_stoichiometry=self.current_data['reaction_stoichiometry'],
            net_reaction_rates=self.current_data['net_reaction_rates'],
            traced_element=traced_element
        )
        
        print(f"✅ 通量图构建完成: {flux_graph.number_of_nodes()} 节点, {flux_graph.number_of_edges()} 边")
        return flux_graph
    
    def run_gps_analysis(self, 
                        source: str, 
                        target: str, 
                        K: int = 1, 
                        alpha: float = 0.1, 
                        beta: float = 0.5, 
                        normal: str = 'max',
                        traced_element: str = 'C'):
        """运行GPS分析"""
        if not self.current_data:
            raise ValueError("请先加载项目数据")
        
        print(f"🚀 开始GPS分析: {source} → {target}")
        
        # 验证源和目标组分
        species_names = self.current_data['species_names']
        if source not in species_names:
            raise ValueError(f"源组分 '{source}' 不在组分列表中")
        if target not in species_names:
            raise ValueError(f"目标组分 '{target}' 不在组分列表中")
        
        # 构建通量图
        self.build_flux_graph(traced_element)
        
        # 运行GPS算法
        self.gps_results = self.gps.GPS_algorithm(
            source=source,
            target=target,
            K=K,
            alpha=alpha,
            beta=beta,
            normal=normal
        )
        
        print("✅ GPS分析完成")
        return self.gps_results
    
    def run_gpsa_analysis(self, global_pathway_name: str = None):
        """运行GPSA分析"""
        if not self.gps_results:
            raise ValueError("请先运行GPS分析")
        
        if not self.current_data:
            raise ValueError("请先加载项目数据")
        
        print("🔬 开始GPSA分析...")
        
        # 获取全局路径
        global_paths = self.gps_results.get('global_path', {})
        if not global_paths:
            print("⚠️ 未找到全局路径，无法进行GPSA分析")
            return {}
        
        # 如果未指定路径，使用第一个
        if global_pathway_name is None:
            global_pathway_name = list(global_paths.keys())[0]
        
        if global_pathway_name not in global_paths:
            raise ValueError(f"全局路径 '{global_pathway_name}' 不存在")
        
        # 准备GPSA输入
        GP_dict = {
            'name': global_pathway_name,
            'member': global_paths[global_pathway_name]['member'],
            'traced': 'C'  # 默认追踪碳元素
        }
        
        # 运行GPSA分析
        gpsa_result = self.gpsa.calculate_GPSA_metrics(
            GP_dict=GP_dict,
            species_concentrations=self.current_data['species_concentrations'],
            net_reaction_rates=self.current_data['net_reaction_rates'],
            reaction_enthalpies=self.current_data['reaction_enthalpies'],
            fuel_composition=self.current_data['fuel_composition'],
            species_elemental_composition=self.current_data['species_elemental_composition'],
            traced_element='C'
        )
        
        self.gpsa_results[global_pathway_name] = gpsa_result
        
        print("✅ GPSA分析完成")
        return gpsa_result
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析结果摘要"""
        if not self.gps_results:
            return {"error": "未运行GPS分析"}
        
        summary = {
            'gps_parameters': self.gps_results.get('parameter', {}),
            'gps_summary': self.gps_results.get('summary', {}),
            'global_pathways': list(self.gps_results.get('global_path', {}).keys()),
            'hub_species': list(self.gps_results.get('hubs', {}).keys()),
            'selected_species_count': len(self.gps_results.get('species', {})),
            'gpsa_analyzed_pathways': list(self.gpsa_results.keys()),
            'data_info': self.current_data.get('condition_info', {}) if self.current_data else {}
        }
        
        return summary
    
    def get_species_selection_details(self) -> Dict[str, Any]:
        """获取组分选择详情"""
        if not self.gps_results:
            return {}
        
        species_data = self.gps_results.get('species', {})
        
        # 按选择方式分类
        by_alpha = []
        by_K = []
        by_beta = []
        
        for species, selection_info in species_data.items():
            if selection_info.get('by_alpha', False):
                by_alpha.append(species)
            if selection_info.get('by_K', []):
                by_K.append({
                    'species': species,
                    'pathways': selection_info['by_K']
                })
            if selection_info.get('by_beta', []):
                by_beta.append({
                    'species': species,
                    'reactions': selection_info['by_beta']
                })
        
        return {
            'by_alpha': by_alpha,
            'by_K': by_K,
            'by_beta': by_beta,
            'total_selected': len(species_data)
        }
    
    def get_pathway_details(self) -> List[Dict[str, Any]]:
        """获取路径详情"""
        if not self.gps_results:
            return []
        
        global_paths = self.gps_results.get('global_path', {})
        pathway_details = []
        
        for pathway_name, pathway_info in global_paths.items():
            detail = {
                'name': pathway_name,
                'species_sequence': pathway_info.get('member', []),
                'length': len(pathway_info.get('member', [])),
                'has_gpsa': pathway_name in self.gpsa_results
            }
            
            if detail['has_gpsa']:
                gpsa_data = self.gpsa_results[pathway_name]
                detail['gpsa_metrics'] = {
                    'R_GP': gpsa_data.get('R_GP', []),
                    'Q_GP': gpsa_data.get('Q_GP', []),
                    'D_GP': gpsa_data.get('D_GP', [])
                }
            
            pathway_details.append(detail)
        
        return pathway_details
    
    def export_results(self, output_dir: str = "gps_results"):
        """导出分析结果"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 导出GPS结果
        if self.gps_results:
            import json
            with open(output_path / "gps_results.json", 'w', encoding='utf-8') as f:
                # 转换numpy数组为列表以便JSON序列化
                exportable_results = self._make_json_serializable(self.gps_results)
                json.dump(exportable_results, f, ensure_ascii=False, indent=2)
        
        # 导出GPSA结果
        if self.gpsa_results:
            with open(output_path / "gpsa_results.json", 'w', encoding='utf-8') as f:
                exportable_gpsa = self._make_json_serializable(self.gpsa_results)
                json.dump(exportable_gpsa, f, ensure_ascii=False, indent=2)
        
        # 导出摘要
        summary = self.get_analysis_summary()
        with open(output_path / "analysis_summary.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已导出到: {output_path}")
        return output_path
    
    def _make_json_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        else:
            return obj
