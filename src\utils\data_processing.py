"""
数据处理工具模块
"""
import numpy as np
import pandas as pd
from typing import Union, List, Dict, Tuple, Optional, Any
import warnings

warnings.filterwarnings("ignore")


def format_dataframe_for_display(df: pd.DataFrame, format_dict: Dict[str, str] = None) -> pd.DataFrame:
    """格式化DataFrame用于显示"""
    if format_dict is None:
        format_dict = {}
    
    formatted_df = df.copy()
    
    for col, fmt in format_dict.items():
        if col in formatted_df.columns:
            formatted_df[col] = formatted_df[col].apply(
                lambda x: fmt.format(x) if isinstance(x, (int, float)) else x)
    
    return formatted_df


def sort_by_overall_contribution(df: pd.DataFrame, threshold: float = 0.001) -> pd.DataFrame:
    """根据总体贡献度排序并过滤DataFrame"""
    df_with_overall = df.copy()
    df_with_overall.loc['overall'] = np.sum(df_with_overall.apply(lambda x: x ** 2), 0)
    
    df_with_overall.sort_values('overall', axis=1, inplace=True, ascending=False)
    df_sorted = df_with_overall.drop('overall', axis=0)
    
    max_values = df_sorted.abs().max()
    df_filtered = df_sorted.loc[:, max_values > threshold]
    
    return df_filtered


def check_dataframe_validity(df: pd.DataFrame) -> bool:
    """检查DataFrame的有效性"""
    if df is None:
        return False
    if df.empty:
        return False
    if df.isna().all().all():
        return False
    return True


def validate_temperature_range(temp_min: float, temp_max: float) -> bool:
    """验证温度范围的有效性"""
    if temp_min >= temp_max:
        return False
    if temp_min < 0 or temp_max < 0:
        return False
    if temp_max > 5000:  # 合理的温度上限
        return False
    return True


def safe_numeric_conversion(value: Any, default: float = 0.0) -> float:
    """安全地将值转换为数值"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def normalize_dataframe(df: pd.DataFrame, method: str = 'minmax') -> pd.DataFrame:
    """标准化DataFrame数据"""
    if not check_dataframe_validity(df):
        return df
    
    df_normalized = df.copy()
    
    if method == 'minmax':
        # Min-Max标准化
        df_normalized = (df - df.min()) / (df.max() - df.min())
    elif method == 'zscore':
        # Z-score标准化
        df_normalized = (df - df.mean()) / df.std()
    elif method == 'robust':
        # 基于中位数的标准化
        median = df.median()
        mad = np.median(np.abs(df - median))
        df_normalized = (df - median) / mad
    
    return df_normalized.fillna(0)


def filter_significant_reactions(df: pd.DataFrame, threshold: float = 0.01) -> pd.DataFrame:
    """过滤出重要的反应"""
    if not check_dataframe_validity(df):
        return df
    
    # 计算每行的最大绝对值
    max_values = df.abs().max(axis=1)
    significant_mask = max_values >= threshold
    
    return df[significant_mask]


def clean_column_names(df: pd.DataFrame) -> pd.DataFrame:
    """清理DataFrame的列名"""
    df_clean = df.copy()
    
    # 移除特殊字符和多余空格
    df_clean.columns = (df_clean.columns
                        .str.strip()
                        .str.replace(r'[^\w\s]', '', regex=True)
                        .str.replace(r'\s+', '_', regex=True))
    
    return df_clean


def interpolate_missing_data(df: pd.DataFrame, method: str = 'linear') -> pd.DataFrame:
    """插值填补缺失数据"""
    if not check_dataframe_validity(df):
        return df
    
    df_interpolated = df.copy()
    
    for column in df_interpolated.columns:
        if df_interpolated[column].dtype in ['float64', 'int64']:
            df_interpolated[column] = df_interpolated[column].interpolate(method=method)
    
    return df_interpolated


def calculate_reaction_contribution(df: pd.DataFrame, species: str) -> pd.Series:
    """计算特定组分的反应贡献"""
    if not check_dataframe_validity(df):
        return pd.Series()
    
    if species not in df.columns:
        return pd.Series()
    
    species_data = df[species]
    total_contribution = species_data.abs().sum()
    
    if total_contribution == 0:
        return pd.Series(index=df.index, data=0.0)
    
    return (species_data.abs() / total_contribution * 100)


def aggregate_by_temperature(df: pd.DataFrame, temp_column: str = 'Temperature') -> pd.DataFrame:
    """按温度聚合数据"""
    if not check_dataframe_validity(df):
        return df
    
    if temp_column not in df.columns:
        return df
    
    # 按温度分组并计算平均值
    aggregated = df.groupby(temp_column).mean()
    
    return aggregated