"""
ROP（反应路径分析）分析器
"""
import copy
import re
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
import plotly.graph_objs as go
from plotly.subplots import make_subplots
from tqdm import tqdm
import streamlit as st

from .exceptions import DataProcessingError, CalculationError
from ..utils.data_processing import filter_significant_reactions, check_dataframe_validity


class ROPAnalyzer:
    """ROP分析器，提供反应路径分析功能"""
    
    def __init__(self, gas_processor, chemkin_processor):
        """初始化ROP分析器"""
        self.gas_processor = gas_processor
        self.chemkin_processor = chemkin_processor
        self.integral_ROP = {}
        self.max_ROP = {}
        self.processed_data = False
    
    def process_rop_data(self, show_progress: bool = False):
        """处理ROP数据"""
        # 检查CHEMKIN数据是否已处理
        if not hasattr(self.chemkin_processor, 'soln_collect'):
            raise DataProcessingError("请先运行combine_sheets方法处理CHEMKIN数据")
        
        # 检查gas_processor中是否已有ROP计算结果
        if hasattr(self.gas_processor, 'integral_ROP') and self.gas_processor.integral_ROP is not None:
            print("检测到已有ROP积分数据，直接使用...")
            self.integral_ROP = self.gas_processor.integral_ROP
            self.max_ROP = getattr(self.gas_processor, 'end_ROP', {})
            self.processed_data = True
            print("ROP数据加载完成！")
            return
        
        print("正在处理ROP数据...")
        
        # 初始化进度条
        if show_progress:
            progress_bar = st.progress(0, text='正在积分ROP值')
        
        soln_collect = self.chemkin_processor.soln_collect
        variables = self.chemkin_processor.variables
        
        # 处理每个工况
        for i, variable in enumerate(tqdm(variables, desc="处理ROP数据", unit='工况')):
            if show_progress:
                progress_bar.progress((i + 1) / len(variables), 
                                    f"正在处理工况 {i + 1}/{len(variables)}: {variable}")
            
            soln_data = soln_collect[variable]
            
            # 积分ROP值
            integral_rop = self._calculate_integral_rop(soln_data)
            self.integral_ROP[variable] = integral_rop
            
            # 计算最大ROP值
            max_rop = self._calculate_max_rop(soln_data)
            self.max_ROP[variable] = max_rop
        
        if show_progress:
            progress_bar.empty()
        
        self.processed_data = True
        print("ROP数据处理完成！")
    
    def _calculate_integral_rop(self, soln_data: pd.DataFrame) -> pd.DataFrame:
        """计算积分ROP值"""
        try:
            # 使用梯形积分计算ROP积分
            integral_rop = pd.DataFrame(index=self.gas_processor.species, 
                                      columns=[f'reaction_{i}' for i in range(len(self.gas_processor.reactions))])
            
            for species in self.gas_processor.species:
                for i, reaction in enumerate(self.gas_processor.reactions):
                    # 获取反应速率数据
                    rate_column = f'Net_rxn_rate_{reaction.split("#")[1]}'
                    if rate_column in soln_data.columns:
                        rate_data = soln_data[rate_column].dropna()
                        if len(rate_data) > 1:
                            # 计算时间序列的积分
                            time_points = np.arange(len(rate_data))
                            integral_value = np.trapz(rate_data, time_points)
                            
                            # 乘以化学计量数
                            stoich_coeff = self.gas_processor.stoichimetric.loc[reaction, species]
                            integral_rop.loc[species, f'reaction_{i}'] = integral_value * stoich_coeff
                        else:
                            integral_rop.loc[species, f'reaction_{i}'] = 0.0
                    else:
                        integral_rop.loc[species, f'reaction_{i}'] = 0.0
            
            return integral_rop.astype(float)
        
        except Exception as e:
            raise CalculationError(f"计算积分ROP时出错: {str(e)}")
    
    def _calculate_max_rop(self, soln_data: pd.DataFrame) -> pd.DataFrame:
        """计算最大ROP值"""
        try:
            max_rop = pd.DataFrame(index=self.gas_processor.species,
                                 columns=[f'reaction_{i}' for i in range(len(self.gas_processor.reactions))])
            
            for species in self.gas_processor.species:
                for i, reaction in enumerate(self.gas_processor.reactions):
                    rate_column = f'Net_rxn_rate_{reaction.split("#")[1]}'
                    if rate_column in soln_data.columns:
                        rate_data = soln_data[rate_column].dropna()
                        if len(rate_data) > 0:
                            max_rate = rate_data.abs().max()
                            stoich_coeff = self.gas_processor.stoichimetric.loc[reaction, species]
                            max_rop.loc[species, f'reaction_{i}'] = max_rate * stoich_coeff
                        else:
                            max_rop.loc[species, f'reaction_{i}'] = 0.0
                    else:
                        max_rop.loc[species, f'reaction_{i}'] = 0.0
            
            return max_rop.astype(float)
        
        except Exception as e:
            raise CalculationError(f"计算最大ROP时出错: {str(e)}")
    
    def plot_single_species_rop(self, species: str, temperature: Any, 
                               threshold: float = 0.001, datatype: str = 'integral', 
                               plot: bool = True) -> Optional[go.Figure]:
        """绘制单个组分的ROP图"""
        if not self.processed_data:
            raise DataProcessingError("请先调用process_rop_data方法处理数据")
        
        if species not in self.gas_processor.species:
            st.error(f"组分 '{species}' 不存在")
            return None
        
        # 选择数据类型
        if datatype == 'integral':
            rop_data = self.integral_ROP[temperature]
        else:
            rop_data = self.max_ROP[temperature]
        
        # 获取该组分的ROP数据
        species_rop = rop_data.loc[species]
        
        # 过滤显著反应
        significant_reactions = species_rop[species_rop.abs() >= threshold]
        
        if len(significant_reactions) == 0:
            st.warning(f"在阈值 {threshold} 下没有找到显著反应")
            return None
        
        # 分离正负贡献
        positive_reactions = significant_reactions[significant_reactions > 0].sort_values(ascending=False)
        negative_reactions = significant_reactions[significant_reactions < 0].sort_values(ascending=True)
        
        # 创建图表
        fig = go.Figure()
        
        # 添加正贡献
        if len(positive_reactions) > 0:
            fig.add_trace(go.Bar(
                x=positive_reactions.index,
                y=positive_reactions.values,
                name='生成反应',
                marker_color='red',
                text=[f'{val:.2e}' for val in positive_reactions.values],
                textposition='auto'
            ))
        
        # 添加负贡献
        if len(negative_reactions) > 0:
            fig.add_trace(go.Bar(
                x=negative_reactions.index,
                y=negative_reactions.values,
                name='消耗反应',
                marker_color='blue',
                text=[f'{val:.2e}' for val in negative_reactions.values],
                textposition='auto'
            ))
        
        # 更新布局
        fig.update_layout(
            title=f'{species} 在 {temperature} 条件下的ROP分析 ({datatype})',
            xaxis_title='反应',
            yaxis_title=f'ROP ({datatype})',
            barmode='relative',
            showlegend=True,
            height=600
        )
        
        if plot:
            st.plotly_chart(fig, use_container_width=True)
        
        return fig
    
    def get_flux_analysis(self, species: str, temperature: Any, 
                         element: str, threshold: float = 0.01) -> pd.DataFrame:
        """获取元素流向分析"""
        if not self.processed_data:
            raise DataProcessingError("请先调用process_rop_data方法处理数据")
        
        if species not in self.gas_processor.species:
            raise DataProcessingError(f"组分 '{species}' 不存在")
        
        if element not in self.gas_processor.elements:
            raise DataProcessingError(f"元素 '{element}' 不存在")
        
        # 获取积分ROP数据
        rop_data = self.integral_ROP[temperature]
        species_rop = rop_data.loc[species]
        
        # 计算元素流向
        flux_data = []
        
        for reaction_id, rop_value in species_rop.items():
            if abs(rop_value) < threshold:
                continue
            
            reaction_index = int(reaction_id.split('_')[1])
            reaction_equation = self.gas_processor.reactions[reaction_index]
            
            # 计算该反应中该元素的流向
            flux_factor = self._calculate_flux_factor(reaction_equation, species, element)
            element_flux = rop_value * flux_factor
            
            flux_data.append({
                'reaction': reaction_equation,
                'rop_value': rop_value,
                'flux_factor': flux_factor,
                'element_flux': element_flux
            })
        
        flux_df = pd.DataFrame(flux_data)
        
        if len(flux_df) > 0:
            flux_df = flux_df.sort_values('element_flux', key=abs, ascending=False)
        
        return flux_df
    
    def _calculate_flux_factor(self, reaction: str, species: str, element: str) -> float:
        """计算流向因子"""
        try:
            # 获取组分中该元素的系数
            if species in self.gas_processor.species_element.index and element in self.gas_processor.species_element.columns:
                element_count = self.gas_processor.species_element.loc[species, element]
                return float(element_count)
            else:
                return 0.0
        except Exception:
            return 0.0
    
    def get_reaction_summary(self, temperature: Any, threshold: float = 0.01) -> pd.DataFrame:
        """获取反应汇总信息"""
        if not self.processed_data:
            raise DataProcessingError("请先调用process_rop_data方法处理数据")
        
        rop_data = self.integral_ROP[temperature]
        
        summary_data = []
        
        for reaction in self.gas_processor.reactions:
            reaction_index = self.gas_processor.reactions.index(reaction)
            reaction_column = f'reaction_{reaction_index}'
            
            if reaction_column in rop_data.columns:
                reaction_rop = rop_data[reaction_column]
                
                # 计算总贡献
                total_contribution = reaction_rop.abs().sum()
                
                if total_contribution >= threshold:
                    # 找到主要参与的组分
                    significant_species = reaction_rop[reaction_rop.abs() >= threshold/10]
                    
                    summary_data.append({
                        'reaction': reaction,
                        'total_contribution': total_contribution,
                        'main_species': ', '.join(significant_species.index.tolist()[:5]),
                        'max_species_contribution': reaction_rop.abs().max(),
                        'num_significant_species': len(significant_species)
                    })
        
        summary_df = pd.DataFrame(summary_data)
        
        if len(summary_df) > 0:
            summary_df = summary_df.sort_values('total_contribution', ascending=False)
        
        return summary_df 