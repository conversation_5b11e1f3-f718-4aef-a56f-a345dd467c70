"""
自定义异常类
"""


class ROPAnalysisError(Exception):
    """ROP分析相关错误的基类"""
    pass


class FileProcessingError(ROPAnalysisError):
    """文件处理异常"""
    pass


class GasFileError(ROPAnalysisError):
    """Gas.out文件处理错误"""
    pass


class ChemkinFileError(ROPAnalysisError):
    """CHEMKIN结果文件处理错误"""
    pass


class ThermoFileError(ROPAnalysisError):
    """热力学文件处理错误"""
    pass


class DataValidationError(ROPAnalysisError):
    """数据验证异常"""
    pass


class VisualizationError(ROPAnalysisError):
    """可视化异常"""
    pass


class ConfigurationError(ROPAnalysisError):
    """配置错误异常"""
    pass


class InsufficientDataError(ROPAnalysisError):
    """数据不足异常"""
    pass


class ReactionRateError(ROPAnalysisError):
    """反应速率计算异常"""
    pass


class DataProcessingError(ROPAnalysisError):
    """数据处理错误"""
    pass


class FileFormatError(ROPAnalysisError):
    """文件格式错误"""
    pass


class ParameterError(ROPAnalysisError):
    """参数错误"""
    pass


class CalculationError(ROPAnalysisError):
    """计算错误"""
    pass


class ChemkinProcessingError(ROPAnalysisError):
    """CHEMKIN数据处理错误"""
    pass