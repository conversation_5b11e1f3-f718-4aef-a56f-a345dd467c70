"""
结果对比分析模块

负责处理实验与模拟结果的对比分析和可视化
从solution0606.py重构而来的功能模块
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.colors
import plotly.express as px
import streamlit as st
import datetime
import os
import copy
from typing import List, Dict, Any, Optional, Union, Tuple
import tempfile


class ComparisonAnalyzer:
    """结果对比分析器"""
    
    @staticmethod
    def create_comparison_plot(
        exp_file, 
        simu_files: List, 
        store_path: str, 
        progress_bar: bool = False,
        selected_x_var: str = None,
        selected_x_data: List = None,
        debug_mode: bool = False
    ) -> Dict[str, pd.DataFrame]:
        """
        创建实验与模拟结果对比图
        
        Args:
            exp_file: 实验数据文件
            simu_files: 模拟数据文件列表
            store_path: 存储路径
            progress_bar: 是否显示进度条
            selected_x_var: 用户选择的横坐标变量名
            selected_x_data: 用户选择的横坐标数据
            
        Returns:
            包含各文件数据的字典
        """
        # 读取实验数据
        exp_data = pd.read_excel(exp_file, index_col=0)
        
        # 初始化变量
        files = simu_files
        file_names = []
        mole_fractions = {}
        data_plot = {}
        
        # 设置进度条
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在处理反应信息')
        else:
            progress_bar1 = None
            
        # 处理每个模拟文件
        for file in files:
            file_name = file.name
            file_names.append(file.name)
            sheets = pd.ExcelFile(file).sheet_names
            xl = pd.ExcelFile(file)
            
            # 查找end_point工作表
            end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
            
            if end_point_sheets:
                # 原有逻辑：有end_point sheets的情况
                for i, sheet in enumerate(end_point_sheets):
                    sheet_end_raw = xl.parse(sheet, index_col=0)
                    
                    if i == 0:
                        new_sheet = sheet_end_raw.iloc[:, 
                                    sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                    else:
                        sheet_end = sheet_end_raw.iloc[:, 
                                   sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                        new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
                        
                    # 处理摩尔分数数据
                    mole_fractions[file_name] = copy.deepcopy(new_sheet)
                    mole_fractions[file_name].columns = (
                        mole_fractions[file_name].columns
                        .str.replace(' Mole_fraction_', '', regex=False)
                        .str.replace('_end_point_()', '', regex=False)
                    )
                    
                    # 获取变量信息
                    variable = sheet_end_raw.iloc[:, 
                              sheet_end_raw.columns.str.contains('.*_C1_.*')]
                    
                    if len(sheet_end_raw) == 0:
                        if progress_bar1:
                            progress_bar1.empty()
                        return None
                        
                    if len(variable.T) > 1:
                        print('注意，导出了两种反应变量，会导致绘图出现错误，请选一种')
                        if progress_bar1:
                            progress_bar1.empty()
                        return None
                    else:
                        mole_fractions[file_name].index = variable.iloc[:, 0]
                        data_plot[file_name] = pd.DataFrame(
                            index=variable.iloc[:, 0], 
                            columns=exp_data.columns
                        )
            else:
                # 增强的新逻辑：没有end_point sheets，从soln_sheets提取端点数据
                soln_sheets = [sheet for sheet in sheets if 'soln_' in sheet and 'rop_' not in sheet]
                
                if not soln_sheets:
                    if progress_bar1:
                        progress_bar1.empty()
                    st.error(f"文件 {file_name} 中没有找到soln_sheets或end_point_sheets")
                    return None
                
                # 从soln_sheets提取端点数据（支持第一点和最后一点）
                extracted_data = ComparisonAnalyzer._extract_endpoints_from_solution_sheets_enhanced(
                    xl, soln_sheets, progress_bar1
                )
                
                if extracted_data is None:
                    if progress_bar1:
                        progress_bar1.empty()
                    return None
                
                mole_fractions[file_name] = extracted_data['mole_fractions']
                
                # 添加调试信息（转置前）
                if debug_mode and hasattr(st, 'info'):
                    st.info(f"调试信息 - 文件 {file_name} (转置前):")
                    st.info(f"  摩尔分数数据形状: {mole_fractions[file_name].shape}")
                    st.info(f"  摩尔分数数据行索引(组分名): {list(mole_fractions[file_name].index[:5])}")
                    st.info(f"  摩尔分数数据列索引(工况ID): {list(mole_fractions[file_name].columns)}")
                    if selected_x_data is not None:
                        st.info(f"  用户选择的横坐标数据长度: {len(selected_x_data)}")
                        st.info(f"  用户选择的横坐标数据: {selected_x_data}")
                
                                # 使用用户选择的横坐标
                if selected_x_data is not None:
                    # 检查长度匹配
                    if len(selected_x_data) == len(mole_fractions[file_name].columns):
                        # 转置数据结构：让横坐标成为索引，组分成为列
                        mole_fractions[file_name] = mole_fractions[file_name].T
                        mole_fractions[file_name].index = selected_x_data
                        
                        # 添加转置后的调试信息
                        if debug_mode and hasattr(st, 'info'):
                            st.info(f"调试信息 - 文件 {file_name} (转置后):")
                            st.info(f"  摩尔分数数据形状: {mole_fractions[file_name].shape}")
                            st.info(f"  摩尔分数数据行索引(横坐标): {list(mole_fractions[file_name].index)}")
                            st.info(f"  摩尔分数数据列索引(组分名): {list(mole_fractions[file_name].columns[:5])}")
                        
                        # 填充实际的摩尔分数数据到data_plot
                        data_plot[file_name] = pd.DataFrame(
                            index=selected_x_data,
                            columns=exp_data.columns
                        )
                        # 填充实际数据
                        for species in exp_data.columns:
                            if species in mole_fractions[file_name].columns:
                                data_plot[file_name][species] = mole_fractions[file_name][species]
                    else:
                        # 长度不匹配，尝试匹配前N个数据
                        min_length = min(len(selected_x_data), len(mole_fractions[file_name].columns))
                        if debug_mode and hasattr(st, 'warning'):
                            st.warning(f"横坐标数据长度({len(selected_x_data)})与摩尔分数工况数({len(mole_fractions[file_name].columns)})不匹配")
                            st.warning(f"将使用前{min_length}个匹配的数据点")
                        
                        # 截取匹配的数据并转置
                        mole_fractions[file_name] = mole_fractions[file_name].iloc[:, :min_length].T
                        mole_fractions[file_name].index = selected_x_data[:min_length]
                        # 填充实际的摩尔分数数据到data_plot
                        data_plot[file_name] = pd.DataFrame(
                            index=selected_x_data[:min_length],
                            columns=exp_data.columns
                        )
                        # 填充实际数据
                        for species in exp_data.columns:
                            if species in mole_fractions[file_name].columns:
                                data_plot[file_name][species] = mole_fractions[file_name][species]
                else:
                    # 使用默认索引（工况ID）并转置
                    mole_fractions[file_name] = mole_fractions[file_name].T
                    # 填充实际的摩尔分数数据到data_plot
                    data_plot[file_name] = pd.DataFrame(
                        index=mole_fractions[file_name].index,
                        columns=exp_data.columns
                    )
                    # 填充实际数据
                    for species in exp_data.columns:
                        if species in mole_fractions[file_name].columns:
                            data_plot[file_name][species] = mole_fractions[file_name][species]
        
        # 提取变量名称和单位
        if selected_x_var:
            variable_name = selected_x_var
            variable_unit = ""  # 用户选择的变量，单位可能需要额外处理
        elif 'variable' in locals():
            variable_name = variable.columns.str.split('_')[0][0]
            variable_unit = variable.columns.str.split('_')[0][-1]
        else:
            variable_name = "Variable"
            variable_unit = "Unit"
        
        # 使用优化的绘图函数
        fig = ComparisonAnalyzer._create_enhanced_comparison_plot(
            exp_data, mole_fractions, file_names, variable_name, variable_unit, progress_bar1, debug_mode
        )
        
        # 保存HTML文件
        current_datetime = datetime.datetime.now()
        formatted_datetime = current_datetime.strftime("%Y%m%d-%H-%M")
        html_filename = f'{store_path}/{formatted_datetime}对比结果.html'
        
        fig.write_html(html_filename)
        
        # 清理进度条
        if progress_bar1:
            progress_bar1.empty()
            
        # 如果可能，打开HTML文件
        try:
            if os.path.exists(html_filename):
                os.startfile(html_filename)
        except Exception:
            pass  # 忽略无法打开文件的错误
        
        # 验证data_plot数据完整性
        if debug_mode:
            ComparisonAnalyzer.validate_data_plot(data_plot, debug_mode=True)
            
        return data_plot

    @staticmethod
    def _create_enhanced_comparison_plot(exp_data, mole_fractions, file_names, variable_name, variable_unit, progress_bar1, debug_mode=False):
        """
        创建优化的对比图表
        
        Args:
            exp_data: 实验数据
            mole_fractions: 摩尔分数数据字典
            file_names: 模拟文件名列表
            variable_name: 变量名称
            variable_unit: 变量单位
            progress_bar1: 进度条
            debug_mode: 调试模式
            
        Returns:
            plotly图表对象
        """
        # 现代化配色方案 - 使用科学可视化友好的配色
        simulation_colors = [
            '#1f77b4',  # 蓝色
            '#ff7f0e',  # 橙色
            '#2ca02c',  # 绿色
            '#d62728',  # 红色
            '#9467bd',  # 紫色
            '#8c564b',  # 棕色
            '#e377c2',  # 粉色
            '#7f7f7f',  # 灰色
            '#bcbd22',  # 橄榄色
            '#17becf',  # 青色
        ]
        
        # 实验数据配色 - 使用更醒目的颜色和样式
        exp_color = '#e74c3c'  # 醒目的红色
        
        # 计算子图布局
        n_species = len(exp_data.columns)
        cols = 2
        rows = (n_species + cols - 1) // cols
        
        # 动态计算垂直间距，避免间距过大导致错误
        if rows <= 1:
            vertical_spacing = 0.05
        elif rows <= 3:
            vertical_spacing = 0.02  # 进一步减小间距
        else:
            # 确保间距不超过 1/(rows-1) 的60%，进一步减小
            max_spacing = 0.4 / (rows - 1) if rows > 1 else 0.02
            vertical_spacing = min(0.02, max_spacing)
        
        # 创建子图，优化间距和标题样式
        fig = make_subplots(
            rows=rows, 
            cols=cols, 
            subplot_titles=[f"<b>{species}</b>" for species in exp_data.columns],
            vertical_spacing=vertical_spacing,
            horizontal_spacing=0.12,
            specs=[[{"secondary_y": False} for _ in range(cols)] for _ in range(rows)]
        )
        
        # 绘制每个组分的对比图
        for num_p, species in enumerate(exp_data.columns):
            if progress_bar1:
                progress_bar1.progress((num_p + 1) / len(exp_data.columns))
                
            row = num_p // cols + 1
            col = num_p % cols + 1
            
            # 添加模拟数据轨迹 - 使用legendgroup实现统一控制
            for i, file_name in enumerate(file_names):
                try:
                    # 简化文件名用于图例显示
                    display_name = file_name.replace('.xlsx', '').replace('.xlsm', '')
                    if len(display_name) > 20:
                        display_name = display_name[:17] + '...'
                    
                    fig.add_trace(
                        go.Scatter(
                            x=mole_fractions[file_name].index,
                            y=mole_fractions[file_name][species],
                            mode='lines',  # 修改：只使用线条，不使用标记点
                            name=f'模拟 - {display_name}',
                            legendgroup=f'sim_{i}',  # 关键：使用legendgroup实现统一控制
                            line=dict(
                                color=simulation_colors[i % len(simulation_colors)], 
                                width=2.5,
                                dash='solid'
                            ),
                            showlegend=(num_p == 0),  # 只在第一个子图显示图例
                            hovertemplate=(
                                f'<b>{display_name}</b><br>' +
                                f'{variable_name}: %{{x}}<br>' +
                                f'{species}: %{{y:.3e}}<br>' +
                                '<extra></extra>'
                            )
                        ),
                        row=row,
                        col=col
                    )
                except KeyError:
                    if debug_mode and hasattr(st, 'warning'):
                        st.warning(f'警告，{file_name}机理文件中没有找到{species}')
                        
            # 添加实验数据轨迹 - 使用legendgroup实现统一控制
            fig.add_trace(
                go.Scatter(
                    x=exp_data.index,
                    y=exp_data[species],
                    mode='markers',
                    name='实验数据',
                    legendgroup='exp',  # 关键：使用legendgroup实现统一控制
                    marker=dict(
                        color=exp_color,
                        size=8,
                        symbol='diamond',
                        line=dict(width=2, color='white')
                    ),
                    showlegend=(num_p == 0),  # 只在第一个子图显示图例
                    hovertemplate=(
                        '<b>实验数据</b><br>' +
                        f'{variable_name}: %{{x}}<br>' +
                        f'{species}: %{{y:.3e}}<br>' +
                        '<extra></extra>'
                    )
                ),
                row=row,
                col=col
            )
            
            # 优化坐标轴样式
            fig.update_xaxes(
                title_text=f"{variable_name} {variable_unit}",
                title_font=dict(size=12, family='Arial, sans-serif'),
                tickfont=dict(size=10),
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True,
                row=row, 
                col=col
            )
            
            fig.update_yaxes(
                title_text="摩尔分数",
                title_font=dict(size=12, family='Arial, sans-serif'),
                tickfont=dict(size=10),
                tickformat='.2e',
                exponentformat='e',
                showgrid=True,
                gridwidth=1,
                gridcolor='rgba(128,128,128,0.2)',
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True,
                rangemode='tozero',
                row=row, 
                col=col
            )
        
        # 计算图例位置
        legend_y_position = 1.02 if rows <= 2 else 1.05
        
        # 更新整体布局 - 现代化样式
        fig.update_layout(
            title=dict(
                text='<b>实验与模拟结果对比分析</b>',
                x=0.5,
                xanchor='center',
                font=dict(size=18, family='Arial, sans-serif', color='#2c3e50')
            ),
            legend=dict(
                x=0.5,
                y=legend_y_position,
                xanchor='center',
                yanchor='bottom',
                orientation='h',
                font=dict(size=12, family='Arial, sans-serif'),
                bgcolor='rgba(255, 255, 255, 0.8)',
                bordercolor='rgba(0, 0, 0, 0.2)',
                borderwidth=1,
                tracegroupgap=10
            ),
            # 整体样式
            plot_bgcolor='white',
            paper_bgcolor='white',
            height=400 * rows,
            width=1000,
            font=dict(family='Arial, sans-serif'),
            # 添加现代化的边距
            margin=dict(l=60, r=60, t=80, b=50)  # 减小顶部边距
        )
        
        # 自定义子图标题样式
        for i, annotation in enumerate(fig['layout']['annotations']):
            if annotation['text'] in exp_data.columns:
                # 更新标题样式
                annotation['font'] = dict(size=14, color='#34495e', family='Arial, sans-serif')
                annotation['text'] = f"<b>{annotation['text']}</b>"
        
        return fig
    
    @staticmethod
    def save_comparison_data(save_path: str, data_plot: Dict[str, pd.DataFrame], debug_mode: bool = False) -> None:
        """
        保存对比数据到Excel文件
        
        Args:
            save_path: 保存路径
            data_plot: 要保存的数据字典
            debug_mode: 调试模式
        """
        try:
            if debug_mode and hasattr(st, 'info'):
                st.info(f"正在保存 {len(data_plot)} 个数据表到Excel文件")
                
            with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                for key, dataframe in data_plot.items():
                    # 限制工作表名称长度
                    sheet_name = key[:31] if len(key) > 31 else key
                    
                    if debug_mode and hasattr(st, 'info'):
                        st.info(f"  保存工作表: {sheet_name}, 数据形状: {dataframe.shape}")
                        if not dataframe.empty:
                            non_null_cols = dataframe.count().sum()
                            st.info(f"    非空数据点: {non_null_cols}")
                        else:
                            st.warning(f"    警告: 工作表 {sheet_name} 为空")
                    
                    dataframe.to_excel(writer, sheet_name=sheet_name)
                    
            if debug_mode and hasattr(st, 'success'):
                st.success(f"Excel文件保存成功: {save_path}")
                
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"保存数据时出错: {str(e)}")
            raise Exception(f"保存数据时出错: {str(e)}")
    
    @staticmethod
    def validate_input_files(exp_file, simu_files: List) -> Dict[str, Any]:
        """
        验证输入文件的有效性
        
        Args:
            exp_file: 实验数据文件
            simu_files: 模拟数据文件列表
            
        Returns:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'exp_file_info': None,
            'simu_files_info': []
        }
        
        # 验证实验文件
        try:
            exp_data = pd.read_excel(exp_file, index_col=0)
            validation_result['exp_file_info'] = {
                'shape': exp_data.shape,
                'columns': list(exp_data.columns),
                'index_name': exp_data.index.name
            }
            
            if exp_data.empty:
                validation_result['errors'].append("实验文件为空")
                validation_result['valid'] = False
                
        except Exception as e:
            validation_result['errors'].append(f"无法读取实验文件: {str(e)}")
            validation_result['valid'] = False
        
        # 验证模拟文件
        for i, simu_file in enumerate(simu_files):
            try:
                sheets = pd.ExcelFile(simu_file).sheet_names
                end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
                
                simu_info = {
                    'filename': simu_file.name,
                    'sheets': sheets,
                    'end_point_sheets': end_point_sheets
                }
                
                if not end_point_sheets:
                    validation_result['warnings'].append(
                        f"模拟文件 {simu_file.name} 中没有找到end_point工作表"
                    )
                    
                validation_result['simu_files_info'].append(simu_info)
                
            except Exception as e:
                validation_result['errors'].append(
                    f"无法读取模拟文件 {simu_file.name}: {str(e)}"
                )
                validation_result['valid'] = False
        
        return validation_result
    
    @staticmethod
    def extract_common_species(exp_file, simu_files: List) -> List[str]:
        """
        提取实验和模拟文件中的共同组分
        
        Args:
            exp_file: 实验数据文件
            simu_files: 模拟数据文件列表
            
        Returns:
            共同组分列表
        """
        try:
            # 获取实验数据的组分
            exp_data = pd.read_excel(exp_file, index_col=0)
            exp_species = set(exp_data.columns)
            
            # 获取所有模拟文件的组分
            all_simu_species = set()
            
            for simu_file in simu_files:
                sheets = pd.ExcelFile(simu_file).sheet_names
                xl = pd.ExcelFile(simu_file)
                end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
                
                for sheet in end_point_sheets:
                    sheet_data = xl.parse(sheet, index_col=0)
                    mole_fraction_cols = sheet_data.columns[
                        sheet_data.columns.str.contains(' Mole_fraction_')
                    ]
                    species = (mole_fraction_cols
                              .str.replace(' Mole_fraction_', '', regex=False)
                              .str.replace('_end_point_()', '', regex=False))
                    all_simu_species.update(species)
            
            # 找出共同组分
            common_species = list(exp_species.intersection(all_simu_species))
            return sorted(common_species)
            
        except Exception as e:
            return []

    @staticmethod
    def _extract_endpoints_from_solution_sheets_enhanced(xl, soln_sheets, progress_bar1, 
                                                        extract_first: bool = True, 
                                                        extract_last: bool = True):
        """
        从solution sheets提取端点数据（支持第一点和最后一点）
        
        Args:
            xl: Excel文件对象
            soln_sheets: solution工作表名称列表
            progress_bar1: 进度条对象
            extract_first: 是否提取第一点数据
            extract_last: 是否提取最后一点数据
            
        Returns:
            包含摩尔分数数据和原始数据的字典
        """
        try:
            # 按工况分组
            condition_sheets = {}
            for sheet in soln_sheets:
                if '#' in sheet:
                    # 对于格式如 '3.soln_no_1_Run#1' 的sheet名
                    condition_id = sheet.split('#')[-1]  # 得到 '1' 
                else:
                    condition_id = '1'
                
                if condition_id not in condition_sheets:
                    condition_sheets[condition_id] = []
                condition_sheets[condition_id].append(sheet)
            
            # 添加调试信息（需要debug_mode参数传递）
            # TODO: 这里需要传递debug_mode参数，暂时注释掉
            # if debug_mode and hasattr(st, 'info'):
            #     st.info(f"调试信息 - sheet解析:")
            #     sample_sheets = soln_sheets[:5]  # 显示前5个sheet
            #     for sheet in sample_sheets:
            #         if '#' in sheet:
            #             condition_id = sheet.split('#')[-1]  # 修正分组逻辑
            #         else:
            #             condition_id = '1'
            #         st.info(f"  Sheet '{sheet}' -> 工况ID: '{condition_id}'")
            
            if progress_bar1:
                progress_bar1.progress(0.2, text=f'发现 {len(condition_sheets)} 个工况组')
            
            # 添加调试信息（需要debug_mode参数传递）
            # TODO: 这里需要传递debug_mode参数，暂时注释掉
            # if debug_mode and hasattr(st, 'info'):
            #     st.info(f"调试信息 - 工况分组详情:")
            #     st.info(f"  总工况数: {len(condition_sheets)}")
            #     for condition_id, sheets in list(condition_sheets.items())[:5]:  # 只显示前5个
            #         st.info(f"  工况 {condition_id}: {len(sheets)} 个sheets")
            #     if len(condition_sheets) > 5:
            #         st.info(f"  ... 还有 {len(condition_sheets) - 5} 个工况")
            
            # 分别存储第一点和最后一点数据
            first_point_data = {} if extract_first else None
            last_point_data = {} if extract_last else None
            all_variable_data = {}  # 存储所有非反应速率变量的端点数据
            
            # 处理每个工况
            for i, (condition_id, sheets) in enumerate(condition_sheets.items()):
                if progress_bar1:
                    progress = 0.2 + (i / len(condition_sheets)) * 0.6
                    progress_bar1.progress(progress, text=f'处理工况 {condition_id}')
                
                # 读取该工况的所有sheets
                condition_data = []
                for sheet in sheets:
                    sheet_data = xl.parse(sheet, index_col=0)
                    condition_data.append(sheet_data)
                
                if condition_data:
                    # 合并同一工况的所有数据
                    if len(condition_data) == 1:
                        merged_data = condition_data[0]
                    else:
                        merged_data = pd.concat(condition_data, axis=1)
                    
                    # 提取端点数据
                    if extract_first:
                        first_point = merged_data.iloc[0]  # 第一行
                        first_point_data[condition_id] = first_point
                    
                    if extract_last:
                        last_point = merged_data.iloc[-1]  # 最后一行
                        last_point_data[condition_id] = last_point
                    
                    # 提取所有非反应速率变量的端点数据
                    for col in merged_data.columns:
                        if (not col.lower().__contains__('mole_fraction') and 
                            not col.lower().__contains__('reaction_rate') and
                            not col.lower().__contains__('net_rate') and
                            not col.lower().__contains__('forward_rate') and
                            not col.lower().__contains__('reverse_rate') and
                            not col.lower().__contains__('rop_') and
                            not col.lower().__contains__('rate_of_production')):
                            
                            # 标准化变量名（去掉Run#工况标识）
                            normalized_col_name = ComparisonAnalyzer._normalize_variable_name(col)
                            
                            if normalized_col_name not in all_variable_data:
                                all_variable_data[normalized_col_name] = {}
                            
                            # 添加变量数据（去掉变化检测限制）
                            if extract_first:
                                all_variable_data[normalized_col_name][f'{condition_id}_first'] = merged_data[col].iloc[0]
                            if extract_last:
                                all_variable_data[normalized_col_name][f'{condition_id}_last'] = merged_data[col].iloc[-1]
            
            if progress_bar1:
                progress_bar1.progress(0.8, text='整理摩尔分数数据')
            
            # 处理摩尔分数数据
            result = {}
            
            # 摩尔分数数据始终使用最后一点（反应最终状态）
            point_data_to_use = last_point_data if last_point_data else first_point_data
            data_type = 'last' if last_point_data else 'first'
            
            if point_data_to_use:
                # 转换为DataFrame格式
                df_list = []
                for condition_id, data in point_data_to_use.items():
                    row = pd.DataFrame([data.values], 
                                     columns=data.index, 
                                     index=[condition_id])
                    df_list.append(row)
                
                # 合并所有工况的数据
                points_df = pd.concat(df_list, axis=0)
                
                # 按数字排序工况ID，而不是字符串排序
                try:
                    # 尝试将索引转换为整数进行排序
                    numeric_index = [int(idx) for idx in points_df.index]
                    sorted_indices = sorted(zip(numeric_index, points_df.index))
                    sorted_condition_ids = [idx[1] for idx in sorted_indices]
                    points_df = points_df.reindex(sorted_condition_ids)
                except ValueError:
                    # 如果不能转换为整数，使用字符串排序
                    points_df = points_df.sort_index()
                
                # 提取摩尔分数列
                mole_cols = [col for col in points_df.columns if 'Mole_fraction_' in col]
                
                if mole_cols:
                    # 重新组织摩尔分数数据
                    mole_data = {}
                    
                    # 按工况提取摩尔分数数据
                    for condition_id in points_df.index:
                        condition_mole_cols = [col for col in mole_cols if f'_Run#{condition_id}_' in col]
                        
                        # 添加调试信息（需要debug_mode参数传递）
                        # TODO: 这里需要传递debug_mode参数，暂时注释掉
                        # if debug_mode and hasattr(st, 'info') and condition_id in list(points_df.index)[:3]:  # 只为前3个工况显示调试信息
                        #     st.info(f"  工况 {condition_id}: 发现 {len(condition_mole_cols)} 个摩尔分数列")
                        #     if condition_mole_cols:
                        #         st.info(f"    示例列: {condition_mole_cols[0]}")
                        
                        for col in condition_mole_cols:
                            # 提取组分名称 - 处理带空格的列名
                            if 'Mole_fraction_' in col:
                                # 处理格式如 " Mole_fraction_H2_Run#1_()" 的列名
                                parts = col.split('Mole_fraction_')[1]  # 得到 "H2_Run#1_()"
                                species = parts.split(f'_Run#{condition_id}_')[0]  # 得到 "H2"
                                
                                if species not in mole_data:
                                    mole_data[species] = {}
                                
                                mole_data[species][condition_id] = points_df.loc[condition_id, col]
                    
                    # 转换为DataFrame
                    if mole_data:
                        mole_fractions_df = pd.DataFrame(mole_data).T
                        
                        # 设置变量索引并按数字排序
                        try:
                            # 将列索引转换为整数并排序
                            condition_indices = [int(idx) for idx in mole_fractions_df.columns]
                            sorted_indices = sorted(condition_indices)
                            
                            # 重新排列列的顺序
                            column_mapping = {str(idx): idx for idx in condition_indices}
                            mole_fractions_df.columns = [column_mapping[col] for col in mole_fractions_df.columns]
                            mole_fractions_df = mole_fractions_df.reindex(columns=sorted_indices)
                        except ValueError:
                            # 如果不能转换为整数，保持原样
                            pass
                        
                        result['mole_fractions'] = mole_fractions_df
                        result[f'{data_type}_points'] = points_df
                        result['all_variables'] = all_variable_data
                        
                        # 提供用户可选择的横坐标变量（横坐标变量优先使用第一点）
                        result['available_x_variables'] = ComparisonAnalyzer._extract_available_variables(
                            all_variable_data, extract_first, extract_last, prefer_first=True
                        )
                        
                        if progress_bar1:
                            progress_bar1.progress(1.0, text='数据提取完成')
                        
                        return result
                else:
                    if hasattr(st, 'error'):
                        st.error("未找到摩尔分数数据")
                    return None
            else:
                if hasattr(st, 'error'):
                    st.error("未能提取到端点数据")
                return None
                
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"提取端点数据时出错: {str(e)}")
            return None

    @staticmethod
    def _extract_available_variables(all_variable_data: Dict, extract_first: bool, extract_last: bool, prefer_first: bool = True) -> Dict[str, List]:
        """
        从提取的变量数据中整理出可用的横坐标变量
        
        Args:
            all_variable_data: 所有变量数据字典
            extract_first: 是否提取了第一点
            extract_last: 是否提取了最后一点
            prefer_first: 是否优先使用第一点（用于横坐标变量）
            
        Returns:
            可用变量字典 {variable_name: [values]}
        """
        available_vars = {}
        
        for var_name, var_data in all_variable_data.items():
            # 提取工况编号和对应的值
            conditions = {}
            
            for key, value in var_data.items():
                if '_first' in key:
                    condition_id = key.replace('_first', '')
                    point_type = 'first'
                elif '_last' in key:
                    condition_id = key.replace('_last', '')
                    point_type = 'last'
                else:
                    continue
                
                try:
                    condition_num = int(condition_id)
                    if condition_num not in conditions:
                        conditions[condition_num] = {}
                    conditions[condition_num][point_type] = value
                except ValueError:
                    continue
            
            # 根据prefer_first参数决定优先级
            var_values = []
            for condition_id in sorted(conditions.keys()):
                if prefer_first:
                    # 优先使用第一点，如果没有则使用最后一点
                    if 'first' in conditions[condition_id] and extract_first:
                        var_values.append(conditions[condition_id]['first'])
                    elif 'last' in conditions[condition_id] and extract_last:
                        var_values.append(conditions[condition_id]['last'])
                else:
                    # 优先使用最后一点，如果没有则使用第一点
                    if 'last' in conditions[condition_id] and extract_last:
                        var_values.append(conditions[condition_id]['last'])
                    elif 'first' in conditions[condition_id] and extract_first:
                        var_values.append(conditions[condition_id]['first'])
            
            if var_values:
                available_vars[var_name] = var_values
        
        return available_vars

    @staticmethod
    def check_files_need_extraction(simu_files: List) -> Dict[str, Any]:
        """
        检查模拟文件是否需要从soln_sheets提取端点数据
        
        Args:
            simu_files: 模拟数据文件列表
            
        Returns:
            检查结果字典
        """
        result = {
            'need_extraction': False,
            'files_without_endpoints': [],
            'files_with_endpoints': [],
            'common_soln_data': None
        }
        
        try:
            for file in simu_files:
                sheets = pd.ExcelFile(file).sheet_names
                end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
                soln_sheets = [sheet for sheet in sheets if 'soln_' in sheet and 'rop_' not in sheet]
                
                if not end_point_sheets and soln_sheets:
                    result['need_extraction'] = True
                    result['files_without_endpoints'].append(file.name)
                elif end_point_sheets:
                    result['files_with_endpoints'].append(file.name)
            
            # 如果需要提取，分析第一个需要提取的文件来获取可用变量
            if result['need_extraction'] and result['files_without_endpoints']:
                first_file = None
                for file in simu_files:
                    if file.name in result['files_without_endpoints']:
                        first_file = file
                        break
                
                if first_file:
                    result['common_soln_data'] = ComparisonAnalyzer._analyze_soln_data(first_file)
            
            return result
            
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"检查文件时出错: {str(e)}")
            return result

    @staticmethod
    def _analyze_soln_data(file):
        """
        分析soln_sheets中的数据结构，获取可用的横坐标变量
        
        Args:
            file: 文件对象
            
        Returns:
            可用变量信息
        """
        try:
            sheets = pd.ExcelFile(file).sheet_names
            xl = pd.ExcelFile(file)
            soln_sheets = [sheet for sheet in sheets if 'soln_' in sheet and 'rop_' not in sheet]
            
            if not soln_sheets:
                return None
            
            # 读取第一个工况的数据来分析结构
            first_sheet = xl.parse(soln_sheets[0], index_col=0)
            
            # 提取可能的横坐标变量（非摩尔分数、非反应速率）
            available_vars = {}
            
            for col in first_sheet.columns:
                if ('Temperature' in col or 'Pressure' in col or 
                    'Distance' in col or 'Time' in col):
                    # 检查是否为常数（变化小于1%）
                    if len(first_sheet) > 1:
                        col_data = first_sheet[col].dropna()
                        if len(col_data) > 1:
                            variation = (col_data.max() - col_data.min()) / col_data.mean()
                            if variation > 0.01:  # 变化大于1%才考虑
                                # 提取最后一点的值作为这个工况的横坐标值
                                available_vars[col] = first_sheet[col].iloc[-1]
            
            return available_vars
            
        except Exception as e:
            return None

    @staticmethod
    def get_available_x_axis_variables(simu_files: List) -> Dict[str, List]:
        """
        获取所有模拟文件的可用横坐标变量（默认使用第一点）
        
        Args:
            simu_files: 模拟文件列表
            
        Returns:
            可用变量字典 {variable_name: [values]}
        """
        return ComparisonAnalyzer.get_available_x_axis_variables_with_point_type(simu_files, 'first')

    @staticmethod
    def get_available_x_axis_variables_with_point_type(simu_files: List, point_type: str = 'first') -> Dict[str, List]:
        """
        获取所有模拟文件的可用横坐标变量，支持选择数据点类型
        
        Args:
            simu_files: 模拟文件列表
            point_type: 数据点类型，'first' 或 'last'
            
        Returns:
            可用变量字典 {variable_name: [values]}
        """
        all_variables = {}
        
        try:
            for file in simu_files:
                sheets = pd.ExcelFile(file).sheet_names
                xl = pd.ExcelFile(file)
                
                # 检查是否有end_point sheets
                end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
                if end_point_sheets:
                    continue  # 有end_point的文件跳过
                
                # 处理soln_sheets
                soln_sheets = [sheet for sheet in sheets if 'soln_' in sheet]
                if not soln_sheets:
                    continue
                
                # 按工况分组
                condition_sheets = {}
                for sheet in soln_sheets:
                    if '#' in sheet:
                        condition_id = sheet.split('#')[-1]  # 修正工况ID提取
                    else:
                        condition_id = '1'
                    
                    if condition_id not in condition_sheets:
                        condition_sheets[condition_id] = []
                    condition_sheets[condition_id].append(sheet)
                
                # 处理每个工况，提取指定位置的变量值
                for condition_id, sheets_list in condition_sheets.items():
                    condition_data = []
                    for sheet in sheets_list:
                        sheet_data = xl.parse(sheet, index_col=0)
                        condition_data.append(sheet_data)
                    
                    if condition_data:
                        # 合并数据
                        if len(condition_data) == 1:
                            merged_data = condition_data[0]
                        else:
                            merged_data = pd.concat(condition_data, axis=1)
                        
                        # 筛选所有非摩尔分数和反应速率的变量
                        for col in merged_data.columns:
                            # 排除摩尔分数、反应速率等列
                            if (not col.lower().__contains__('mole_fraction') and 
                                not col.lower().__contains__('reaction_rate') and
                                not col.lower().__contains__('net_rate') and
                                not col.lower().__contains__('forward_rate') and
                                not col.lower().__contains__('reverse_rate') and
                                not col.lower().__contains__('rop_') and
                                not col.lower().__contains__('rate_of_production')):
                                
                                # 标准化变量名（去掉Run#工况标识）
                                normalized_col_name = ComparisonAnalyzer._normalize_variable_name(col)
                                
                                # 根据point_type提取相应位置的值
                                if point_type == 'first':
                                    target_value = merged_data[col].iloc[0]  # 第一点
                                else:
                                    target_value = merged_data[col].iloc[-1]  # 最后一点
                                
                                if normalized_col_name not in all_variables:
                                    all_variables[normalized_col_name] = []
                                
                                # 添加工况和对应的值
                                all_variables[normalized_col_name].append((int(condition_id), target_value))
            
            # 整理结果：按工况排序并只返回值
            result_variables = {}
            for var_name, condition_values in all_variables.items():
                # 按工况ID排序
                condition_values.sort(key=lambda x: x[0])
                # 只保留值
                result_variables[var_name] = [val[1] for val in condition_values]
            
            return result_variables
            
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"获取横坐标变量时出错: {str(e)}")
            return {}

    @staticmethod
    def get_variable_selection_interface(simu_files: List) -> Dict[str, Any]:
        """
        获取变量选择界面所需的数据
        
        Args:
            simu_files: 模拟文件列表
            
        Returns:
            包含变量选择信息的字典
        """
        result = {
            'has_no_endpoints': False,
            'variable_names': [],
            'first_point_variables': {},
            'last_point_variables': {},
            'variable_info': {}
        }
        
        try:
            # 检查是否有需要提取端点的文件
            check_result = ComparisonAnalyzer.check_files_need_extraction(simu_files)
            
            if check_result['need_extraction']:
                result['has_no_endpoints'] = True
                
                # 获取第一点和最后一点的变量数据
                result['first_point_variables'] = ComparisonAnalyzer.get_available_x_axis_variables_with_point_type(
                    simu_files, 'first'
                )
                result['last_point_variables'] = ComparisonAnalyzer.get_available_x_axis_variables_with_point_type(
                    simu_files, 'last'
                )
                
                # 获取变量名称列表
                result['variable_names'] = ComparisonAnalyzer.get_variable_names_from_soln_sheets(simu_files)
                
                # 为每个变量提供详细信息
                for var_name in result['variable_names']:
                    var_info = {
                        'first_values': result['first_point_variables'].get(var_name, []),
                        'last_values': result['last_point_variables'].get(var_name, []),
                        'has_variation': True  # 已经过滤了无变化的变量
                    }
                    
                    # 计算变化范围
                    if var_info['last_values']:
                        var_info['range'] = f"{min(var_info['last_values']):.3f} - {max(var_info['last_values']):.3f}"
                    elif var_info['first_values']:
                        var_info['range'] = f"{min(var_info['first_values']):.3f} - {max(var_info['first_values']):.3f}"
                    else:
                        var_info['range'] = "N/A"
                    
                    result['variable_info'][var_name] = var_info
            
            return result
            
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"获取变量选择界面数据时出错: {str(e)}")
            return result

    @staticmethod
    def _normalize_variable_name(col_name: str) -> str:
        """
        标准化变量名，去掉Run#工况标识
        
        Args:
            col_name: 原始列名，格式如 "变量名_Run#工况序号_(变量单位)"
            
        Returns:
            标准化后的变量名
        """
        # 如果包含_Run#，去掉Run#部分
        if '_Run#' in col_name:
            # 找到_Run#的位置
            run_index = col_name.find('_Run#')
            # 找到Run#后面的下一个_的位置
            next_underscore = col_name.find('_', run_index + 1)
            if next_underscore != -1:
                # 重组变量名：前缀 + 后缀（去掉Run#部分）
                prefix = col_name[:run_index]
                suffix = col_name[next_underscore:]
                return prefix + suffix
            else:
                # 如果没有找到下一个_，返回Run#前的部分
                return col_name[:run_index]
        return col_name

    @staticmethod
    def get_variable_names_from_soln_sheets(simu_files: List) -> List[str]:
        """
        从soln_sheets中获取所有可能的横坐标变量名称
        
        Args:
            simu_files: 模拟文件列表
            
        Returns:
            变量名称列表
        """
        all_variable_names = set()
        
        try:
            for file in simu_files:
                sheets = pd.ExcelFile(file).sheet_names
                xl = pd.ExcelFile(file)
                
                # 检查是否有end_point sheets
                end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
                if end_point_sheets:
                    continue  # 有end_point的文件跳过
                
                # 处理soln_sheets（排除rop_sheets）
                soln_sheets = [sheet for sheet in sheets if 'soln_' in sheet and 'rop_' not in sheet]
                if not soln_sheets:
                    continue
                
                # 读取第一个工况的第一个sheet来获取列名
                first_sheet = xl.parse(soln_sheets[0], index_col=0)
                
                # 筛选所有非摩尔分数和反应速率的变量名
                for col in first_sheet.columns:
                    # 排除摩尔分数、反应速率等列
                    if (not col.lower().__contains__('mole_fraction') and 
                        not col.lower().__contains__('reaction_rate') and
                        not col.lower().__contains__('net_rate') and
                        not col.lower().__contains__('forward_rate') and
                        not col.lower().__contains__('reverse_rate') and
                        not col.lower().__contains__('rop_') and
                        not col.lower().__contains__('rate_of_production')):
                        
                        # 标准化变量名（去掉Run#工况标识）
                        normalized_name = ComparisonAnalyzer._normalize_variable_name(col)
                        all_variable_names.add(normalized_name)
            
            return sorted(list(all_variable_names))
            
        except Exception as e:
            if hasattr(st, 'error'):
                st.error(f"获取变量名称时出错: {str(e)}")
            return []

    @staticmethod
    def create_advanced_comparison_plot(
        exp_file, 
        simu_files: List, 
        store_path: str, 
        progress_bar: bool = False,
        selected_x_var: str = None,
        selected_x_data: List = None,
        plot_style: str = 'scientific',
        show_confidence_bands: bool = False
    ) -> Dict[str, pd.DataFrame]:
        """
        创建高级对比图（附加功能版本）
        
        Args:
            exp_file: 实验数据文件
            simu_files: 模拟数据文件列表
            store_path: 存储路径
            progress_bar: 是否显示进度条
            selected_x_var: 用户选择的横坐标变量名
            selected_x_data: 用户选择的横坐标数据
            plot_style: 绘图样式 ('scientific', 'modern', 'colorful')
            show_confidence_bands: 是否显示置信区间
            
        Returns:
            包含各文件数据的字典
        """
        # 基础数据处理逻辑保持不变...
        # 这里可以复用原有的数据处理逻辑
        
        # 根据plot_style选择不同的配色方案
        color_schemes = {
            'scientific': {
                'simulation': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
                'experiment': '#e74c3c',
                'background': 'white'
            },
            'modern': {
                'simulation': ['#3498db', '#e67e22', '#2ecc71', '#e74c3c', '#9b59b6'],
                'experiment': '#34495e',
                'background': '#f8f9fa'
            },
            'colorful': {
                'simulation': px.colors.qualitative.Set3[:10],
                'experiment': '#2c3e50',
                'background': 'white'
            }
        }
        
        # 这里可以添加更多高级功能的实现
        # 例如：置信区间、统计分析、残差分析等
        
        # 暂时返回标准功能（可以后续扩展）
        return ComparisonAnalyzer.create_comparison_plot(
            exp_file, simu_files, store_path, progress_bar, selected_x_var, selected_x_data
        )

    @staticmethod
    def _get_color_scheme(style: str = 'scientific') -> Dict[str, Any]:
        """
        获取配色方案
        
        Args:
            style: 样式名称
            
        Returns:
            配色方案字典
        """
        schemes = {
            'scientific': {
                'simulation_colors': [
                    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
                ],
                'experiment_color': '#e74c3c',
                'background_color': 'white',
                'grid_color': 'rgba(128,128,128,0.2)',
                'text_color': '#2c3e50'
            },
            'modern': {
                'simulation_colors': [
                    '#3498db', '#e67e22', '#2ecc71', '#e74c3c', '#9b59b6',
                    '#34495e', '#f39c12', '#27ae60', '#8e44ad', '#2980b9'
                ],
                'experiment_color': '#34495e',
                'background_color': '#f8f9fa',
                'grid_color': 'rgba(52,73,94,0.1)',
                'text_color': '#2c3e50'
            },
            'colorful': {
                'simulation_colors': [
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
                ],
                'experiment_color': '#2C3E50',
                'background_color': 'white',
                'grid_color': 'rgba(128,128,128,0.15)',
                'text_color': '#2c3e50'
            }
        }
        
        return schemes.get(style, schemes['scientific'])

    @staticmethod
    def validate_data_plot(data_plot: Dict[str, pd.DataFrame], debug_mode: bool = False) -> Dict[str, Any]:
        """
        验证data_plot数据的完整性
        
        Args:
            data_plot: 要验证的数据字典
            debug_mode: 调试模式
            
        Returns:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'total_files': len(data_plot),
            'empty_files': [],
            'files_with_data': [],
            'summary': {}
        }
        
        for file_name, dataframe in data_plot.items():
            if dataframe.empty:
                validation_result['empty_files'].append(file_name)
                validation_result['valid'] = False
            else:
                # 检查是否有实际数据（非NaN）
                non_null_count = dataframe.count().sum()
                if non_null_count > 0:
                    validation_result['files_with_data'].append(file_name)
                    validation_result['summary'][file_name] = {
                        'shape': dataframe.shape,
                        'non_null_points': non_null_count,
                        'columns': list(dataframe.columns),
                        'index_range': [dataframe.index.min(), dataframe.index.max()] if len(dataframe.index) > 0 else None
                    }
                else:
                    validation_result['empty_files'].append(file_name)
                    validation_result['valid'] = False
        
        if debug_mode and hasattr(st, 'info'):
            st.info(f"数据验证结果:")
            st.info(f"  总文件数: {validation_result['total_files']}")
            st.info(f"  有数据的文件: {len(validation_result['files_with_data'])}")
            st.info(f"  空文件: {len(validation_result['empty_files'])}")
            
            if validation_result['empty_files']:
                st.warning(f"  空文件列表: {validation_result['empty_files']}")
            
            for file_name, summary in validation_result['summary'].items():
                st.info(f"  {file_name}: {summary['shape']}, {summary['non_null_points']} 个数据点")
        
        return validation_result


class DataProcessor:
    """数据处理器"""
    
    @staticmethod
    def preprocess_experimental_data(exp_file) -> pd.DataFrame:
        """
        预处理实验数据
        
        Args:
            exp_file: 实验数据文件
            
        Returns:
            处理后的实验数据DataFrame
        """
        try:
            exp_data = pd.read_excel(exp_file, index_col=0)
            
            # 移除包含NaN的行
            exp_data = exp_data.dropna()
            
            # 确保数据类型为数值
            for col in exp_data.columns:
                exp_data[col] = pd.to_numeric(exp_data[col], errors='coerce')
            
            # 再次移除转换失败的数据
            exp_data = exp_data.dropna()
            
            return exp_data
            
        except Exception as e:
            raise Exception(f"预处理实验数据时出错: {str(e)}")
    
    @staticmethod
    def interpolate_simulation_data(simu_data: pd.DataFrame, 
                                   target_index: pd.Index) -> pd.DataFrame:
        """
        将模拟数据插值到目标索引
        
        Args:
            simu_data: 模拟数据DataFrame
            target_index: 目标索引
            
        Returns:
            插值后的数据DataFrame
        """
        try:
            # 确保索引是数值类型
            simu_data.index = pd.to_numeric(simu_data.index)
            target_index = pd.to_numeric(target_index)
            
            # 对每列进行插值
            interpolated_data = pd.DataFrame(index=target_index)
            
            for col in simu_data.columns:
                interpolated_data[col] = np.interp(
                    target_index, 
                    simu_data.index, 
                    simu_data[col]
                )
            
            return interpolated_data
            
        except Exception as e:
            raise Exception(f"插值数据时出错: {str(e)}")


# 为了向后兼容，保留原函数名
def compare_plot(exp_file, simu_files: List, store_path: str, 
                progress_bar: bool = False, selected_x_var: str = None,
                selected_x_data: List = None) -> Dict[str, pd.DataFrame]:
    """向后兼容函数"""
    return ComparisonAnalyzer.create_comparison_plot(
        exp_file, simu_files, store_path, progress_bar, selected_x_var, selected_x_data
    )


def save_compare_plot(save_path: str, data_plot: Dict[str, pd.DataFrame]) -> None:
    """向后兼容函数"""
    return ComparisonAnalyzer.save_comparison_data(save_path, data_plot, debug_mode=False) 