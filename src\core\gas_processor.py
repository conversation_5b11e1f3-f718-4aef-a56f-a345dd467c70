"""
Gas文件处理器基础类
"""
import copy
import re
from typing import Dict, List, Optional, Union, IO
import pandas as pd
import numpy as np
from tqdm import tqdm

from .exceptions import GasFileError
from ..utils.string_utils import replace_scientific_notation, clean_reaction_equation


class GasProcessor:
    """Gas.out文件处理器"""
    
    def __init__(self, gas_file: Union[str, IO], test: bool = False):
        """初始化Gas处理器"""
        self._init_attributes()
        self._load_gas_file(gas_file, test)
    
    def _init_attributes(self):
        """初始化所有属性"""
        self.elements = None
        self.species = None  
        self.reactions = None
        self.reactants = None
        self.products = None
        self.stoichimetric = None
        self.species_element = None
        self.species_MW = None
        self.reaction_index = None
        self.Arr_all = []
        self.Arr_SI_unit = []
        self.cantera_reactions = []
        self.load_file = False
    
    def _load_gas_file(self, gas_file: Union[str, IO], test: bool):
        """加载gas文件内容"""
        try:
            if not test:
                self.gas_out = str(gas_file.read(), 'utf-8')
            else:
                with open(gas_file, 'r', encoding='utf-8') as f:
                    self.gas_out = f.read()
        except Exception as e:
            raise GasFileError(f"无法读取gas.out文件: {str(e)}")
    
    def process_gas_out(self, show_progress: bool = False) -> None:
        """处理gas.out文件内容"""
        print('正在处理gas.out文件信息')
        gas_out_lines = self.gas_out.splitlines()
        
        self._extract_species_info(gas_out_lines, show_progress)
        self._extract_reaction_info(gas_out_lines, show_progress)
        self._process_stoichiometric_matrix(show_progress)
        
        print('处理完毕！')
        self.load_file = True
    
    def _extract_species_info(self, gas_out_lines: List[str], show_progress: bool):
        """提取组分信息"""
        species_flag = False
        species_info = []
        
        for line in gas_out_lines:
            if species_flag and '(k = A T**b exp(-E/RT))' in line:
                species_flag = False
            if line.startswith(' CONSIDERED '):
                species_flag = True
            if species_flag:
                species_info.append(line)
        
        # 处理元素列表和组分信息
        element_list = species_info[0].split()[6:]
        self.species_element = pd.DataFrame(columns=element_list)
        self.species_MW = pd.DataFrame(columns=['MW'])
        
        for line in species_info[2:-4]:
            species = line.split()[1]
            self.species_element.loc[species, :] = line.split()[7:]
            self.species_MW.loc[species, :] = line.split()[4]
        
        self.species_element = self.species_element.astype(int)
        self.species_MW = self.species_MW.astype(float).apply(lambda x: round(x)).T
        self.elements = element_list
    
    def _extract_reaction_info(self, gas_out_lines: List[str], show_progress: bool):
        """提取反应信息"""
        reaction_flag = False
        reaction_info = []
        
        for i, line in enumerate(gas_out_lines):
            if reaction_flag and ' UNITS for the preceding reactions' in line:
                reaction_flag = False
            if reaction_flag:
                reaction_info.append(line)
            if line.startswith('      REACTIONS CONSIDERED'):
                reaction_flag = True
        
        self.reaction_index = pd.DataFrame(columns=['reaction_equation'])
        self.Arr_all = []
        self.cantera_reactions = []
        
        print('正在处理反应信息')
        for i, line in enumerate(tqdm(reaction_info)):
            if line.split('.')[0].strip().isdigit():
                self._process_reaction_line(line, reaction_info, i)
    
    def _process_reaction_line(self, line: str, reaction_info: List[str], index: int):
        """处理单个反应行"""
        no_index_line = line.replace(line.split('.')[0] + '.', '')
        reaction = no_index_line.split()[0]
        
        # 处理可能分行的反应式
        if '=' not in reaction:
            reaction = reaction + reaction_info[index + 1].strip()
        
        # 生成Cantera格式的反应式
        if '<=>' in reaction:
            self.cantera_reactions.append(
                reaction.replace('+', ' + ').replace('<=>', ' <=> ').replace('( + M)', ' (+M)'))
        else:
            self.cantera_reactions.append(
                reaction.replace('+', ' + ').replace('=', ' <=> ').replace('<=> >', '<=> ').replace('( + M)', ' (+M)'))
        
        # 存储反应式
        reaction_num = int(line.split('.')[0].strip())
        self.reaction_index.loc[reaction_num, 'reaction_equation'] = reaction
        
        # 提取Arrhenius参数
        arr_param = {
            'A': float(replace_scientific_notation(no_index_line.split()[1])),
            'b': float(replace_scientific_notation(no_index_line.split()[2])),
            'Ea': float(replace_scientific_notation(no_index_line.split()[3]))
        }
        self.Arr_all.append(copy.deepcopy(arr_param))
    
    def _process_stoichiometric_matrix(self, show_progress: bool):
        """处理化学计量数矩阵"""
        print('正在处理化学计量数矩阵')
        
        # 初始化化学计量数矩阵
        stoichi_raw = pd.DataFrame(
            columns=self.species_element.index,
            index=[f'{reaction}#{i+1}' for i, reaction in enumerate(self.reaction_index.loc[:, 'reaction_equation'])],
            data=0
        )
        
        self.reactants = {}
        self.products = {}
        self.Arr_SI_unit = []
        
        for i, reaction in enumerate(tqdm(stoichi_raw.index)):
            self._process_single_reaction_stoichiometry(reaction, stoichi_raw, i)
        
        # 设置最终属性
        self.species = [str(species) for species in stoichi_raw.columns]
        self.reactions = [f'{reaction}' for reaction in stoichi_raw.index]
        self.stoichimetric = stoichi_raw
        self.stoichimetric.index = self.reactions
    
    def _process_single_reaction_stoichiometry(self, reaction: str, stoichi_raw: pd.DataFrame, index: int):
        """处理单个反应的化学计量数"""
        # 分离反应物和产物
        if '<' in reaction:
            reactant_orig = '#'.join(reaction.split('#')[:-1]).split('<=')[0]
        else:
            reactant_orig = '#'.join(reaction.split('#')[:-1]).split('=')[0]
        
        reactant = re.sub(r'\(\+.*\)', '', reactant_orig)  # 去除三体反应项
        species_r = reactant.split('+')
        
        # 处理反应物
        sp_r = []
        for sp in species_r:
            if sp not in ['m', 'M']:
                stoich_coeff, species_name = self._extract_stoichiometry(sp)
                if species_name:
                    stoichi_raw.loc[reaction, species_name] -= stoich_coeff
                    sp_r.append(species_name)
        self.reactants[reaction] = sp_r
        
        # 处理产物
        if '>' in reaction:
            product_orig = '#'.join(reaction.split('#')[:-1]).split('=>')[1]
        else:
            product_orig = '#'.join(reaction.split('#')[:-1]).split('=')[1]
        
        product = re.sub(r'\(\+.*\)', '', product_orig)
        species_p = product.split('+')
        
        sp_p = []
        for sp in species_p:
            if sp not in ['m', 'M', 'HV', 'H*']:  # 排除光子等
                stoich_coeff, species_name = self._extract_stoichiometry(sp)
                if species_name:
                    stoichi_raw.loc[reaction, species_name] += stoich_coeff
                    sp_p.append(species_name)
        self.products[reaction] = sp_p
        
        # 转换为SI单位的Arrhenius参数
        arr_si = {
            'A': self.Arr_all[index]['A'] * (1000**(len(self.reactants[reaction]) - 1)),
            'b': self.Arr_all[index]['b'],
            'Ea': self.Arr_all[index]['Ea'] * 4184
        }
        self.Arr_SI_unit.append(copy.deepcopy(arr_si))
    
    def _extract_stoichiometry(self, species_str: str) -> tuple[float, str]:
        """从组分字符串中提取化学计量数和组分名"""
        species_str = species_str.strip()
        if not species_str:
            return 0.0, None
        
        if species_str[0].isdigit() or species_str[0] == '.':
            # 有显式计量数
            stoich_match = re.search(r'^\d*\.?\d*', species_str)
            if stoich_match:
                stoich_coeff = float(stoich_match.group())
                species_name = re.sub(r'^\d*\.?\d*', '', species_str)
                return stoich_coeff, species_name.strip()
        
        # 默认计量数为1
        return 1.0, species_str.strip()