"""
兼容性包装器
提供与原始solution0606模块相同的API，但使用优化后的实现
"""
from .gas_solution import GasSolution
from .chemkin_solution import ChemkinSolution
from .chemkin_solution_optimized import ChemkinSolutionOptimized
import os
import cantera as ct
import pandas as pd
import streamlit as st


class solution(GasSolution):
    """兼容原始solution类的包装器"""
    
    def __init__(self, gas_out='gas.out', test=False):
        super().__init__(gas_out, test)


def ckfile(file_path, use_optimized=True):
    """
    创建CHEMKIN文件处理对象
    
    Args:
        file_path: CHEMKIN文件路径
        use_optimized: 是否使用优化版本（默认True）
    
    Returns:
        ChemkinSolution或ChemkinSolutionOptimized对象
    """
    if use_optimized:
        return ChemkinSolutionOptimized(file_path)
    else:
        from .chemkin_solution import ChemkinSolution
        return ChemkinSolution(file_path)


class ct_file:
    """热力学文件处理器（保持原始实现）"""
    
    def __init__(self, input_file='thermo_file'):
        if os.path.exists(input_file):
            print(input_file)
            result = os.system(f'python -m cantera.ck2yaml --thermo="{input_file}" --permissive --output=thermo.yaml')
            if result == 0:
                self.ct_species = ct.Species.list_from_file('thermo.yaml')
                self.reverse_rate_constants = None
                st.success('导入成功！')
            else:
                st.error('热力学文件导入失败，请检查！')
        else:
            st.error('文件不存在！')

    def reversed_rate(self, a, selected_reaction, temp_min, temp_max):
        """计算逆反应速率常数"""
        reaction_where = a.reactions.index(selected_reaction)
        reaction = ct.Reaction(equation=a.cantera_reactions[reaction_where], rate=a.Arr_SI_unit[reaction_where])
        reaction_reverse_name = f'{reaction.product_string} <=> {reaction.reactant_string}'
        
        if reaction.reversible:
            try:
                self.ct_gas = ct.Solution(thermo='ideal-gas', species=self.ct_species, kinetics='gas',
                                        reactions=[reaction])
                self.reverse_rate_constants = pd.DataFrame()
                
                import numpy as np
                for T in np.arange(temp_min, temp_max, 10):
                    self.ct_gas.TP = T, ct.one_atm
                    self.reverse_rate_constants.loc[T, reaction_reverse_name] = \
                        self.ct_gas.reverse_rate_constants[0] / (1000**(len(reaction.products)-1))
                    self.reverse_rate_constants.loc[T, 'forward'] = \
                        self.ct_gas.forward_rate_constants[0] / (1000**(len(reaction.reactants)-1))
                    self.reverse_rate_constants.loc[T, 'EQ_constant'] = \
                        self.ct_gas.equilibrium_constants[0]*(1000**(len(reaction.reactants)-len(reaction.products)))
            except ValueError:
                pass
        else:
            pass

    def fit_arr(self, T_low, T_high):
        """拟合Arrhenius参数"""
        from lmfit import Model
        import numpy as np
        
        def Arr_fit(T, A, n, Ea):
            return np.log(A) + n * np.log(T) - Ea / (T * 8.314 / 4.184)

        ln_rc_fit = np.array(np.log(self.reverse_rate_constants.iloc[:, 0].loc[float(T_low):float(T_high)]))
        T_fit = np.array(self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high].index)
        
        model = Model(Arr_fit)
        params = model.make_params(A=10000, n=1, Ea=5000)
        result = model.fit(ln_rc_fit, params, T=T_fit)

        self.Arr_fitted = {
            'A': result.params['A'].value,
            'b': result.params['n'].value,
            'Ea': result.params['Ea'].value
        }

        data_fit_origin = pd.DataFrame(index=T_fit, columns=['original', 'fitted'])
        data_fit_origin.loc[:, 'original'] = self.reverse_rate_constants.iloc[:, 0].loc[T_low:T_high]
        data_fit_origin.loc[:, 'fitted'] = np.exp(
            np.log(result.params['A'].value) +
            result.params['n'].value * np.log(T_fit) -
            result.params['Ea'].value / (T_fit * 8.314 / 4.184)
        )
        
        return data_fit_origin


# 保持原始的工具函数
def compare_plot(exp_file, simu_files, store_path, progress_bar=False):
    """对比图绘制功能（保持原始实现）"""
    import copy
    import datetime
    import plotly
    import plotly.graph_objs as go
    from plotly.subplots import make_subplots
    from tqdm import tqdm
    import re
    
    exp_data = pd.read_excel(exp_file, index_col=0)
    files = simu_files
    file_names = []
    mole_fractions = {}
    data_plot = {}
    
    if progress_bar:
        progress_bar1 = st.progress(0, text='正在处理文件')
    
    for file in files:
        file_name = file.name
        file_names.append(file.name)
        sheets = pd.ExcelFile(file).sheet_names
        xl = pd.ExcelFile(file)
        end_point_sheets = [sheet for sheet in sheets if 'end_point' in sheet]
        
        for i, sheet in enumerate(end_point_sheets):
            sheet_end_raw = xl.parse(sheet, index_col=0)
            if i == 0:
                new_sheet = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
            else:
                sheet_end = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains(' Mole_fraction_')]
                new_sheet = pd.concat([new_sheet, sheet_end], axis=1)
            
            mole_fractions[file_name] = copy.deepcopy(new_sheet)
            mole_fractions[file_name].columns = mole_fractions[file_name].columns.str.replace(
                ' Mole_fraction_', '', regex=False).str.replace('_end_point_()', '', regex=False)
            
            variable = sheet_end_raw.iloc[:, sheet_end_raw.columns.str.contains('.*_C1_.*')]
            if len(sheet_end_raw) == 0:
                return None
            
            if len(variable.T) > 1:
                print('注意，导出了两种反应变量，会导致绘图出现错误，请选一种')
                return None
            else:
                mole_fractions[file_name].index = variable.iloc[:, 0]
                data_plot[file_name] = pd.DataFrame(index=variable.iloc[:, 0], columns=exp_data.columns)

    variable_name = variable.columns.str.split('_')[0][0]
    variable_unit = variable.columns.str.split('_')[0][-1]
    
    colors = plotly.colors.qualitative.Plotly
    rows_plot = (len(exp_data.columns) + 1) // 2
    position = 1 - 1 / rows_plot + 0.01
    
    fig = make_subplots(rows=rows_plot, cols=2, subplot_titles=exp_data.columns, vertical_spacing=0.02)
    
    for num_p, species in enumerate(exp_data.columns):
        if progress_bar:
            progress_bar1.progress((num_p + 1) / len(exp_data.columns))
        
        row = int(num_p / 2) + 1
        col = int(num_p % 2) + 1

        for i, file_name in enumerate(file_names):
            try:
                fig.add_trace(
                    go.Scatter(
                        x=mole_fractions[file_name].index,
                        y=mole_fractions[file_name][species],
                        mode='lines',
                        name=f'Sim_{file_name}',
                        line=dict(color=colors[i % len(colors)]),
                        showlegend=(num_p == 0)
                    ),
                    row=row,
                    col=col
                )
            except KeyError:
                st.warning(f'警告，{file_name}机理文件中没有找到{species}')
        
        fig.add_trace(
            go.Scatter(
                x=exp_data.index,
                y=exp_data[species],
                mode='markers',
                name='Exp.',
                showlegend=(num_p == 0)
            ),
            row=row,
            col=col
        )
        
        fig.update_xaxes(title_text=f"{variable_name} {variable_unit}", row=row, col=col)
        fig.update_yaxes(title_text="Mole Fraction", tickformat='.2e', row=row, col=col)

    fig.update_layout(
        title='Comparison Plot',
        legend=dict(
            x=0.5,
            y=position,
            xanchor='center',
            yanchor='top',
            orientation='h',
            font=dict(family="Courier", size=14)
        ),
        xaxis_title=f"{variable_name} ({variable_unit})",
        yaxis={'title': "Mole Fraction", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'},
        height=500 * rows_plot,
        width=1000
    )

    current_datetime = datetime.datetime.now()
    formatted_datetime = current_datetime.strftime("%Y%m%d-%H-%M")
    fig.write_html(f'{store_path}/{formatted_datetime}对比结果.html')
    
    os.startfile(f'{store_path}/{formatted_datetime}对比结果.html')
    return data_plot


def save_compare_plot(save_path, data_plot):
    """保存对比图数据"""
    writer = pd.ExcelWriter(save_path, engine='openpyxl')
    for key in data_plot.keys():
        data_plot[key].to_excel(writer, f'{key}')
    writer.close()


def forward_rate(reaction_equation, Arr_param, t_min=300, t_max=2000):
    """计算正向反应速率常数"""
    import math
    import numpy as np
    
    temp_range = np.arange(t_min, t_max, 10)

    def rate_constant(A, n, Ea, temp_range):
        temps = temp_range
        rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
        return rc

    rc = rate_constant(Arr_param['A'], Arr_param['b'], Arr_param['Ea'], temp_range)
    fc = pd.DataFrame(index=temp_range, columns=[reaction_equation], data=rc)
    return fc


def process_rc(string=None, t_min=300, t_max=2000):
    """处理反应速率常数字符串"""
    import math
    import numpy as np
    import re
    
    temp_range = np.arange(t_min, t_max, 10)
    reactions = string
    lines = reactions.split('\n')

    def rate_constant(A, n, Ea, temp_range):
        temps = temp_range
        rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
        return rc

    data = pd.DataFrame(index=temp_range)
    
    for line in lines:
        if not line.strip() == '':
            if '!' in line:
                line = line[0:line.rfind('!')].strip()
            else:
                line = line.strip()
            
            try:
                if 'PLOG' in line:
                    reaction = 'P=' + line.split('/')[1].split()[0]
                    A = float(line.split('/')[1].split()[1])
                    n = float(line.split('/')[1].split()[2])
                    Ea = float(line.split('/')[1].split()[3])
                elif '=' in line:
                    id = 1
                    reaction = re.split(r"\s+", line)[0]
                    A = float(re.split(r"\s+", line)[id])
                    n = float(re.split(r"\s+", line)[id + 1])
                    Ea = float(re.split(r"\s+", line)[id + 2])
                else:
                    id = 0
                    A = float(re.split(r"\s+", line)[id])
                    n = float(re.split(r"\s+", line)[id + 1])
                    Ea = float(re.split(r"\s+", line)[id + 2])
                    reaction = f'{A=:.2e}  {n=:.2f}  {Ea=:.2e}'
                
                rc = rate_constant(A, n, Ea, temp_range)
                if reaction in data.columns:
                    data.loc[:, reaction] = data.loc[:, reaction] + rc
                else:
                    data.loc[:, reaction] = rc
                    
            except ValueError:
                pass
    
    return data


def plot_rc(dataframe, plot=False):
    """绘制反应速率常数图"""
    import plotly.graph_objs as go
    
    fig = go.Figure()
    for col in dataframe.columns:
        fig.add_trace(go.Scatter(
            x=dataframe.index.astype(float), 
            y=dataframe[col],
            mode='lines',
            name=col,
            line=dict(width=2)
        ))

    fig.update_layout(
        title='Rate constant',
        xaxis_title='Temperature / K',
        yaxis={'title': 'Rate constant / cm^3 mol^-1 s^-1', 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.1e'},
        yaxis_type='log',
        legend=dict(
            x=0.5,
            y=-0.2,
            xanchor='center',
            yanchor='top',
            orientation='h',
            font=dict(size=14)
        ),
        margin=dict(r=20),
    )

    fig.update_xaxes(showgrid=True)
    fig.update_yaxes(showgrid=True)
    
    if plot:
        fig.show()
    
    return fig 