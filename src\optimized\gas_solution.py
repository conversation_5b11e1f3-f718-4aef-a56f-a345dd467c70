"""
优化的Gas文件处理器
基于原始solution类，但进行了结构优化和功能拆分
"""
import copy
import re
import numpy as np
import pandas as pd
import streamlit as st
from tqdm import tqdm
import plotly.graph_objs as go
from plotly.subplots import make_subplots


class GasSolution:
    """优化的Gas.out文件处理器"""
    
    def __init__(self, gas_out='gas.out', test=False):
        """
        初始化Gas文件处理器
        
        Args:
            gas_out: gas.out文件或文件对象
            test: 是否为测试模式
        """
        self.elements = None
        self.species = None
        self.reactions = None
        self.reactants = None
        self.products = None
        self.stoichimetric = None
        self.species_element = None
        self.species_MW = None
        self.reaction_index = None
        self.Arr_all = []
        self.Arr_SI_unit = []
        self.cantera_reactions = []
        self.possible_reactants = None
        self.load_file = False
        
        # 读取文件内容
        if not test:
            # 非测试模式，从文件对象读取
            self.gas_out = str(gas_out.read(), 'utf-8')
        else:
            # 测试模式，从文件路径读取
            with open(gas_out, 'r') as f:
                self.gas_out = f.read()
    
    def process_gas_out(self, progress_bar=False):
        """处理gas.out文件"""
        print('正在处理gas.out文件信息')
        
        # 设置进度条
        progress_bar1 = None
        progress_bar2 = None
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在处理反应信息')
            progress_bar2 = st.progress(0, text='正在处理反应系数矩阵')
        
        # 提取组分信息
        self._extract_species_info(progress_bar)
        
        # 提取反应信息
        self._extract_reaction_info(progress_bar1)
        
        # 构建化学计量数矩阵
        self._build_stoichiometric_matrix(progress_bar2)
        
        # 清空进度条
        if progress_bar1:
            progress_bar1.empty()
        if progress_bar2:
            progress_bar2.empty()
        
        self.load_file = True
        print('gas.out文件处理完毕！')
    
    def _extract_species_info(self, progress_bar=False):
        """提取组分信息"""
        gas_out_lines = self.gas_out.splitlines()
        species_flag = False
        species_info = []
        
        for line in gas_out_lines:
            if species_flag == True and '(k = A T**b exp(-E/RT))' in line:
                species_flag = False
            if line.startswith(' CONSIDERED '):
                species_flag = True
            if species_flag == True:
                species_info.append(line)
        
        # 解析元素列表
        if not species_info:
            raise ValueError("未找到组分信息，请检查gas.out文件格式")
        element_list = species_info[0].split()[6:]
        
        # 建立组分-元素表
        self.species_element = pd.DataFrame(columns=element_list)
        self.species_MW = pd.DataFrame(columns=['MW'])
        
        for line in species_info[2:-4]:
            species = line.split()[1]
            self.species_element.loc[species, :] = line.split()[7:]
            self.species_MW.loc[species, :] = line.split()[4]
        
        self.species_element = self.species_element.astype(int)
        self.species_MW = self.species_MW.astype(float).apply(lambda x: round(x)).T
        self.elements = element_list
        
        print('组分信息处理完毕！')
    
    def _extract_reaction_info(self, progress_bar=None):
        """提取反应信息"""
        gas_out_lines = self.gas_out.splitlines()
        reaction_flag = False
        reaction_info = []
        
        for i, line in enumerate(gas_out_lines):
            if reaction_flag and ' UNITS for the preceding reactions' in line:
                reaction_flag = False
            if reaction_flag:
                reaction_info.append(line)
            if line.startswith('      REACTIONS CONSIDERED'):
                reaction_flag = True
        
        # 初始化反应相关数据结构
        self.reaction_index = pd.DataFrame(columns=['reaction_equation'])
        self.Arr_all = []
        self.cantera_reactions = []
        
        print('正在处理反应信息')
        
        # 处理每个反应
        for i, line in enumerate(tqdm(reaction_info, desc="正在处理反应信息", unit='反应')):
            if progress_bar:
                progress_bar.progress((i + 1) / len(reaction_info), text='正在处理反应信息')
            if line.split('.')[0].strip().isdigit():
                self._process_single_reaction(line, reaction_info, i)
        
        print('反应信息处理完毕！')
    
    def _process_single_reaction(self, line, reaction_info, index):
        """处理单个反应"""
        no_index_line = line.replace(line.split('.')[0] + '.', '')
        reaction = no_index_line.split()[0]
        
        # 处理跨行反应
        if '=' not in reaction:
            reaction = reaction + reaction_info[index + 1].strip()
        
        # 格式化反应方程式
        if '<=>' in reaction:
            cantera_reaction = reaction.replace('+', ' + ').replace('<=>', ' <=> ').replace('( + M)', ' (+M)')
        else:
            cantera_reaction = reaction.replace('+', ' + ').replace('=', ' <=> ').replace('<=> >', '<=> ').replace('( + M)', ' (+M)')
        
        self.cantera_reactions.append(cantera_reaction)
        
        # 存储反应方程式
        reaction_no = int(line.split('.')[0].strip())
        self.reaction_index.loc[reaction_no, 'reaction_equation'] = reaction
        
        # 提取Arrhenius参数
        self._extract_arrhenius_params(no_index_line)
    
    def _extract_arrhenius_params(self, no_index_line):
        """提取Arrhenius参数"""
        def replace_sci_num(string):
            if string.startswith('-'):
                string = '-' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
            elif string.startswith('+'):
                string = '+' + string[1:].replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
            else:
                string = string.replace('+', 'E+').replace('EE+', 'E+').replace('-', 'E-').replace('EE-', 'E-')
            return string
        
        Arr_param = {}
        Arr_param['A'] = float(replace_sci_num(no_index_line.split()[1]))
        Arr_param['b'] = float(replace_sci_num(no_index_line.split()[2]))
        Arr_param['Ea'] = float(replace_sci_num(no_index_line.split()[3]))
        self.Arr_all.append(copy.deepcopy(Arr_param))
    
    def _build_stoichiometric_matrix(self, progress_bar=None):
        """构建化学计量数矩阵"""
        print('正在处理化学计量数矩阵')
        
        # 初始化计量矩阵
        stoichi_raw = pd.DataFrame(
            columns=self.species_element.index,
            index=[f'{reaction}#{i + 1}' for i, reaction in enumerate(self.reaction_index.loc[:, 'reaction_equation'])],
            data=0
        )
        
        # 初始化反应物和产物字典
        self.reactants = {}
        self.products = {}
        self.Arr_SI_unit = []
        
        # 处理每个反应的计量系数
        for i, reaction in enumerate(tqdm(stoichi_raw.index, desc="正在处理反应系数矩阵", unit='反应')):
            if progress_bar:
                progress_bar.progress((i + 1) / len(stoichi_raw.index), text='正在处理反应系数矩阵')
            
            self._process_reaction_stoichiometry(reaction, stoichi_raw)
        
        # 设置最终属性
        self.species = [str(species) for species in stoichi_raw.columns]
        self.reactions = [f'{reaction}' for reaction in stoichi_raw.index]
        self.stoichimetric = stoichi_raw
        self.stoichimetric.index = self.reactions
        
        print('化学计量数矩阵处理完毕！')
    
    def _process_reaction_stoichiometry(self, reaction, stoichi_raw):
        """处理单个反应的化学计量系数"""
        # 提取反应物
        if '<' in reaction:
            reactant_orig = '#'.join(reaction.split('#')[:-1]).split('<=')[0]
        else:
            reactant_orig = '#'.join(reaction.split('#')[:-1]).split('=')[0]
        
        reactant = re.sub(r'\(\+.*\)', '', reactant_orig)
        species_r = reactant.split('+')
        
        sp_r = []
        for sp in species_r:
            if sp != 'm' and sp != 'M':
                if sp[0].isdigit() or sp[0] == '.':
                    sto = re.search(r'\d*\.*\d*', sp)[0]
                    sp = re.sub(r'\A\d*\.*\d*', '', sp)
                    stoichi_raw.loc[reaction, sp] -= float(sto)
                else:
                    sto = 1
                    stoichi_raw.loc[reaction, sp] -= float(sto)
                sp_r.append(sp)
        
        self.reactants[reaction] = sp_r
        
        # 提取产物
        if '>' in reaction:
            product_orig = '#'.join(reaction.split('#')[:-1]).split('=>')[1]
        else:
            product_orig = '#'.join(reaction.split('#')[:-1]).split('=')[1]
        
        product = re.sub(r'\(\+.*\)', '', product_orig)
        species_p = product.split('+')
        
        sp_p = []
        for sp in species_p:
            if sp != 'm' and sp != 'M' and sp != 'HV' and sp != 'H*':
                if sp[0].isdigit() or sp[0] == '.':
                    sto = re.search(r'\d*\.*\d*', sp)[0]
                    sp = re.sub(r'\A\d*\.*\d*', '', sp)
                    stoichi_raw.loc[reaction, sp] += float(sto)
                else:
                    sto = 1
                    stoichi_raw.loc[reaction, sp] += float(sto)
                sp_p.append(sp)
        
        self.products[reaction] = sp_p
        
        # 计算SI单位的Arrhenius参数
        reaction_index = len(self.Arr_SI_unit)
        Arr_SI = {}
        Arr_SI['A'] = self.Arr_all[reaction_index]['A'] * (1000 ** (len(sp_r) - 1))
        Arr_SI['b'] = self.Arr_all[reaction_index]['b']
        Arr_SI['Ea'] = self.Arr_all[reaction_index]['Ea'] * 4184
        self.Arr_SI_unit.append(copy.deepcopy(Arr_SI))
    
    def plot_ROP_single(self, chemkin_file, select_species, selected_temp, threshold=0.001, datatype='integral', plot=False):
        """
        单点反应ROP分析
        
        Args:
            chemkin_file: CHEMKIN文件处理器实例
            select_species: 选择的组分
            selected_temp: 选择的温度点
            threshold: 显示阈值
            datatype: 数据类型('integral'或'end')
            plot: 是否显示图表
        """
        if datatype == 'integral':
            ROP = self.integral_ROP[selected_temp]
        elif datatype == 'end':
            ROP = self.end_ROP[selected_temp]
        
        # 计算ROP数据
        self.select_rop = pd.DataFrame(columns=self.stoichimetric.index, index=['raw', 'abs'])
        self.select_rop.loc['raw', :] = ROP.loc[:, select_species]
        self.select_rop.loc['abs', :] = ROP.loc[:, select_species].abs()
        self.select_rop.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.select_rop.drop('abs', inplace=True)
        
        # 计算总产生和消耗
        self.select_rop['Production'] = np.sum(self.select_rop[self.select_rop > 0], 1)
        self.select_rop['Consumption'] = np.sum(self.select_rop[self.select_rop <= 0], 1)
        
        # 计算百分比
        self.ROP_percent = pd.DataFrame(index=self.select_rop.index, columns=self.select_rop.columns)
        ROP_nor_P = self.select_rop[self.select_rop > 0].multiply(1 / self.select_rop['Production'], axis="index")
        ROP_nor_N = self.select_rop[self.select_rop <= 0].multiply(-1 / self.select_rop['Consumption'], axis="index")
        
        self.ROP_percent[self.select_rop > 0] = ROP_nor_P[self.select_rop > 0]
        self.ROP_percent[self.select_rop < 0] = ROP_nor_N[self.select_rop < 0]
        
        # 数据后处理
        self.ROP_percent.loc['abs', :] = self.ROP_percent.loc['raw', :].abs()
        self.ROP_percent.index = ['Relative values', 'abs']
        self.ROP_percent.loc['Absolute values', :] = self.select_rop.loc['raw', :]
        self.ROP_percent.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.ROP_percent.drop('abs', inplace=True)
        self.ROP_percent.drop('Production', axis=1, inplace=True)
        self.ROP_percent.drop('Consumption', axis=1, inplace=True)
        
        # 过滤阈值
        self.ROP_percent = self.ROP_percent.loc[:, self.ROP_percent.loc['Absolute values', :] != 0]
        self.ROP_percent.loc['Direction', :] = 'Produce'
        self.ROP_percent.loc['Direction', self.ROP_percent.loc['Relative values', :].astype(float) <= 0] = 'Consume'
        self.ROP_percent = self.ROP_percent.T[np.abs(self.ROP_percent.T['Relative values']) > threshold].T
        
        # 创建图表
        return self._create_rop_figure(chemkin_file, select_species, selected_temp, plot)
    
    def _create_rop_figure(self, chemkin_file, select_species, selected_temp, plot=False):
        """创建ROP图表"""
        max_limit = len(self.ROP_percent.T)
        fig = make_subplots(rows=1, cols=2, horizontal_spacing=0.05)
        
        # 相对值图
        fig.add_trace(go.Bar(
            y=self.ROP_percent.columns,
            x=self.ROP_percent.loc['Relative values'] * 100,
            orientation='h',
        ), row=1, col=1)
        
        # 绝对值图
        fig.add_trace(go.Bar(
            y=self.select_rop.columns[0:max_limit],
            x=self.select_rop.loc['raw'][0:max_limit],
            orientation='h',
        ), row=1, col=2)
        
        # 图表布局
        fig.update_layout(
            title={
                'text': f"Cumulative {select_species} formation at {chemkin_file.variable_name} {selected_temp} {chemkin_file.variable_unit}",
                'y': 0.9,
                'x': 0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': {'size': 20}
            },
            width=1000, height=400,
            font=dict(size=12, color="RebeccaPurple"),
            showlegend=False,
            xaxis={'title': 'ROP relative percent (%)', 'rangemode': 'tozero', 'ticks': 'outside'},
            xaxis2={'title': 'ROP absolute values (mole cm3 s)', 'showexponent': 'all', 'exponentformat': 'e', 'rangemode': 'normal', 'ticks': 'outside'},
            yaxis1={'showgrid': True},
            yaxis2={'side': 'right', 'showgrid': True}
        )
        
        if plot:
            fig.show()
        
        return fig
    
    def plot_ROP_single_species(self, b, select_species, selected_temp, selected_element, threshold=0.01, datatype='integral', plot=False):
        """
        单个组分的ROP分析（元素流向）
        
        Args:
            b: CHEMKIN文件处理器实例
            select_species: 选择的组分
            selected_temp: 选择的温度点
            selected_element: 选择的元素
            threshold: 显示阈值
            datatype: 数据类型('integral'或'end')
            plot: 是否显示图表
        """
        if datatype == 'integral':
            rop_species = self.integral_ROP[selected_temp][self.integral_ROP[selected_temp].loc[:, select_species] != 0]
        elif datatype == 'end':
            rop_species = self.end_ROP[selected_temp][self.end_ROP[selected_temp].loc[:, select_species] != 0]
        
        flux = pd.DataFrame(index=self.species, columns=self.species, dtype=float).fillna(0.0)
        source = {}
        source_sorted = {}  # 建立一个用来存放各反应比例数的字典，之后用来给source进行排序
        
        for i in range(len(rop_species.index)):
            species_in_reaction = rop_species.iloc[i, :][rop_species.iloc[i, :] != 0]
            for species in species_in_reaction.index:
                if rop_species.loc[:, species].iloc[i] * rop_species.loc[:, select_species].iloc[i] < 0 and \
                        self.species_element.loc[species, selected_element] != 0:
                    flux.loc[select_species, species] = float(flux.loc[select_species, species]) + float(rop_species.loc[:, species].iloc[i])
                    if species not in source.keys():
                        source[species] = [f'{rop_species.index[i]:30} {rop_species.loc[:, species].iloc[i]:9.2e}']
                        source_sorted[species] = [float(rop_species.loc[:, species].iloc[i])]
                    else:
                        source[species].append(f'{rop_species.index[i]:30} {rop_species.loc[:, species].iloc[i]:9.2e}')
                        source_sorted[species].append(float(rop_species.loc[:, species].iloc[i]))
        
        for species in source.keys():
            rank_order = np.argsort(np.abs(source_sorted[species]))[::-1]
            source[species] = np.array(source[species])[rank_order]
        
        ROP = flux.loc[select_species, flux.loc[select_species, :] != 0]
        select_rop = pd.DataFrame(columns=ROP.index, index=['raw', 'abs'])
        select_rop.loc['raw', :] = ROP
        select_rop.loc['abs', :] = ROP.abs()
        select_rop.sort_values('abs', axis=1, inplace=True, ascending=False)
        select_rop.drop('abs', inplace=True)
        
        production_total = np.sum(select_rop.loc['raw', select_rop.loc['raw', :] > 0]) + 1E-100  # 加一个极小的数防止除0
        consumption_total = np.sum(select_rop.loc['raw', select_rop.loc['raw', :] < 0]) + 1E-100
        
        self.ROP_percent_flux = pd.DataFrame(index=select_rop.index, columns=select_rop.columns, dtype=float)
        ROP_nor_P = select_rop.loc['raw', select_rop.loc['raw', :] < 0].multiply(1 / consumption_total, axis="index")
        ROP_nor_N = select_rop.loc['raw', select_rop.loc['raw', :] >= 0].multiply(-1 / production_total, axis="index")
        
        self.ROP_percent_flux.loc['raw', select_rop.loc['raw', :] < 0] = ROP_nor_P[select_rop.loc['raw', :] < 0]
        self.ROP_percent_flux.loc['raw', select_rop.loc['raw', :] >= 0] = ROP_nor_N[select_rop.loc['raw', :] >= 0]
        self.ROP_percent_flux.loc['abs', :] = self.ROP_percent_flux.loc['raw', :].abs()
        
        self.ROP_percent_flux.index = ['Relative values', 'abs']
        self.ROP_percent_flux.loc['Absolute values', :] = select_rop.loc['raw', :]
        self.ROP_percent_flux.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.ROP_percent_flux.drop('abs', inplace=True)

        self.ROP_percent_flux.loc['Direction', :] = 'To'
        self.ROP_percent_flux.loc['Direction', self.ROP_percent_flux.loc['Relative values', :].astype(float) >= 0] = 'From'
        self.ROP_percent_flux.loc['Source', :] = source
        self.ROP_percent_flux = self.ROP_percent_flux.T[np.abs(self.ROP_percent_flux.T['Relative values']) > threshold].T
        
        max_limit = len(self.ROP_percent_flux.T)
        fig = make_subplots(rows=1, cols=2, horizontal_spacing=0.05)
        
        fig.add_trace(go.Bar(
            y=self.ROP_percent_flux.columns,
            x=self.ROP_percent_flux.loc['Relative values'] * 100,
            orientation='h',
        ), row=1, col=1)

        fig.add_trace(go.Bar(
            y=select_rop.columns[0:max_limit],
            x=select_rop.loc['raw'][0:max_limit],
            orientation='h',
        ), row=1, col=2)

        fig.update_layout(title={
            'text': f"Cumulative {select_species} formation at {selected_temp}",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=400,
            font=dict(size=12, color="RebeccaPurple"),
            showlegend=False,
            xaxis={'title': 'ROP relative percent (%)', 'rangemode': 'tozero', 'ticks': 'outside'},
            xaxis2={'title': 'ROP absolute values (mole cm3 s)', 'showexponent': 'all', 'exponentformat': 'e', 'rangemode': 'normal', 'ticks': 'outside'},
            yaxis1={'showgrid': True},
            yaxis2={'side': 'right', 'showgrid': True})
        
        if plot:
            fig.show()
        
        return fig
    
    def get_coefficients(self, reaction, selected_element, selected_species):
        """获取指定反应中指定组分的指定元素系数"""
        return self.species_element.loc[selected_species, selected_element] * self.stoichimetric.abs().loc[reaction, selected_species]
    
    def get_flux_factor(self, reaction, selected_element, reverse=False):
        """获得元素流向分析时组分的转化比例"""
        # 检查质量守恒
        if reverse == False:
            reactants = [species for species in self.reactants[reaction] if self.species_element.loc[species, selected_element] != 0]
            products = [species for species in self.products[reaction] if self.species_element.loc[species, selected_element] != 0]
        else:
            reactants = [species for species in self.products[reaction] if self.species_element.loc[species, selected_element] != 0]
            products = [species for species in self.reactants[reaction] if self.species_element.loc[species, selected_element] != 0]
        
        flux_factor = pd.DataFrame(index=reactants, columns=products).fillna(0)

        # 简化版本的flux_factor计算 - 这里可以根据需要完善
        for reactant in reactants:
            for product in products:
                flux_factor.loc[reactant, product] = self.species_element.loc[product, selected_element] * \
                                                   self.stoichimetric.abs().loc[reaction, product] / \
                                                   (self.species_element.loc[reactant, selected_element] *
                                                    self.stoichimetric.abs().loc[reaction, reactant])
        
        return flux_factor
    
    def process_ROP_multi(self, b, select_species, threshold=0.01, datatype='integral', plot=False):
        """
        多点ROP分析处理
        
        Args:
            b: CHEMKIN文件处理器实例
            select_species: 选择的组分
            threshold: 显示阈值
            datatype: 数据类型('integral'或'end')
            plot: 是否显示图表
        """
        if b.net_reaction_rate_exist:
            self.species_rop = pd.DataFrame(columns=np.sort(b.variables), index=self.reactions)
            for temp in np.sort(b.variables):
                if datatype == 'integral':
                    self.species_rop.loc[:, temp] = self.integral_ROP[temp].loc[:, select_species]
                elif datatype == 'end':
                    self.species_rop.loc[:, temp] = self.end_ROP[temp].loc[:, select_species]
            self.species_rop = copy.deepcopy(self.species_rop[self.species_rop.apply(np.sum, axis=1) != 0].T)
        elif b.net_reaction_rate_exist == False and b.ROP_exist == True:
            if datatype == 'integral':
                self.species_rop = copy.deepcopy(self.integral_ROP[select_species].astype(float))
            elif datatype == 'end':
                self.species_rop = copy.deepcopy(self.end_ROP[select_species].astype(float))
        
        # 处理数据
        self.species_rop.loc['overall'] = np.sum(self.species_rop.apply(lambda x: x ** 2), 0)
        self.species_rop.sort_values('overall', axis=1, inplace=True, ascending=False)
        self.species_rop.drop('overall', inplace=True)
        self.species_rop['Production'] = np.sum(self.species_rop[self.species_rop > 0], 1)
        self.species_rop['Consumption'] = np.sum(self.species_rop[self.species_rop <= 0], 1)
        self.species_rop['Net_production'] = self.species_rop['Production'] + self.species_rop['Consumption']

        # 重新排列列
        self.species_rop.insert(0, 'Net_production', self.species_rop.pop('Net_production'))
        self.species_rop.insert(0, 'Consumption', self.species_rop.pop('Consumption'))
        self.species_rop.insert(0, 'Production', self.species_rop.pop('Production'))

        # 计算百分比
        self.ROP_percent2 = pd.DataFrame(index=self.species_rop.index, columns=self.species_rop.columns, dtype=float)
        ROP_nor_P = self.species_rop[self.species_rop > 0].multiply(1 / self.species_rop['Production'], axis="index")
        ROP_nor_N = self.species_rop[self.species_rop <= 0].multiply(-1 / self.species_rop['Consumption'], axis="index")

        self.ROP_percent2[self.species_rop > 0] = ROP_nor_P[self.species_rop > 0]
        self.ROP_percent2[self.species_rop < 0] = ROP_nor_N[self.species_rop < 0]
        
        # 应用阈值过滤
        first_three = self.species_rop.iloc[:, :3]
        rest = self.ROP_percent2.iloc[:, 3:].loc[:, self.ROP_percent2.iloc[:, 3:].abs().max() > threshold]
        self.ROP_percent2 = pd.concat([first_three, rest], axis=1)
        species_rop_rest = self.species_rop.loc[:, rest.columns]
        self.species_rop = self.species_rop.loc[:, self.ROP_percent2.columns]
        
        # 计算其他项
        self.species_rop['Others_production'] = self.species_rop['Production'] - np.sum(
            species_rop_rest[species_rop_rest > 0], axis=1)
        self.species_rop['Others_consumption'] = self.species_rop['Consumption'] - np.sum(
            species_rop_rest[species_rop_rest < 0], axis=1)
        self.ROP_percent2['Others_production'] = 1 - np.sum(rest[rest > 0], axis=1)
        self.ROP_percent2['Others_consumption'] = -1 - np.sum(rest[rest < 0], axis=1)
        
        # 创建显示格式版本
        self.ROP_percent2_display = copy.deepcopy(self.ROP_percent2)
        self.species_rop_display = copy.deepcopy(self.species_rop)
        self.ROP_percent2_display.iloc[:, :3] = self.ROP_percent2_display.iloc[:, :3].map(lambda x: f'{x:.2e}')
        self.ROP_percent2_display.iloc[:, 3:] = self.ROP_percent2.iloc[:, 3:].map(
            lambda x: f'{x:.2%}')
        self.species_rop_display = self.species_rop.map(
            lambda x: f'{x:.2e}' if isinstance(x, float) else x)

    def plot_ROP_multi(self, b, plot=False):
        """
        绘制多点ROP分析图表
        
        Args:
            b: CHEMKIN文件处理器实例
            plot: 是否显示图表
            
        Returns:
            Plotly图表对象
        """
        fig = go.Figure()
        
        # 检查是否有临时的横坐标变量设置（用于没有end_point sheets的情况）
        if hasattr(self, 'temp_variable_name') and hasattr(self, 'temp_variables'):
            x_axis_data = self.temp_variables
        else:
            x_axis_data = self.species_rop.index
        
        # 添加前三条线（Production, Consumption, Net_production）
        for i in np.arange(0, 3, 1):
            fig.add_trace(go.Scatter(
                x=x_axis_data,
                y=self.species_rop.iloc[:, i],
                name=self.species_rop.columns[i],
                line=dict(shape='linear', width=3), ))

        # 添加其他反应线
        for i in range(3, len(self.ROP_percent2.T)):
            if self.species_rop.iloc[:, i].max() > 0:
                fig.add_trace(go.Scatter(
                    x=x_axis_data,
                    y=self.species_rop.iloc[:, i],
                    name=self.species_rop.columns[i],
                    line=dict(shape='linear', width=2), ))
            else:
                fig.add_trace(go.Scatter(
                    x=x_axis_data,
                    y=self.species_rop.iloc[:, i],
                    name=self.species_rop.columns[i],
                    line=dict(shape='linear', width=2, dash='dot')))

        # 检查是否有临时的横坐标变量设置（用于没有end_point sheets的情况）
        if hasattr(self, 'temp_variable_name') and hasattr(self, 'temp_variables'):
            x_axis_title = f"{self.temp_variable_name} / {self.temp_variable_unit}" if self.temp_variable_unit else self.temp_variable_name
            x_axis_data = self.temp_variables
        else:
            x_axis_title = f"{b.variable_name} / {b.variable_unit}"
            x_axis_data = self.species_rop.index
        
        fig.update_layout(title={
            'text': f"Rate of production analysis",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=600,
            font=dict(size=12, color="RebeccaPurple"),
            xaxis={'title': x_axis_title, 'ticks': 'outside'},
            yaxis={'title': " Absolute ROP (mole-cm3-s)", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'})
        
        if plot:
            fig.show()
        return fig

    def ele_ini(self, b, reactants):
        """
        初始化元素分析

        Args:
            b: CHEMKIN文件处理器实例
            reactants: 反应物列表
        """
        # 检查mole_fractions_max
        if not hasattr(self, 'mole_fractions_max'):
            print(f"    ❌ mole_fractions_max 不存在")
            return

        # 调试信息：检查mole_fractions_max的结构
        print(f"    调试: mole_fractions_max shape: {self.mole_fractions_max.shape}")
        print(f"    调试: mole_fractions_max index: {list(self.mole_fractions_max.index)}")
        print(f"    调试: mole_fractions_max类型: {type(self.mole_fractions_max.index)}")

        self.elements_initial = pd.DataFrame(index=self.species_element.columns, columns=b.variables, data=0.0)

        for reactant in reactants:
            # 检查反应物是否存在
            if reactant not in self.species_element.index:
                continue

            if reactant not in self.mole_fractions_max.columns:
                continue

            # 获取反应物的元素组成
            reactant_elements = self.species_element.loc[reactant, :]

            # 获取反应物的最大摩尔分数（兼容不同的索引格式）
            print(f"    {reactant} - 检查mole_fractions_max索引格式...")
            print(f"    'max_values' in index: {'max_values' in self.mole_fractions_max.index}")
            print(f"    mole_fractions_max行数: {len(self.mole_fractions_max)}")

            if 'max_values' in self.mole_fractions_max.index:
                # 新格式：单行，索引为'max_values'
                reactant_max_mole_fraction = self.mole_fractions_max.loc['max_values', reactant]
                print(f"    使用新格式 (max_values)")
            elif len(self.mole_fractions_max) == 1:
                # 单行但索引不是'max_values'
                reactant_max_mole_fraction = self.mole_fractions_max.iloc[0][reactant]
                print(f"    使用单行格式 (索引: {self.mole_fractions_max.index[0]})")
            else:
                # 多行格式：计算最大值
                reactant_max_mole_fraction = self.mole_fractions_max[reactant].max()
                print(f"    使用多行格式，计算最大值")

            print(f"    {reactant} 最大摩尔分数: {reactant_max_mole_fraction}")

            # 计算元素贡献：每个元素在所有温度点都使用相同的最大摩尔分数
            for element in self.elements_initial.index:
                element_content = reactant_elements.loc[element]
                if element_content > 0:  # 只处理含有该元素的反应物
                    # 所有温度点都使用相同的最大摩尔分数值
                    self.elements_initial.loc[element, :] += element_content * reactant_max_mole_fraction

        # 过滤和转置
        self.elements_initial = self.elements_initial.loc[self.elements_initial.max(axis=1) != 0, :].T

    def element_plot(self, b, selected_element, threshold=0.05, plot=False):
        """
        绘制元素分布图表
        
        Args:
            b: CHEMKIN文件处理器实例
            selected_element: 选择的元素
            threshold: 显示阈值
            plot: 是否显示图表
            
        Returns:
            Plotly图表对象
        """
        # 检查elements_initial
        if not hasattr(self, 'elements_initial'):
            return None

        if selected_element not in self.elements_initial.columns:
            return None

        self.element_percent = pd.DataFrame(index=b.variables, columns=self.mole_fractions.columns)

        # 逐个组分计算
        for sp in self.mole_fractions.columns:
            try:
                # 检查组分是否含有目标元素
                element_content = self.species_element.loc[sp, selected_element]
                if element_content == 0:
                    continue  # 跳过不含目标元素的组分

                # 计算元素分布
                mole_fraction = self.mole_fractions.loc[:, sp]
                elements_initial_element = self.elements_initial.loc[:, selected_element]

                # 检查除零错误
                if (elements_initial_element == 0).any():
                    continue

                self.element_percent.loc[:, sp] = mole_fraction * element_content / elements_initial_element.T

            except Exception:
                continue

        self.element_percent.loc['overall'] = np.sum(self.element_percent.abs(), 0)
        self.element_percent.sort_values('overall', axis=1, inplace=True, ascending=False)
        self.element_percent.drop('overall', inplace=True)

        # 应用阈值过滤
        self.element_percent = self.element_percent.loc[:, self.element_percent.abs().max() > threshold]
        self.element_percent_display = self.element_percent.map(
            lambda x: f'{x:.2%}' if type(x) is float else x).T
        
        # 检查是否有临时的横坐标变量设置（用于没有end_point sheets的情况）
        if hasattr(self, 'temp_variable_name') and hasattr(self, 'temp_variables'):
            x_axis_title = f"{self.temp_variable_name} / {self.temp_variable_unit}" if self.temp_variable_unit else self.temp_variable_name
            x_axis_data = self.temp_variables
        else:
            x_axis_title = "Temperature / K"
            x_axis_data = self.element_percent.index

        fig = go.Figure()
        for i in range(np.sum(self.element_percent.abs().max() > 0.005)):
            fig.add_trace(go.Scatter(
                x=x_axis_data,
                y=self.element_percent.iloc[:, i],
                name=self.element_percent.columns[i],
                line=dict(shape='linear', width=2), ))

        fig.update_layout(title={
            'text': f"Element {selected_element} Distribuation",
            'y': 0.9,
            'x': 0.5,
            'xanchor': 'center',
            'yanchor': 'top',
            'font': {'size': 20}},
            width=1000, height=600,
            font=dict(size=16, color="RebeccaPurple"),
            xaxis={'title': x_axis_title, 'ticks': 'outside'},
            yaxis={'title': "Species", 'showgrid': True, 'rangemode': 'tozero', 'tickformat': '.2e'})
        
        if plot:
            fig.show()
        return fig
    
    def sg_display_format(self, dataframe):
        """
        格式化显示数据框
        
        Args:
            dataframe: 需要格式化的数据框
            
        Returns:
            格式化后的数据框
        """
        dataframe.loc['Absolute values', :] = dataframe.loc['Absolute values', :].apply(lambda x: f'{x:.2e}')
        dataframe.loc['Relative values', :] = dataframe.loc['Relative values', :].apply(lambda x: f'{x:.2%}')
        table_values = dataframe.T
        return table_values

    def flux_all(self, selected_temp, selected_element, threshold=0.01, type='integral', progress_bar=False):
        """
        计算所有组分的元素流向（全局流量矩阵）
        Args:
            selected_temp: 选择的温度点
            selected_element: 选择的元素
            threshold: 显示阈值
            type: 'integral'或'end'
            progress_bar: 是否显示进度条
        """
        if type == 'integral':
            rop_species = self.integral_ROP[selected_temp]
        elif type == 'end':
            rop_species = self.end_ROP[selected_temp]
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在处理反信息')
            progress_bar1.empty()
        self.flux = pd.DataFrame(index=self.species, columns=self.species).fillna(0)
        self.flux_normalized = pd.DataFrame(index=self.species, columns=self.species).fillna(0)
        for i, reaction in enumerate(self.reactions):
            for reactant in self.reactants[reaction]:
                if self.species_element.loc[reactant, selected_element] != 0:
                    for product in self.products[reaction]:
                        if self.species_element.loc[product, selected_element] != 0:
                            if rop_species.loc[reaction, reactant] < 0:
                                self.flux.loc[reactant, product] += rop_species.loc[reaction, reactant] * \
                                    self.get_flux_factor(reaction, selected_element).loc[reactant, product]
                                self.flux.loc[product, reactant] -= rop_species.loc[reaction, reactant] * \
                                    self.get_flux_factor(reaction, selected_element).loc[reactant, product]
                            elif rop_species.loc[reaction, reactant] > 0:
                                self.flux.loc[reactant, product] -= rop_species.loc[reaction, product] * \
                                    self.get_flux_factor(reaction, selected_element, reverse=True).loc[product, reactant]
                                self.flux.loc[product, reactant] += rop_species.loc[reaction, product] * \
                                    self.get_flux_factor(reaction, selected_element, reverse=True).loc[product, reactant]
            if progress_bar:
                progress_bar1.progress((i + 1) / len(self.reactions), f"正在处理{reaction}")
        self.flux['Production'] = np.sum(self.flux[self.flux > 0], 1)
        self.flux['Consumption'] = np.sum(self.flux[self.flux <= 0], 1)
        self.flux_normalized = pd.DataFrame(index=self.flux.index, columns=self.flux.columns)
        ROP_nor_P = self.flux[self.flux > 0].multiply(1 / self.flux['Production'], axis="index")
        ROP_nor_N = self.flux[self.flux <= 0].multiply(-1 / self.flux['Consumption'], axis="index")
        self.flux_normalized[self.flux > 0] = ROP_nor_P[self.flux > 0]
        self.flux_normalized[self.flux < 0] = ROP_nor_N[self.flux < 0]
        self.flux_normalized.loc['abs', :] = self.flux_normalized.loc['raw', :].abs()
        self.flux_normalized.index = ['Relative values', 'abs']
        self.flux_normalized.loc['Absolute values', :] = self.select_rop.loc['raw', :]
        self.flux_normalized.sort_values('abs', axis=1, inplace=True, ascending=False)
        self.flux_normalized.drop('abs', inplace=True)
        self.flux_normalized.drop('Production', axis=1, inplace=True)
        self.flux_normalized.drop('Consumption', axis=1, inplace=True)

    def process_sensitivity_multi(self, chemkin_file, target_species, gradient_variable='Temperature', threshold=0.01, mode='gradient_max'):
        """
        多点敏感性分析
        
        Args:
            chemkin_file: CHEMKIN文件处理器实例
            target_species: 目标组分名称
            gradient_variable: 梯度变量名称
            threshold: 敏感性系数阈值
            mode: 分析模式
        """
        print(f"开始敏感性分析: 目标组分={target_species}, 梯度变量={gradient_variable}, 模式={mode}")
        
        # 获取敏感性梯度数据
        gradient_sens = chemkin_file.get_sensitivity_gradients(
            self, target_species, gradient_variable, mode
        )
        
        if not gradient_sens:
            print("未获取到敏感性数据")
            return
        
        print(f"获取到 {len(gradient_sens)} 个工况的敏感性数据")
        
        # 收集所有反应标签和反应映射
        all_reactions = set()
        reaction_mappings = {}  # 存储每个工况的反应映射
        
        for condition, sens_data in gradient_sens.items():
            print(f"\n处理工况 {condition}:")
            
            # 提取当前工况的反应编号和映射
            condition_reactions = {}
            condition_reaction_names = set()
            
            for col in sens_data.index:
                if '_Sens_' in col:
                    try:
                        # 从列名中提取反应编号
                        # 例如: ' OH_Sens_C3H8+O2<=>NC3H7+HO2_GasRxn#724_Run#1_()'
                        parts = col.split('_Sens_')[1]  # 'C3H8+O2<=>NC3H7+HO2_GasRxn#724_Run#1_()'
                        if '_GasRxn#' in parts:
                            reaction_part = parts.split('_GasRxn#')[1]  # '724_Run#1_()'
                            reaction_no = reaction_part.split('_')[0]  # '724'
                            
                            # 构建反应名称（唯一：反应方程_#编号）
                            reaction_equation = parts.split('_GasRxn#')[0]  # 'C3H8+O2<=>NC3H7+HO2'
                            reaction_name = f"{reaction_equation}#{reaction_no}"
                            
                            # 直接用reaction_name，不再加dup
                            condition_reactions[col] = reaction_name
                            condition_reaction_names.add(reaction_name)
                            all_reactions.add(reaction_name)
                            
                    except Exception as e:
                        print(f"  警告：解析反应列名失败 {col}: {e}")
                        continue
            
            reaction_mappings[condition] = condition_reactions
            print(f"  成功提取 {len(condition_reactions)} 个反应")
            print(f"  反应示例: {list(condition_reaction_names)[:3]}")
        
        print(f"\n总共检测到 {len(all_reactions)} 个唯一反应")
        
        # 创建敏感性矩阵 - 使用动态反应列表
        all_reactions_list = sorted(list(all_reactions))
        self.sensitivity_matrix = pd.DataFrame(
            index=list(gradient_sens.keys()),
            columns=all_reactions_list,
            dtype=float
        ).fillna(0.0)
        
        # 填充敏感性矩阵
        for condition, sens_data in gradient_sens.items():
            condition_mapping = reaction_mappings[condition]
            
            for col, reaction_name in condition_mapping.items():
                if reaction_name in all_reactions_list:
                    sens_value = sens_data[col]
                    if pd.notna(sens_value) and abs(sens_value) >= threshold:
                        self.sensitivity_matrix.loc[condition, reaction_name] = sens_value
        
        # 过滤掉所有值都为0的反应
        non_zero_reactions = (self.sensitivity_matrix.abs() > 0).any(axis=0)
        self.sensitivity_matrix = self.sensitivity_matrix.loc[:, non_zero_reactions]
        
        # 按敏感性绝对值大小排序
        max_abs_sens = self.sensitivity_matrix.abs().max(axis=0)
        sorted_reactions = max_abs_sens.sort_values(ascending=False).index
        self.sensitivity_matrix = self.sensitivity_matrix[sorted_reactions]
        
        print(f"\n敏感性矩阵构建完成:")
        print(f"  - 工况数: {len(self.sensitivity_matrix.index)}")
        print(f"  - 有效反应数: {len(self.sensitivity_matrix.columns)}")
        print(f"  - 最大敏感性系数: {self.sensitivity_matrix.abs().max().max():.3e}")
        print(f"  - 平均敏感性系数: {self.sensitivity_matrix.abs().mean().mean():.3e}")
        
        # 显示最敏感的反应
        top_reactions = sorted_reactions[:10]
        print(f"\n最敏感的10个反应:")
        for i, reaction in enumerate(top_reactions):
            max_sens = max_abs_sens[reaction]
            print(f"  {i+1:2d}. {reaction}: {max_sens:.3e}")
        
        self.sensitivity_analysis_completed = True
    
    def plot_sensitivity_multi(self, chemkin_file, target_species, plot=False):
        """
        绘制多点敏感性分析图表
        
        Args:
            chemkin_file: CHEMKIN文件处理器实例
            target_species: 目标组分名称
            plot: 是否显示图表
            
        Returns:
            Plotly图表对象
        """
        if not hasattr(self, 'sensitivity_matrix'):
            print("错误：请先运行process_sensitivity_multi方法")
            return None
        
        # 检查是否有临时的横坐标变量设置（用于没有end_point sheets的情况）
        if hasattr(self, 'temp_variable_name') and hasattr(self, 'temp_variables'):
            x_axis_title = f"{self.temp_variable_name} / {self.temp_variable_unit}" if self.temp_variable_unit else self.temp_variable_name
            x_axis_data = self.temp_variables
            hover_x_name = self.temp_variable_name
        else:
            x_axis_title = f"{chemkin_file.variable_name} / {chemkin_file.variable_unit}"
            x_axis_data = self.sensitivity_matrix.index
            hover_x_name = chemkin_file.variable_name

        fig = go.Figure()
        
        # 添加敏感性曲线
        for i, reaction in enumerate(self.sensitivity_matrix.columns):
            # 根据敏感性系数的正负设置线型
            line_style = dict(shape='linear', width=2)
            if self.sensitivity_matrix[reaction].mean() < 0:
                line_style['dash'] = 'dot'
            
            # 简化反应名称用于图例显示
            if '#' in reaction:
                # 如果是完整的反应方程式，取前30个字符
                display_name = reaction.split('#')[0][:30] + f"...#{reaction.split('#')[-1]}"
            else:
                # 如果是原始列名，尝试简化
                display_name = reaction[:40] + '...' if len(reaction) > 40 else reaction
            
            fig.add_trace(go.Scatter(
                x=x_axis_data,
                y=self.sensitivity_matrix[reaction],
                name=display_name,
                line=line_style,
                hovertemplate=f'<b>{reaction}</b><br>' +
                             f'{hover_x_name}: %{{x}}<br>' +
                             f'敏感性系数: %{{y:.3e}}<extra></extra>'
            ))
        
        fig.update_layout(
            title={
                'text': f"{target_species} 敏感性分析",
                'y': 0.9,
                'x': 0.5,
                'xanchor': 'center',
                'yanchor': 'top',
                'font': {'size': 20}
            },
            width=1000, 
            height=600,
            font=dict(size=12, color="RebeccaPurple"),
            xaxis={
                'title': x_axis_title, 
                'ticks': 'outside'
            },
            yaxis={
                'title': "敏感性系数", 
                'showgrid': True, 
                'tickformat': '.2e',
                'zeroline': True,
                'zerolinecolor': 'black',
                'zerolinewidth': 1
            },
            legend={
                'orientation': 'v',
                'yanchor': 'top',
                'y': 1,
                'xanchor': 'left', 
                'x': 1.01
            }
        )
        
        if plot:
            fig.show()
        
        return fig
    
    def get_available_gradient_variables(self):
        """
        获取可用的梯度变量列表（仅从solution数据中获取，排除敏感性数据）
        
        Returns:
            list: 可用的变量列表
        """
        print("=" * 60)
        print("获取可用梯度变量（从solution数据）...")
        print("=" * 60)
        
        if not hasattr(self, 'soln_collect'):
            print("❌ 错误：没有找到solution数据")
            return []
        
        # 获取第一个工况的数据来确定可用变量
        first_condition = list(self.soln_collect.values())[0]
        print(f"从第一个工况检查变量，共有 {len(first_condition.columns)} 列")
        
        # 显示所有列以便调试
        print("Solution数据的所有列:")
        for i, col in enumerate(first_condition.columns):
            print(f"  {i+1:2d}. {col}")
        
        available_vars = []
        
        # 分析所有列名，提取变量类型
        for col in first_condition.columns:
            # 跳过Time列
            if 'Time' in col:
                print(f"  跳过Time列: {col}")
                continue
            
            # 排除敏感性数据列（包含_Sens_的列）
            if '_Sens_' in col:
                print(f"  跳过敏感性列: {col}")
                continue
                
            # 排除反应速率列
            if 'rxn_rate' in col.lower() or 'rate' in col.lower():
                print(f"  跳过反应速率列: {col}")
                continue
                
            # 提取变量名（去除单位和Run#信息）
            if '_Run#' in col:
                var_part = col.split('_Run#')[0].strip()
                
                # 处理不同的变量类型
                if var_part.startswith(' '):
                    var_part = var_part[1:]  # 去除开头空格
                    
                # 提取实际变量名
                if var_part == 'Temperature':
                    available_vars.append('Temperature')
                    print(f"  ✅ 发现Temperature变量: {col}")
                elif var_part == 'Pressure':
                    available_vars.append('Pressure')
                    print(f"  ✅ 发现Pressure变量: {col}")
                elif var_part == 'Density':
                    available_vars.append('Density')
                    print(f"  ✅ 发现Density变量: {col}")
                elif var_part == 'Volume':
                    available_vars.append('Volume')
                    print(f"  ✅ 发现Volume变量: {col}")
                elif var_part.startswith('Mole_fraction_'):
                    # 提取组分名，如 Mole_fraction_C3H8 -> C3H8
                    species_name = var_part.replace('Mole_fraction_', '')
                    var_name = f'Mole_fraction_{species_name}'
                    available_vars.append(var_name)
                    print(f"  ✅ 发现摩尔分数变量: {var_name} 来自列: {col}")
                elif var_part.startswith('Mass_fraction_'):
                    # 提取组分名，如 Mass_fraction_O2 -> O2
                    species_name = var_part.replace('Mass_fraction_', '')
                    var_name = f'Mass_fraction_{species_name}'
                    available_vars.append(var_name)
                    print(f"  ✅ 发现质量分数变量: {var_name} 来自列: {col}")
                elif var_part.startswith('Concentration_'):
                    # 提取组分名，如 Concentration_OH -> OH
                    species_name = var_part.replace('Concentration_', '')
                    var_name = f'Concentration_{species_name}'
                    available_vars.append(var_name)
                    print(f"  ✅ 发现浓度变量: {var_name} 来自列: {col}")
                else:
                    # 其他类型的变量
                    available_vars.append(var_part)
                    print(f"  ✅ 发现其他变量: {var_part} 来自列: {col}")
        
        # 去除重复项并排序
        available_vars = sorted(list(set(available_vars)))
        
        print("-" * 60)
        print(f"最终检测到可用梯度变量: {available_vars}")
        print("=" * 60)
        
        if not available_vars:
            print("⚠️  警告：没有找到任何可用的梯度变量！")
            print("请检查solution数据是否包含Temperature、Pressure或摩尔分数等变量")
        
        return available_vars 