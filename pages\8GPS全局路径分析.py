import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

# 添加GPS集成模块
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
from src.gps_integration import IntegratedGPSAnalyzer

# ========================================
# 页面配置和主题应用
# ========================================

st.set_page_config(layout="wide", page_title="GPS全局路径分析", page_icon="🌐")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">🌐 GPS全局路径分析</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        Global Pathway Search (GPS) - 识别化学反应网络中的关键全局路径和枢纽组分
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if ('a' and 'b') not in st.session_state:
    st.markdown("""
    <div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行GPS分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# 检查净反应速率数据
elif not st.session_state['b'].net_reaction_rate_exist:
    st.markdown("""
    <div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少净反应速率数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有净反应速率输出，无法进行GPS分析。请检查CHEMKIN输出设置，确保输出了Net_rxn_rate数据。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# 检查摩尔分数数据
elif not hasattr(st.session_state['a'], 'mole_fractions') or st.session_state['a'].mole_fractions is None:
    st.markdown("""
    <div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少摩尔分数数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有摩尔分数数据，GPS分析不可用。请检查CHEMKIN输出设置，确保输出了Mole_fraction数据。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# 检查反应焓数据并显示警告
has_enthalpy_data = False  # 假设没有反应焓数据
if not has_enthalpy_data:
    st.markdown("""
    <div class="warning-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少反应焓数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有反应焓数据，GPSA分析中的Q_GP（净热释放率）值将不可用。
        </p>
    </div>
    """, unsafe_allow_html=True)

# ========================================
# 初始化GPS分析器
# ========================================

@st.cache_resource
def initialize_gps_analyzer():
    """初始化GPS分析器"""
    return IntegratedGPSAnalyzer()

gps_analyzer = initialize_gps_analyzer()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ GPS分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # 数据类型选择
        data_type = st.radio(
            '**数据类型**', 
            ['积分ROP', 'EndPointROP'], 
            captions=[
                '使用积分反应速率数据',
                '使用终点反应速率数据'
            ],
            help="选择用于GPS分析的反应速率数据类型"
        )
        use_end_point = (data_type == 'EndPointROP')
    
    with col2:
        # 条件选择
        if hasattr(st.session_state['a'], 'integral_ROP') and st.session_state['a'].integral_ROP:
            available_conditions = list(st.session_state['a'].integral_ROP.keys())
        else:
            available_conditions = ['700']  # 默认值
        
        condition_key = st.selectbox(
            "**分析条件**：", 
            options=available_conditions,
            index=0,
            help="选择要分析的温度/条件点"
        )
    
    with col3:
        # 源组分选择
        available_species = st.session_state['a'].species
        source_species = st.selectbox(
            "**源组分**：", 
            options=available_species,
            index=available_species.index('CH4') if 'CH4' in available_species else 0,
            help="选择反应路径的起始组分（通常为燃料组分）"
        )
    
    with col4:
        # 目标组分选择
        target_species = st.selectbox(
            "**目标组分**：", 
            options=available_species,
            index=available_species.index('CO2') if 'CO2' in available_species else 1,
            help="选择反应路径的目标组分（通常为产物组分）"
        )
    
    st.markdown('</div>', unsafe_allow_html=True)

# GPS算法参数
with st.expander("🔧 GPS算法参数", expanded=False):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        alpha = st.number_input(
            '**Alpha (α)**', 
            min_value=0.0, 
            max_value=1.0, 
            value=0.1, 
            step=0.01,
            help="枢纽组分选择阈值"
        )
    
    with col2:
        beta = st.number_input(
            '**Beta (β)**', 
            min_value=0.0, 
            max_value=1.0, 
            value=0.5, 
            step=0.01,
            help="反应级别组分选择阈值"
        )
    
    with col3:
        K = st.number_input(
            '**K值**', 
            min_value=1, 
            max_value=10, 
            value=1, 
            step=1,
            help="路径搜索参数"
        )
    
    with col4:
        traced_element = st.selectbox(
            "**追踪元素**：", 
            options=['C', 'H', 'O', 'N'],
            index=0,
            help="选择要追踪的元素类型"
        )
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 自动分析执行
# ========================================

def run_gps_analysis_auto():
    """自动运行GPS分析"""
    try:
        # 创建进度条
        progress_bar = st.progress(0)
        status_text = st.empty()

        # 步骤1: 加载项目数据
        status_text.text("🔄 正在加载项目数据...")
        progress_bar.progress(10)

        success = gps_analyzer.load_project_data(
            gas_obj=st.session_state['a'],
            condition_key=condition_key,
            use_end_point=use_end_point
        )

        if not success:
            st.error("❌ 数据加载失败")
            return False

        progress_bar.progress(25)

        # 步骤2: 运行GPS分析
        status_text.text("🚀 正在进行GPS分析...")
        progress_bar.progress(40)

        gps_results = gps_analyzer.run_gps_analysis(
            source=source_species,
            target=target_species,
            K=K,
            alpha=alpha,
            beta=beta,
            traced_element=traced_element
        )

        progress_bar.progress(60)

        # 步骤3: 自动运行GPSA分析
        status_text.text("🔬 正在运行GPSA分析...")
        pathway_details = gps_analyzer.get_pathway_details()
        gpsa_results = {}

        if pathway_details:
            total_pathways = len(pathway_details)
            for i, pathway in enumerate(pathway_details):
                try:
                    gpsa_result = gps_analyzer.run_gpsa_analysis(pathway['name'])
                    gpsa_results[pathway['name']] = gpsa_result

                    # 更新进度
                    current_progress = 60 + (30 * (i + 1) / total_pathways)
                    progress_bar.progress(int(current_progress))
                    status_text.text(f"🔬 正在分析路径 {i+1}/{total_pathways}...")

                except Exception as e:
                    st.warning(f"⚠️ 路径 {pathway['name'][:30]}... GPSA分析失败: {str(e)}")

        progress_bar.progress(95)

        # 步骤4: 存储结果
        status_text.text("💾 正在保存结果...")
        st.session_state['gps_analyzer'] = gps_analyzer
        st.session_state['gps_results'] = gps_results
        st.session_state['gpsa_results'] = gpsa_results
        st.session_state['has_enthalpy_data'] = has_enthalpy_data
        st.session_state['analysis_completed'] = True

        progress_bar.progress(100)
        status_text.text("✅ GPS和GPSA分析完成！")

        # 清理进度指示器
        import time
        time.sleep(1)
        progress_bar.empty()
        status_text.empty()

        return True

    except Exception as e:
        st.error(f"❌ GPS分析失败: {str(e)}")
        return False

# 检查是否需要自动运行分析
auto_run_analysis = True

# 检查必要参数是否可用
required_params_available = all([
    source_species,
    target_species,
    condition_key,
    alpha is not None,
    beta is not None,
    K is not None,
    traced_element
])

if auto_run_analysis and required_params_available:
    # 检查是否已经运行过分析
    if 'analysis_completed' not in st.session_state or not st.session_state['analysis_completed']:
        st.info("🔄 正在自动运行GPS分析...")
        run_gps_analysis_auto()
    else:
        st.success("✅ GPS分析已完成，结果如下所示")

elif not required_params_available:
    st.warning("⚠️ 请确保所有必要参数都已设置。分析将在参数完整后自动运行。")

# 手动重新分析按钮
if st.button("🔄 重新运行分析", help="使用当前参数重新运行GPS和GPSA分析"):
    st.session_state['analysis_completed'] = False
    st.rerun()

# ========================================
# 路径详情区域（始终可见）
# ========================================

if 'gps_results' in st.session_state and st.session_state['gps_results']:

    # 创建路径代码映射
    pathway_details = st.session_state['gps_analyzer'].get_pathway_details()
    pathway_codes = {}

    if pathway_details:
        for i, pathway in enumerate(pathway_details):
            pathway_code = f"P{i+1}"
            pathway_codes[pathway['name']] = pathway_code
        st.session_state['pathway_codes'] = pathway_codes

    st.markdown("## 🛤️ 全局路径详情")

    if pathway_details:
        # 路径详细信息表格
        st.markdown("### 📋 路径信息表")

        # 准备路径信息表格数据
        pathway_info_data = []
        for pathway in pathway_details:
            pathway_code = pathway_codes.get(pathway['name'], f"P{len(pathway_info_data)+1}")
            source_species = pathway['species_sequence'][0] if pathway['species_sequence'] else "Unknown"
            target_species = pathway['species_sequence'][-1] if len(pathway['species_sequence']) > 1 else "Unknown"

            pathway_info_data.append({
                '路径代码': pathway_code,
                '源组分': source_species,
                '目标组分': target_species,
                '路径长度': pathway['length'],
                '完整路径': " → ".join(pathway['species_sequence'])
            })

        pathway_info_df = pd.DataFrame(pathway_info_data)
        st.dataframe(pathway_info_df, use_container_width=True)

        # 下载路径信息
        pathway_csv = pathway_info_df.to_csv(index=False, encoding='utf-8-sig')
        st.download_button(
            label="📥 下载路径信息表 (CSV)",
            data=pathway_csv,
            file_name=f"GPS_pathway_details_{condition_key}K.csv",
            mime="text/csv",
            use_container_width=True
        )
    else:
        st.info("未找到全局路径")

# ========================================
# 结果显示区域
# ========================================

if 'gps_results' in st.session_state and st.session_state['gps_results']:



    # ========================================
    # 分析结果标签页
    # ========================================

    # 初始化标签页状态
    if 'active_tab' not in st.session_state:
        st.session_state.active_tab = 0

    # 创建标签页（3个标签页）
    tab_names = ["📈 GPSA参数比较", "📊 参数趋势分析", "🎯 组分选择分析"]

    # 使用session state来维护标签页状态
    selected_tab = st.radio(
        "选择分析视图:",
        options=range(len(tab_names)),
        format_func=lambda x: tab_names[x],
        index=st.session_state.active_tab,
        horizontal=True,
        key="tab_selector"
    )

    # 更新活动标签页
    if selected_tab != st.session_state.active_tab:
        st.session_state.active_tab = selected_tab

    # 获取路径代码映射
    pathway_codes = st.session_state.get('pathway_codes', {})

    # ========================================
    # Tab 1: GPSA参数比较
    # ========================================

    if selected_tab == 0:
        if 'gpsa_results' in st.session_state and st.session_state['gpsa_results']:
            st.markdown("### 📈 GPSA参数比较分析")

            # 显示分析进度信息
            if 'analysis_completed' in st.session_state and st.session_state['analysis_completed']:
                gpsa_count = len(st.session_state['gpsa_results'])
                st.info(f"✅ 已完成 {gpsa_count} 个路径的GPSA分析")

            gpsa_results = st.session_state['gpsa_results']
            has_enthalpy = st.session_state.get('has_enthalpy_data', False)

            # 准备数据
            pathway_names = []
            pathway_codes_list = []
            d_gp_values = []
            r_gp_values = []
            q_gp_values = []

            for pathway_name, gpsa_data in gpsa_results.items():
                if pathway_name in pathway_codes:
                    pathway_names.append(pathway_name)
                    pathway_codes_list.append(pathway_codes[pathway_name])

                    # 提取GPSA指标
                    d_gp_values.append(gpsa_data.get('D_GP', [0])[0] if gpsa_data.get('D_GP') else 0)
                    r_gp_values.append(gpsa_data.get('R_GP', [0])[0] if gpsa_data.get('R_GP') else 0)
                    if has_enthalpy:
                        q_gp_values.append(gpsa_data.get('Q_GP', [0])[0] if gpsa_data.get('Q_GP') else 0)
                    else:
                        q_gp_values.append(None)

            if pathway_codes_list:
                # 创建比较图表
                col1, col2 = st.columns(2)

                with col1:
                    # D_GP 比较图
                    fig_d = go.Figure(data=[
                        go.Bar(
                            x=pathway_codes_list,
                            y=d_gp_values,
                            name='D_GP (路径主导性)',
                            marker_color='#FF6B6B',
                            text=[f"{val:.4f}" for val in d_gp_values],
                            textposition='auto',
                        )
                    ])
                    fig_d.update_layout(
                        title="路径主导性 (D_GP) 比较",
                        xaxis_title="路径代码",
                        yaxis_title="D_GP 值",
                        height=400
                    )
                    st.plotly_chart(fig_d, use_container_width=True)

                with col2:
                    # R_GP 比较图
                    fig_r = go.Figure(data=[
                        go.Bar(
                            x=pathway_codes_list,
                            y=r_gp_values,
                            name='R_GP (净自由基产生率)',
                            marker_color='#4ECDC4',
                            text=[f"{val:.2e}" for val in r_gp_values],
                            textposition='auto',
                        )
                    ])
                    fig_r.update_layout(
                        title="净自由基产生率 (R_GP) 比较",
                        xaxis_title="路径代码",
                        yaxis_title="R_GP 值",
                        height=400
                    )
                    st.plotly_chart(fig_r, use_container_width=True)

                # Q_GP 比较图（如果有反应焓数据）
                if has_enthalpy and any(q is not None for q in q_gp_values):
                    fig_q = go.Figure(data=[
                        go.Bar(
                            x=pathway_codes_list,
                            y=[q for q in q_gp_values if q is not None],
                            name='Q_GP (净热释放率)',
                            marker_color='#45B7D1',
                            text=[f"{val:.2e}" for val in q_gp_values if val is not None],
                            textposition='auto',
                        )
                    ])
                    fig_q.update_layout(
                        title="净热释放率 (Q_GP) 比较",
                        xaxis_title="路径代码",
                        yaxis_title="Q_GP 值",
                        height=400
                    )
                    st.plotly_chart(fig_q, use_container_width=True)

                # 创建下载表格
                st.markdown("### 📊 GPSA参数数据表")

                # 准备表格数据
                table_data = {
                    '路径代码': pathway_codes_list,
                    '源组分': [pathway['species_sequence'][0] if pathway['species_sequence'] else "Unknown"
                              for pathway in pathway_details if pathway['name'] in pathway_names],
                    '目标组分': [pathway['species_sequence'][-1] if len(pathway['species_sequence']) > 1 else "Unknown"
                                for pathway in pathway_details if pathway['name'] in pathway_names],
                    'D_GP (路径主导性)': [f"{val:.6f}" for val in d_gp_values],
                    'R_GP (净自由基产生率)': [f"{val:.6e}" for val in r_gp_values]
                }

                if has_enthalpy:
                    table_data['Q_GP (净热释放率)'] = [f"{val:.6e}" if val is not None else "N/A" for val in q_gp_values]

                gpsa_df = pd.DataFrame(table_data)
                st.dataframe(gpsa_df, use_container_width=True)

                # 下载按钮
                csv_data = gpsa_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="📥 下载GPSA参数表 (CSV)",
                    data=csv_data,
                    file_name=f"GPSA_parameters_{condition_key}K.csv",
                    mime="text/csv",
                    use_container_width=True
                )
            else:
                st.info("未找到GPSA分析结果")
        else:
            st.info("请先运行GPS分析以查看GPSA参数比较")

    # ========================================
    # Tab 2: 参数趋势分析
    # ========================================

    elif selected_tab == 1:
        st.markdown("### 📊 参数趋势分析")

        # 检查是否有多个条件可用于趋势分析
        if hasattr(st.session_state['a'], 'integral_ROP') and len(st.session_state['a'].integral_ROP) > 1:

            # 参数选择
            trend_param = st.selectbox(
                "选择要分析的参数:",
                options=['D_GP', 'R_GP', 'Q_GP'] if st.session_state.get('has_enthalpy_data', False) else ['D_GP', 'R_GP'],
                format_func=lambda x: {
                    'D_GP': 'D_GP (路径主导性)',
                    'R_GP': 'R_GP (净自由基产生率)',
                    'Q_GP': 'Q_GP (净热释放率)'
                }[x],
                help="选择要在趋势图中显示的GPSA参数"
            )

            if st.button("🔄 运行多条件趋势分析", use_container_width=True):
                try:
                    # 获取所有可用条件
                    all_conditions = list(st.session_state['a'].integral_ROP.keys())
                    trend_data = {}

                    # 创建进度指示器
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    total_conditions = len(all_conditions)

                    status_text.text(f"🔄 开始多条件趋势分析 (共 {total_conditions} 个条件)...")

                    # 为每个条件运行GPS分析
                    for i, condition in enumerate(all_conditions):
                        try:
                            # 更新进度
                            progress = int((i / total_conditions) * 100)
                            progress_bar.progress(progress)
                            status_text.text(f"🔄 正在分析条件 {condition}K ({i+1}/{total_conditions})...")

                            # 重新加载数据并分析
                            temp_analyzer = IntegratedGPSAnalyzer()
                            temp_analyzer.load_project_data(
                                gas_obj=st.session_state['a'],
                                condition_key=condition,
                                use_end_point=use_end_point
                            )

                            temp_gps_results = temp_analyzer.run_gps_analysis(
                                source=source_species,
                                target=target_species,
                                K=K,
                                alpha=alpha,
                                beta=beta,
                                traced_element=traced_element
                            )

                            # 运行GPSA分析
                            temp_pathway_details = temp_analyzer.get_pathway_details()
                            condition_gpsa = {}

                            if temp_pathway_details:
                                pathway_count = len(temp_pathway_details)
                                for j, pathway in enumerate(temp_pathway_details):
                                    try:
                                        # 更新子进度
                                        sub_progress = progress + int((j / pathway_count) * (100 / total_conditions))
                                        progress_bar.progress(min(sub_progress, 99))
                                        status_text.text(f"🔬 分析条件 {condition}K - 路径 {j+1}/{pathway_count}")

                                        gpsa_result = temp_analyzer.run_gpsa_analysis(pathway['name'])
                                        condition_gpsa[pathway['name']] = gpsa_result
                                    except:
                                        continue

                            trend_data[condition] = condition_gpsa

                        except Exception as e:
                            st.warning(f"条件 {condition} 分析失败: {str(e)}")
                            continue

                    # 完成分析
                    progress_bar.progress(100)
                    status_text.text("💾 正在保存趋势数据...")

                    # 存储趋势数据
                    st.session_state['trend_data'] = trend_data
                    st.session_state['trend_conditions'] = all_conditions

                    # 清理进度指示器
                    import time
                    time.sleep(1)
                    progress_bar.empty()
                    status_text.empty()

                    st.success(f"✅ 完成 {len(trend_data)} 个条件的趋势分析")

                except Exception as e:
                    st.error(f"❌ 趋势分析失败: {str(e)}")

            # 显示趋势结果
            if 'trend_data' in st.session_state and st.session_state['trend_data']:
                st.markdown("#### 📊 参数趋势图")

                trend_data = st.session_state['trend_data']
                conditions = st.session_state['trend_conditions']

                # 准备趋势图数据
                fig_trend = go.Figure()

                # 获取所有路径名称
                all_pathways = set()
                for condition_data in trend_data.values():
                    all_pathways.update(condition_data.keys())

                # 为每个路径创建趋势线
                colors = px.colors.qualitative.Set3
                for i, pathway_name in enumerate(all_pathways):
                    if pathway_name in st.session_state.get('pathway_codes', {}):
                        pathway_code = st.session_state['pathway_codes'][pathway_name]

                        x_values = []
                        y_values = []

                        for condition in conditions:
                            if condition in trend_data and pathway_name in trend_data[condition]:
                                gpsa_data = trend_data[condition][pathway_name]
                                param_value = gpsa_data.get(trend_param, [0])[0] if gpsa_data.get(trend_param) else 0

                                x_values.append(float(condition))
                                y_values.append(param_value)

                        if x_values and y_values:
                            fig_trend.add_trace(go.Scatter(
                                x=x_values,
                                y=y_values,
                                mode='lines+markers',
                                name=pathway_code,
                                line=dict(color=colors[i % len(colors)], width=2),
                                marker=dict(size=8)
                            ))

                param_labels = {
                    'D_GP': 'D_GP (路径主导性)',
                    'R_GP': 'R_GP (净自由基产生率)',
                    'Q_GP': 'Q_GP (净热释放率)'
                }

                fig_trend.update_layout(
                    title=f"{param_labels[trend_param]} 随温度变化趋势",
                    xaxis_title="温度 (K)",
                    yaxis_title=param_labels[trend_param],
                    height=500,
                    legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
                )

                st.plotly_chart(fig_trend, use_container_width=True)

                # 创建趋势数据表
                st.markdown("#### 📋 趋势数据表")

                # 准备表格数据
                trend_table_data = {'温度 (K)': conditions}

                for pathway_name in all_pathways:
                    if pathway_name in st.session_state.get('pathway_codes', {}):
                        pathway_code = st.session_state['pathway_codes'][pathway_name]

                        param_values = []
                        for condition in conditions:
                            if condition in trend_data and pathway_name in trend_data[condition]:
                                gpsa_data = trend_data[condition][pathway_name]
                                param_value = gpsa_data.get(trend_param, [0])[0] if gpsa_data.get(trend_param) else 0
                                param_values.append(f"{param_value:.6e}" if trend_param in ['R_GP', 'Q_GP'] else f"{param_value:.6f}")
                            else:
                                param_values.append("N/A")

                        trend_table_data[pathway_code] = param_values

                trend_df = pd.DataFrame(trend_table_data)
                st.dataframe(trend_df, use_container_width=True)

                # 下载趋势数据
                trend_csv = trend_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label=f"📥 下载{param_labels[trend_param]}趋势数据 (CSV)",
                    data=trend_csv,
                    file_name=f"GPS_{trend_param}_trend_analysis.csv",
                    mime="text/csv",
                    use_container_width=True
                )

        else:
            st.info("💡 当前只有单一条件数据，无法进行趋势分析。需要多个温度条件的数据才能显示参数变化趋势。")
    
    # ========================================
    # Tab 3: 组分选择分析
    # ========================================

    elif selected_tab == 2:
        st.markdown("### 🎯 组分选择分析")

        species_details = st.session_state['gps_analyzer'].get_species_selection_details()

        # 创建组分选择表格
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### 🏆 枢纽组分 (Alpha选择)")
            if species_details['by_alpha']:
                # 获取枢纽组分的分数
                hubs_data = st.session_state['gps_results'].get('hubs', {})
                hub_table_data = []

                for species in species_details['by_alpha']:
                    score = hubs_data.get(species, {}).get('score', 0) if hubs_data else 0
                    hub_table_data.append({
                        '组分': species,
                        '重要性分数': f"{score:.6f}"
                    })

                hub_df = pd.DataFrame(hub_table_data)
                st.dataframe(hub_df, use_container_width=True)

                # 下载按钮
                hub_csv = hub_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="📥 下载枢纽组分表",
                    data=hub_csv,
                    file_name=f"hub_species_{condition_key}K.csv",
                    mime="text/csv",
                    key="download_hub"
                )
            else:
                st.info("无枢纽组分")

        with col2:
            st.markdown("#### 🛤️ 路径组分 (K选择)")
            if species_details['by_K']:
                pathway_table_data = []
                for item in species_details['by_K']:
                    # 使用路径代码
                    pathways_with_codes = []
                    for pathway in item['pathways']:
                        pathway_code = pathway_codes.get(pathway, pathway[:20] + "...")
                        pathways_with_codes.append(pathway_code)

                    pathway_table_data.append({
                        '组分': item['species'],
                        '相关路径': ", ".join(pathways_with_codes[:3]) + ("..." if len(pathways_with_codes) > 3 else ""),
                        '路径数量': len(item['pathways'])
                    })

                pathway_df = pd.DataFrame(pathway_table_data)
                st.dataframe(pathway_df, use_container_width=True)

                # 下载按钮
                pathway_csv = pathway_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="📥 下载路径组分表",
                    data=pathway_csv,
                    file_name=f"pathway_species_{condition_key}K.csv",
                    mime="text/csv",
                    key="download_pathway"
                )
            else:
                st.info("无路径组分")

        with col3:
            st.markdown("#### ⚗️ 反应组分 (Beta选择)")
            if species_details['by_beta']:
                reaction_table_data = []
                for item in species_details['by_beta']:
                    reaction_table_data.append({
                        '组分': item['species'],
                        '相关反应数': len(item['reactions']),
                        '反应详情': ", ".join(item['reactions'][:2]) + ("..." if len(item['reactions']) > 2 else "")
                    })

                reaction_df = pd.DataFrame(reaction_table_data)
                st.dataframe(reaction_df, use_container_width=True)

                # 下载按钮
                reaction_csv = reaction_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="📥 下载反应组分表",
                    data=reaction_csv,
                    file_name=f"reaction_species_{condition_key}K.csv",
                    mime="text/csv",
                    key="download_reaction"
                )
            else:
                st.info("无反应组分")

        # ========================================
        # 组分重要性分析
        # ========================================

        st.markdown("#### 📊 组分重要性分析")

        # 获取枢纽组分的分数
        if 'gps_results' in st.session_state:
            hubs_data = st.session_state['gps_results'].get('hubs', {})
            if hubs_data:
                hub_species = list(hubs_data.keys())
                hub_scores = [hubs_data[hub]['score'] for hub in hub_species]

                col1, col2 = st.columns([2, 1])

                with col1:
                    # 创建枢纽组分重要性图
                    fig_importance = go.Figure(data=[
                        go.Bar(
                            x=hub_species,
                            y=hub_scores,
                            marker_color='lightcoral',
                            text=[f"{score:.3f}" for score in hub_scores],
                            textposition='auto',
                        )
                    ])

                    fig_importance.update_layout(
                        title="枢纽组分重要性分数",
                        xaxis_title="组分",
                        yaxis_title="重要性分数",
                        height=400
                    )

                    st.plotly_chart(fig_importance, use_container_width=True)

                with col2:
                    # 重要性数据表
                    importance_table_data = []
                    for species, score in zip(hub_species, hub_scores):
                        importance_table_data.append({
                            '组分': species,
                            '重要性分数': f"{score:.6f}"
                        })

                    importance_df = pd.DataFrame(importance_table_data)
                    st.markdown("##### 📋 重要性数据表")
                    st.dataframe(importance_df, use_container_width=True)

                    # 下载重要性数据
                    importance_csv = importance_df.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="📥 下载重要性数据",
                        data=importance_csv,
                        file_name=f"species_importance_{condition_key}K.csv",
                        mime="text/csv",
                        key="download_importance"
                    )
            else:
                st.info("无枢纽组分重要性数据")


    

    
    # ========================================
    # 结果导出
    # ========================================

    st.markdown("## 💾 结果导出")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("📁 导出完整GPS分析结果", use_container_width=True):
            try:
                output_dir = st.session_state['gps_analyzer'].export_results("gps_analysis_results")
                st.success(f"✅ 完整结果已导出到: {output_dir}")
            except Exception as e:
                st.error(f"❌ 导出失败: {str(e)}")

    with col2:
        if st.button("📊 导出所有数据表", use_container_width=True):
            try:
                # 创建综合数据包
                import zipfile
                import io
                from datetime import datetime

                # 创建内存中的zip文件
                zip_buffer = io.BytesIO()

                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    # 添加分析信息
                    info_text = f"""GPS分析信息
分析条件: {condition_key}K
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
数据文件: GPS分析结果数据包
"""
                    zip_file.writestr(f"analysis_info_{condition_key}K.txt", info_text)

                zip_buffer.seek(0)

                # 提供下载
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                st.download_button(
                    label="📥 下载数据包 (ZIP)",
                    data=zip_buffer.getvalue(),
                    file_name=f"GPS_analysis_data_{condition_key}K_{timestamp}.zip",
                    mime="application/zip",
                    use_container_width=True
                )

                st.success("✅ 数据包准备完成，点击上方按钮下载")

            except Exception as e:
                st.error(f"❌ 数据包创建失败: {str(e)}")

else:
    st.info("👆 请设置参数并点击'开始GPS分析'按钮来运行分析")
