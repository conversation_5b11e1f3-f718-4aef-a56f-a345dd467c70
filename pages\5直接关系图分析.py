import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

# ========================================
# 页面配置和主题应用
# ========================================

st.set_page_config(layout="wide", page_title="直接关系图分析", page_icon="🕸️")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">🕸️ 直接关系图分析 (DRGEP)</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        基于DRGEP方法分析组分间的直接关系和重要性系数 - 适用于组分直接关联性
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 状态检查和错误处理
# ========================================

if ('a' and 'b') not in st.session_state:
    st.markdown("""
    <div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">🚨 请先完成数据处理</h3>
        <p style="margin: 0.5rem 0 0 0;">
            请返回首页完成前处理和后处理步骤后再进行直接关系图分析。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

elif not st.session_state['b'].net_reaction_rate_exist:
    st.markdown("""
    <div class="error-card">
        <h3 style="color: var(--warning-color); margin: 0;">⚠️ 缺少净反应速率数据</h3>
        <p style="margin: 0.5rem 0 0 0;">
            当前数据文件中没有净反应速率输出，无法进行DRGEP分析。请检查CHEMKIN输出设置，确保输出了Net_rxn_rate数据。
        </p>
    </div>
    """, unsafe_allow_html=True)
    st.stop()

# ========================================
# 参数设置区域
# ========================================

with st.expander("🎛️ DRGEP分析参数设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        ROP_type = st.radio(
            '**分析类型**', 
            ['积分ROP', 'EndPointROP'], 
            captions=[
                '将ROP沿时间或距离积分',
                '取稳态最后一点ROP'
            ],
            help="选择合适的ROP分析类型"
        )
    
    with col2:
        # 目标组分选择（单选）
        available_species = st.session_state['a'].rop_species_output if hasattr(st.session_state['a'], 'rop_species_output') else st.session_state['a'].species
        target_species_single = st.selectbox(
            "**目标组分**：", 
            options=available_species,
            index=0 if available_species else None,
            help="选择要分析的目标组分（DRGEP分析通常针对单一目标组分）"
        )
        target_species = [target_species_single] if target_species_single else []
    
    with col3:
        # 重要性系数阈值
        importance_threshold = st.number_input(
            '**重要性系数阈值**', 
            min_value=0.0, 
            max_value=1.0, 
            value=0.1,
            format="%.3f",
            help="设置显示阈值，过滤较小的重要性系数"
        )
    
    with col4:
        # 显示组分数量限制
        max_species_display = st.number_input(
            '**最大显示组分数**', 
            min_value=5, 
            max_value=50, 
            value=10,
            help="限制显示的组分数量，避免图表过于复杂"
        )
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    # 检查是否需要选择横坐标变量（没有end_point sheets的情况）
    if hasattr(st.session_state['b'], 'need_extract_endpoints') and st.session_state['b'].need_extract_endpoints:
        st.markdown("---")
        st.markdown("##### 🎯 横坐标变量选择")
        st.info("检测到没有end_point数据表，请选择横坐标变量和数据点类型：")
        
        col1_axis, col2_axis = st.columns(2)
        
        with col1_axis:
            # 获取可用的横坐标变量
            x_vars_last = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'last')
            x_vars_first = st.session_state['b'].get_available_x_axis_variables(st.session_state['a'], 'first')
            
            # 合并两种数据点的变量选项
            all_x_vars = set(x_vars_last.keys()) | set(x_vars_first.keys())
            
            if all_x_vars:
                selected_x_var = st.selectbox(
                    "**横坐标变量**：",
                    options=list(all_x_vars),
                    help="选择用作横坐标的变量"
                )
            else:
                st.error("未找到可用的横坐标变量")
                selected_x_var = None
        
        with col2_axis:
            point_type = st.radio(
                "**数据点类型**：",
                options=['last', 'first'],
                format_func=lambda x: '最后一点' if x == 'last' else '第一点',
                help="选择使用每个工况的第一点还是最后一点数据"
            )
        
        # 设置临时变量用于绘图
        if selected_x_var and point_type:
            x_vars_data = x_vars_last if point_type == 'last' else x_vars_first
            if selected_x_var in x_vars_data:
                st.session_state['a'].temp_variables = x_vars_data[selected_x_var]
                st.session_state['a'].temp_variable_name = selected_x_var
                # 提取单位信息
                if 'Temperature' in selected_x_var:
                    st.session_state['a'].temp_variable_unit = 'K'
                elif 'Pressure' in selected_x_var:
                    st.session_state['a'].temp_variable_unit = 'Pa'
                elif 'Mole_fraction' in selected_x_var:
                    st.session_state['a'].temp_variable_unit = ''
                else:
                    st.session_state['a'].temp_variable_unit = ''
                
                st.success(f"已选择横坐标变量: {selected_x_var} ({point_type}点数据)")
                st.write(f"数据范围: {min(x_vars_data[selected_x_var]):.3f} - {max(x_vars_data[selected_x_var]):.3f}")
    else:
        # 清除临时变量
        if hasattr(st.session_state['a'], 'temp_variables'):
            delattr(st.session_state['a'], 'temp_variables')
        if hasattr(st.session_state['a'], 'temp_variable_name'):
            delattr(st.session_state['a'], 'temp_variable_name')
        if hasattr(st.session_state['a'], 'temp_variable_unit'):
            delattr(st.session_state['a'], 'temp_variable_unit')

# ========================================
# 数据处理和分析
# ========================================

if target_species:
    try:
        # 获取数据
        gas_processor = st.session_state['a']
        chemkin_solution = st.session_state['b']
        
        # 确定数据类型
        data_type = 'integral' if ROP_type == '积分ROP' else 'endpoint'
        
        # 检查数据可用性 - DRGEP使用净反应速率，不是ROP
        if data_type == 'integral':
            # 对于积分类型，需要从solution数据计算
            if not hasattr(gas_processor, 'soln_collect') or not gas_processor.soln_collect:
                st.error("没有找到solution数据，无法计算积分净反应速率")
                st.stop()
            analysis_type = "积分净反应速率"
        else:
            # 对于终点类型，可以从end_point数据获取
            if hasattr(chemkin_solution, 'end_point_sheets') and chemkin_solution.end_point_sheets:
                analysis_type = "终点净反应速率"
            elif hasattr(gas_processor, 'soln_collect') and gas_processor.soln_collect:
                analysis_type = "终点净反应速率（从solution提取）"
            else:
                st.error("没有找到净反应速率数据")
                st.stop()
        
        # 创建进度显示
        progress_container = st.container()
        with progress_container:
            progress_bar = st.progress(0)
            progress_text = st.empty()
        
        def progress_callback(step, total, message):
            """进度回调函数"""
            progress = step / total
            progress_bar.progress(progress)
            progress_text.text(f"🔄 {message} ({step}/{total})")
        
        # 调用后端计算方法
        with st.spinner('正在计算DRGEP重要性系数...'):
            importance_coeffs_dict = chemkin_solution.calculate_drgep_importance_coefficients(
                gas_processor, target_species, data_type, progress_callback
            )
        
        # 清空进度显示
        progress_container.empty()
        
        # ========================================
        # 结果显示
        # ========================================
        
        st.markdown("""
        <div class="success-card">
            <h3 style="color: var(--success-color); margin: 0;">✅ DRGEP分析完成</h3>
            <p style="margin: 0.5rem 0 0 0;">
                成功计算了所有工况条件下的组分重要性系数
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # 创建标签页
        tab1, tab2, tab3 = st.tabs(["📈 系数变化趋势", "🔍 单一工况详情", "📋 数据导出"])
        
        # 准备总览数据（用于趋势分析）
        conditions = list(importance_coeffs_dict.keys())
        species_names = gas_processor.species
        summary_data = []
        
        for species in species_names:
            max_coeff = max([importance_coeffs_dict[cond].get(species, 0.0) for cond in conditions])
            if max_coeff >= importance_threshold:
                row = {'组分': species, '最大重要性系数': max_coeff}
                for cond in conditions:
                    row[f'工况_{cond}'] = importance_coeffs_dict[cond].get(species, 0.0)
                summary_data.append(row)
        
        # 按最大重要性系数排序
        summary_data.sort(key=lambda x: x['最大重要性系数'], reverse=True)
        summary_data = summary_data[:max_species_display]
        
        with tab1:
            st.subheader("📈 重要性系数变化趋势")
            
            if len(conditions) > 1 and summary_data:
                # 自动显示所有高于阈值的组分
                selected_species_for_trend = [row['组分'] for row in summary_data]
                
                if selected_species_for_trend:
                    # 确定横坐标数据和标题
                    if hasattr(st.session_state['a'], 'temp_variable_name') and hasattr(st.session_state['a'], 'temp_variables'):
                        x_axis_title = f"{st.session_state['a'].temp_variable_name} / {st.session_state['a'].temp_variable_unit}" if st.session_state['a'].temp_variable_unit else st.session_state['a'].temp_variable_name
                        x_axis_data = st.session_state['a'].temp_variables
                    else:
                        # 使用实际的变量值作为横坐标，而不是工况序号
                        x_axis_title = f"{st.session_state['b'].variable_name} / {st.session_state['b'].variable_unit}"
                        x_axis_data = st.session_state['b'].variables  # 使用实际的温度值等
                    
                    # 创建趋势图
                    fig_trend = go.Figure()
                    
                    for species in selected_species_for_trend:
                        y_values = [importance_coeffs_dict[cond].get(species, 0.0) for cond in conditions]
                        fig_trend.add_trace(go.Scatter(
                            x=x_axis_data,
                            y=y_values,
                            mode='lines+markers',
                            name=species,
                            line=dict(width=2),
                            marker=dict(size=6)
                        ))
                    
                    # 应用主题配置
                    theme_config = get_plotly_theme()
                    fig_trend.update_layout(
                        title=f"重要性系数变化趋势 (目标组分: {', '.join(target_species)})",
                        xaxis_title=x_axis_title,
                        yaxis_title="重要性系数",
                        height=500,
                        hovermode='x unified',
                        **theme_config['layout']
                    )
                    
                    st.plotly_chart(fig_trend, use_container_width=True)
                    
                    # 在趋势图下方显示数据表
                    if summary_data:
                        st.subheader("📋 重要性系数数据表")
                        summary_df = pd.DataFrame(summary_data)
                        
                        # 设置数据表格式
                        def color_importance(val):
                            if val >= 0.8:
                                return 'background-color: #d73027; color: white'
                            elif val >= 0.5:
                                return 'background-color: #fc8d59; color: white'
                            elif val >= 0.2:
                                return 'background-color: #fee08b'
                            else:
                                return 'background-color: #e0f3f8'
                        
                        styled_df = summary_df.style.applymap(color_importance, subset=[col for col in summary_df.columns if col != '组分'])
                        st.dataframe(styled_df, use_container_width=True)
            else:
                st.info("需要多个工况条件才能显示趋势图")
                
                # 即使没有趋势图，也显示数据表
                if summary_data:
                    st.subheader("📋 重要性系数数据表")
                    summary_df = pd.DataFrame(summary_data)
                    
                    # 设置数据表格式
                    def color_importance(val):
                        if val >= 0.8:
                            return 'background-color: #d73027; color: white'
                        elif val >= 0.5:
                            return 'background-color: #fc8d59; color: white'
                        elif val >= 0.2:
                            return 'background-color: #fee08b'
                        else:
                            return 'background-color: #e0f3f8'
                    
                    styled_df = summary_df.style.applymap(color_importance, subset=[col for col in summary_df.columns if col != '组分'])
                    st.dataframe(styled_df, use_container_width=True)
                else:
                    st.warning(f"没有找到重要性系数大于 {importance_threshold} 的组分")
        
        with tab2:
            st.subheader("🔍 单一工况详情")
            
            # 选择工况
            selected_condition = st.selectbox(
                "选择工况条件：",
                options=conditions,
                key="condition_detail"
            )
            
            if selected_condition:
                condition_coeffs = importance_coeffs_dict[selected_condition]
                
                # 过滤和排序
                filtered_coeffs = {sp: coeff for sp, coeff in condition_coeffs.items() 
                                 if coeff >= importance_threshold}
                sorted_coeffs = dict(sorted(filtered_coeffs.items(), key=lambda x: x[1], reverse=True))
                sorted_coeffs = dict(list(sorted_coeffs.items())[:max_species_display])
                
                if sorted_coeffs:
                    # 创建柱状图
                    fig_bar = px.bar(
                        x=list(sorted_coeffs.values()),
                        y=list(sorted_coeffs.keys()),
                        orientation='h',
                        title=f"工况 {selected_condition} 的重要性系数分布",
                        labels={'x': '重要性系数', 'y': '组分'}
                    )
                    
                    # 应用主题配置
                    theme_config = get_plotly_theme()
                    fig_bar.update_layout(
                        height=max(400, len(sorted_coeffs) * 20),
                        **theme_config['layout']
                    )
                    st.plotly_chart(fig_bar, use_container_width=True)
                    
                    # 显示详细数据
                    detail_df = pd.DataFrame(list(sorted_coeffs.items()), columns=['组分', '重要性系数'])
                    detail_df['排名'] = range(1, len(detail_df) + 1)
                    detail_df = detail_df[['排名', '组分', '重要性系数']]
                    
                    st.dataframe(detail_df, use_container_width=True)
                else:
                    st.warning(f"工况 {selected_condition} 中没有重要性系数大于 {importance_threshold} 的组分")
        
        with tab3:
            st.subheader("📋 数据导出")
            
            # 准备导出数据
            export_data = []
            for cond in conditions:
                for species in species_names:
                    coeff = importance_coeffs_dict[cond].get(species, 0.0)
                    if coeff >= importance_threshold:
                        export_data.append({
                            '工况条件': cond,
                            '组分': species,
                            '重要性系数': coeff,
                            '目标组分': ', '.join(target_species),
                            '分析类型': analysis_type
                        })
            
            if export_data:
                export_df = pd.DataFrame(export_data)
                
                # 显示导出数据预览
                st.write("📋 导出数据预览：")
                st.dataframe(export_df.head(20), use_container_width=True)
                
                # 提供下载按钮
                csv_data = export_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="📥 下载完整数据 (CSV)",
                    data=csv_data,
                    file_name=f"DRGEP_importance_coefficients_{'-'.join(target_species)}_{analysis_type}.csv",
                    mime="text/csv"
                )
                
                # 显示统计信息
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("总记录数", len(export_data))
                with col2:
                    st.metric("涉及组分数", len(set(export_data[i]['组分'] for i in range(len(export_data)))))
                with col3:
                    st.metric("工况条件数", len(conditions))
            else:
                st.warning("没有满足阈值条件的数据可供导出")
    
    except Exception as e:
        st.markdown(f"""
        <div class="error-card">
            <h3 style="color: var(--warning-color); margin: 0;">❌ 分析过程中出现错误</h3>
            <p style="margin: 0.5rem 0 0 0;">
                错误信息: {str(e)}
            </p>
        </div>
        """, unsafe_allow_html=True)
        st.error(f"详细错误信息：{str(e)}")

else:
    st.markdown("""
    <div class="parameter-card">
        <h4 style="color: var(--info-color); margin: 0;">ℹ️ 使用说明</h4>
        <p style="margin: 0.5rem 0 0 0; color: var(--text-color);">
            1. 选择分析类型（积分ROP或终点ROP）<br>
            2. 选择一个目标组分进行DRGEP分析<br>
            3. 设置重要性系数阈值来过滤结果<br>
            4. 查看不同工况条件下的组分重要性系数和变化趋势
        </p>
    </div>
    """, unsafe_allow_html=True)
