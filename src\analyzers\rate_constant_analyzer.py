"""
反应速率常数分析模块

负责处理Arrhenius参数计算、反应速率常数处理和可视化
从solution0606.py重构而来的功能模块
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import math
import re
from typing import Dict, Any, Optional, Union


class RateConstantAnalyzer:
    """反应速率常数分析器"""
    
    @staticmethod
    def calculate_forward_rate(
        reaction_equation: str, 
        arr_param: Dict[str, float], 
        t_min: float = 300, 
        t_max: float = 2000
    ) -> pd.DataFrame:
        """
        计算正反应速率常数
        
        Args:
            reaction_equation: 反应方程式
            arr_param: Arrhenius参数字典，包含'A', 'b', 'Ea'
            t_min: 最低温度 (K)
            t_max: 最高温度 (K)
            
        Returns:
            包含温度和速率常数的DataFrame
        """
        temp_range = np.arange(t_min, t_max, 10)
        
        def rate_constant(A: float, n: float, Ea: float, temp_range: np.ndarray) -> list:
            """根据Arrhenius方程计算速率常数"""
            temps = temp_range
            rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
            return rc
        
        rc = rate_constant(arr_param['A'], arr_param['b'], arr_param['Ea'], temp_range)
        fc = pd.DataFrame(index=temp_range, columns=[reaction_equation], data=rc)
        return fc
    
    @staticmethod 
    def process_rate_constants(
        input_string: Optional[str] = None, 
        t_min: float = 300, 
        t_max: float = 2000
    ) -> pd.DataFrame:
        """
        处理多个反应速率常数输入
        
        Args:
            input_string: 包含多个反应速率参数的字符串
            t_min: 最低温度 (K) 
            t_max: 最高温度 (K)
            
        Returns:
            包含温度和各反应速率常数的DataFrame
        """
        if input_string is None:
            return pd.DataFrame()
            
        temp_range = np.arange(t_min, t_max, 10)
        reactions = input_string
        lines = reactions.split('\n')
        
        def rate_constant(A: float, n: float, Ea: float, temp_range: np.ndarray) -> list:
            """根据Arrhenius方程计算速率常数"""
            temps = temp_range
            rc = [A * temp ** (n) * math.exp(-Ea / (1.9872036 * temp)) for temp in temps]
            return rc
        
        data = pd.DataFrame(index=temp_range)
        
        for line in lines:
            if not line.strip():
                continue
                
            # 移除注释
            if '!' in line:
                line = line[:line.rfind('!')].strip()
            else:
                line = line.strip()
                
            try:
                if 'PLOG' in line:
                    # 处理压力相关反应
                    parts = line.split('/')
                    if len(parts) > 1:
                        params = parts[1].split()
                        reaction = f'P={params[0]}'
                        A = float(params[1])
                        n = float(params[2])
                        Ea = float(params[3])
                elif '=' in line:
                    # 包含反应方程式的情况
                    parts = re.split(r"\s+", line)
                    reaction = parts[0]
                    A = float(parts[1])
                    n = float(parts[2])
                    Ea = float(parts[3])
                else:
                    # 只有参数的情况
                    parts = re.split(r"\s+", line)
                    A = float(parts[0])
                    n = float(parts[1])
                    Ea = float(parts[2])
                    reaction = f'A={A:.2e}  n={n:.2f}  Ea={Ea:.2e}'
                
                rc = rate_constant(A, n, Ea, temp_range)
                
                if reaction in data.columns:
                    data.loc[:, reaction] = data.loc[:, reaction] + rc
                else:
                    data.loc[:, reaction] = rc
                    
            except (ValueError, IndexError) as e:
                # 忽略格式错误的行
                continue
                
        return data
    
    @staticmethod
    def create_rate_constant_plot(
        dataframe: pd.DataFrame, 
        show_plot: bool = False
    ) -> go.Figure:
        """
        创建反应速率常数对比图
        
        Args:
            dataframe: 包含温度和速率常数数据的DataFrame
            show_plot: 是否显示图表
            
        Returns:
            Plotly图表对象
        """
        fig = go.Figure()
        
        for col in dataframe.columns:
            fig.add_trace(
                go.Scatter(
                    x=dataframe.index.astype(float),
                    y=dataframe[col],
                    mode='lines',
                    name=col,
                    line=dict(width=2)
                )
            )
        
        # 设置图形布局
        fig.update_layout(
            title='反应速率常数对比',
            xaxis_title='温度 / K',
            yaxis=dict(
                title='速率常数 / cm³ mol⁻¹ s⁻¹',
                showgrid=True,
                rangemode='tozero',
                tickformat='.1e',
                type='log'  # Y轴设置为对数刻度
            ),
            legend=dict(
                x=0.5,
                y=-0.2,
                xanchor='center',
                yanchor='top',
                orientation='h',
                font=dict(size=14)
            ),
            margin=dict(r=20),
            showlegend=True
        )
        
        # 添加网格
        fig.update_xaxes(showgrid=True)
        fig.update_yaxes(showgrid=True)
        
        if show_plot:
            fig.show()
            
        return fig


class ArrheniusParameterProcessor:
    """Arrhenius参数处理器"""
    
    @staticmethod
    def extract_parameters_from_string(input_string: str) -> list:
        """
        从字符串中提取Arrhenius参数
        
        Args:
            input_string: 包含反应速率参数的字符串
            
        Returns:
            参数列表，每个元素是字典包含反应名称和参数
        """
        parameters = []
        lines = input_string.split('\n')
        
        for line in lines:
            if not line.strip():
                continue
                
            # 移除注释
            if '!' in line:
                clean_line = line[:line.rfind('!')].strip()
                comment = line[line.rfind('!'):].strip()
            else:
                clean_line = line.strip()
                comment = ''
                
            try:
                if 'PLOG' in clean_line:
                    # 处理压力相关反应
                    parts = clean_line.split('/')
                    if len(parts) > 1:
                        params = parts[1].split()
                        param_dict = {
                            'reaction': f'P={params[0]}',
                            'A': float(params[1]),
                            'n': float(params[2]),
                            'Ea': float(params[3]),
                            'comment': comment,
                            'type': 'PLOG'
                        }
                        parameters.append(param_dict)
                elif '=' in clean_line:
                    # 包含反应方程式的情况
                    parts = re.split(r"\s+", clean_line)
                    param_dict = {
                        'reaction': parts[0],
                        'A': float(parts[1]),
                        'n': float(parts[2]),
                        'Ea': float(parts[3]),
                        'comment': comment,
                        'type': 'reaction'
                    }
                    parameters.append(param_dict)
                else:
                    # 只有参数的情况
                    parts = re.split(r"\s+", clean_line)
                    A, n, Ea = float(parts[0]), float(parts[1]), float(parts[2])
                    param_dict = {
                        'reaction': f'A={A:.2e}  n={n:.2f}  Ea={Ea:.2e}',
                        'A': A,
                        'n': n,
                        'Ea': Ea,
                        'comment': comment,
                        'type': 'parameters_only'
                    }
                    parameters.append(param_dict)
                    
            except (ValueError, IndexError):
                # 忽略格式错误的行
                continue
                
        return parameters
    
    @staticmethod
    def validate_parameters(parameters: Dict[str, float]) -> bool:
        """
        验证Arrhenius参数的有效性
        
        Args:
            parameters: 包含A, n, Ea的参数字典
            
        Returns:
            参数是否有效
        """
        required_keys = ['A', 'n', 'Ea']
        
        # 检查必要的键是否存在
        if not all(key in parameters for key in required_keys):
            return False
            
        # 检查数值的合理性
        if parameters['A'] <= 0:
            return False
            
        if not (-5 <= parameters['n'] <= 5):  # n值通常在这个范围内
            return False
            
        if parameters['Ea'] < 0:  # 活化能不应为负值
            return False
            
        return True


# 为了向后兼容，保留原函数名
def forward_rate(reaction_equation: str, arr_param: Dict[str, float], 
                t_min: float = 300, t_max: float = 2000) -> pd.DataFrame:
    """向后兼容函数"""
    return RateConstantAnalyzer.calculate_forward_rate(
        reaction_equation, arr_param, t_min, t_max
    )


def process_rc(string: Optional[str] = None, t_min: float = 300, 
              t_max: float = 2000) -> pd.DataFrame:
    """向后兼容函数"""
    return RateConstantAnalyzer.process_rate_constants(string, t_min, t_max)


def plot_rc(dataframe: pd.DataFrame, plot: bool = False) -> go.Figure:
    """向后兼容函数"""
    return RateConstantAnalyzer.create_rate_constant_plot(dataframe, plot) 