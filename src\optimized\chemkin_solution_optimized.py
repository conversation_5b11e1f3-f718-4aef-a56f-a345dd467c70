"""
优化版本的ChemkinSolution类，专门针对combine_sheets方法进行性能优化
"""
import pandas as pd
import numpy as np
import copy
from tqdm import tqdm
import streamlit as st
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from .chemkin_solution import ChemkinSolution

class ChemkinSolutionOptimized(ChemkinSolution):
    """优化版本的ChemkinSolution类"""
    
    def combine_sheets_optimized(self, gas_out_file, progress_bar=False, mole_only=False):
        """
        优化版本的combine_sheets方法 - 支持两种场景

        Scenario A: 包含end_point_sheets的文件
        Scenario B: 不包含end_point_sheets的文件（需要从solution sheets提取端点数据）

        主要优化点：
        1. 批量读取Excel sheets，减少I/O操作
        2. 并行处理数据
        3. 向量化计算
        4. 内存优化
        """
        if progress_bar:
            progress_bar1 = st.progress(0, text='正在合并Sheets')
            progress_bar2 = st.progress(0, text='正在积分ROP值')
            progress_bar3 = st.progress(0, text='正在处理敏感性数据')
        else:
            progress_bar1 = None
            progress_bar2 = None
            progress_bar3 = None

        print("正在执行优化版本的combine_sheets...")

        # 关键修复：处理Scenario B（没有end_point_sheets的情况）
        if hasattr(self, 'need_extract_endpoints') and self.need_extract_endpoints:
            print("🔄 检测到需要从solution sheets提取端点数据 (Scenario B)")

            # 创建端点提取的进度回调
            def endpoint_progress_callback(step, total, message):
                if progress_bar1:
                    progress = int((step / total) * 100)
                    progress_bar1.progress(progress / 100, text=f"🔄 {message}")

            self._extract_endpoints_from_solution_optimized(gas_out_file, endpoint_progress_callback)

        # 优化1: 批量预读取所有需要的sheets
        print("优化1: 批量预读取Excel数据...")
        all_sheets_data = self._batch_read_sheets(progress_bar1)

        # 存储批量数据供端点提取使用（如果需要）
        self._current_batch_data = all_sheets_data

        # 优化2: 并行处理不同类型的数据
        print("优化2: 并行处理数据...")

        # 处理摩尔分数数据（支持两种场景）
        if self.mole_fraction_exist:
            if len(self.end_point_sheets) > 0:
                # Scenario A: 使用end_point_sheets
                print("📊 处理Scenario A: 使用end_point_sheets")
                self._process_mole_fractions_optimized(all_sheets_data, gas_out_file)
            else:
                # Scenario B: 使用从solution sheets提取的数据
                print("📊 处理Scenario B: 使用从solution sheets提取的数据")
                # 数据已在_extract_endpoints_from_solution_optimized中处理
                pass

        # 处理Solution数据（用于敏感性分析）
        if len(self.soln_sheets) > 0:
            # 为Solution数据处理创建专用进度条
            solution_progress_bar = progress_bar1 if progress_bar else None
            self._process_solution_data_optimized(all_sheets_data, gas_out_file, solution_progress_bar)

        # 处理敏感性数据
        if self.sensitivity_exist and len(self.sensitivity_sheets) > 0:
            self._process_sensitivity_data_optimized(all_sheets_data, gas_out_file, progress_bar3)

        # 优化3: 高效处理反应速率数据
        if self.net_reaction_rate_exist:
            print("优化3: 高效处理反应速率数据...")
            self._process_reaction_rates_optimized(all_sheets_data, gas_out_file, progress_bar1, progress_bar2)
        elif self.ROP_exist:
            print("优化3: 处理ROP数据...")
            self._process_rop_data_optimized(all_sheets_data, gas_out_file, progress_bar2)

        # 清理批量数据
        if hasattr(self, '_current_batch_data'):
            delattr(self, '_current_batch_data')

        # 数据完整性验证
        print("\n📊 数据完整性验证:")
        self._validate_data_integrity(gas_out_file)

        print(f"\n✅ 优化版本数据处理完成!")

    def combine_sheets(self, gas_out_file, progress_bar=False, mole_only=False):
        """
        重写基类的combine_sheets方法，调用优化版本
        确保兼容性：当调用combine_sheets时，实际执行combine_sheets_optimized
        """
        return self.combine_sheets_optimized(gas_out_file, progress_bar, mole_only)
    
    def _batch_read_sheets(self, progress_bar=None):
        """批量读取所有需要的sheets"""
        all_sheets_data = {}
        
        # 收集所有需要读取的sheet名称
        sheets_to_read = (
            self.soln_sheets + 
            self.end_point_sheets + 
            self.rop_sheets + 
            self.sensitivity_sheets +
            getattr(self, 'max_sheets', [])
        )
        
        print(f"开始批量读取 {len(sheets_to_read)} 个工作表...")
        
        # 使用线程池并行读取sheets
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_sheet = {
                executor.submit(self._read_single_sheet, sheet_name): sheet_name 
                for sheet_name in sheets_to_read
            }
            
            completed = 0
            for future in as_completed(future_to_sheet):
                sheet_name = future_to_sheet[future]
                try:
                    sheet_data = future.result()
                    all_sheets_data[sheet_name] = sheet_data
                    completed += 1
                    
                    # 控制台进度显示
                    progress_percent = (completed / len(sheets_to_read)) * 100
                    print(f"  读取进度: {completed}/{len(sheets_to_read)} ({progress_percent:.1f}%) - {sheet_name}")
                    
                    # Streamlit进度显示
                    if progress_bar:
                        progress = completed / len(sheets_to_read)
                        progress_bar.progress(progress, f"批量读取工作表: {completed}/{len(sheets_to_read)} ({progress_percent:.1f}%)")
                        
                except Exception as e:
                    print(f"❌ 读取sheet {sheet_name} 失败: {e}")
        
        print(f"✅ 批量读取完成，成功读取 {len(all_sheets_data)} 个工作表")
        return all_sheets_data
    
    def _read_single_sheet(self, sheet_name):
        """读取单个sheet的数据"""
        return self.xlsx.parse(sheet_name, index_col=0)
    
    def _process_mole_fractions_optimized(self, all_sheets_data, gas_out_file):
        """优化的摩尔分数数据处理 - 修复数据结构一致性"""
        print("处理摩尔分数数据...")

        # 并行处理end_point sheets
        mole_fraction_dfs = []
        for sheet in self.end_point_sheets:
            if sheet in all_sheets_data:
                sheet_data = all_sheets_data[sheet]
                mole_cols = sheet_data.columns[sheet_data.columns.str.contains(' Mole_fraction_')]
                mole_fraction_dfs.append(sheet_data[mole_cols])

        if mole_fraction_dfs:
            # 一次性合并所有数据
            combined_mole_fractions = pd.concat(mole_fraction_dfs, axis=1)
            gas_out_file.mole_fractions = combined_mole_fractions.copy()
            gas_out_file.mole_fractions.columns = (
                gas_out_file.mole_fractions.columns
                .str.replace(' Mole_fraction_', '')
                .str.replace('_end_point_()', '', regex=False)
            )

            # 关键修复：设置正确的index以保持兼容性
            gas_out_file.mole_fractions.index = self.variables

            # 关键修复：从mole_fractions计算mole_fractions_max，而不是使用max_sheets
            gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
            gas_out_file.mole_fractions_max.index = ['max_values']  # 单行，表示最大值

            # 计算possible_reactants
            gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
                8, 'max_values').index.tolist()

            print(f"✅ 摩尔分数数据处理完成: {gas_out_file.mole_fractions.shape}")
            print(f"✅ mole_fractions_max计算完成: {gas_out_file.mole_fractions_max.shape}")
            print(f"✅ possible_reactants: {len(gas_out_file.possible_reactants)} 个组分")
    
    def _process_solution_data_optimized(self, all_sheets_data, gas_out_file, progress_bar=None):
        """优化的Solution数据处理 - 兼容原始格式"""
        print("🔄 开始处理Solution数据...")

        # 按工况点分组处理solution数据
        soln_full_collect = {}  # 完整数据（用于敏感性分析等）
        soln_net_collect = {}   # 仅Net列数据（兼容原始格式）
        condition_groups = self._group_sheets_by_condition(self.soln_sheets)
        total_conditions = len(self.variables)

        for condition_idx, (variable, sheet_names) in enumerate(zip(self.variables, condition_groups)):
            condition_data_list = []
            condition_net_data_list = []

            for sheet_name in sheet_names:
                if sheet_name in all_sheets_data:
                    sheet_data = all_sheets_data[sheet_name]
                    condition_data_list.append(sheet_data)

                    # 提取仅Net列的数据（兼容原始格式）
                    net_cols = sheet_data.columns[sheet_data.columns.str.contains('Net')]
                    condition_net_data_list.append(sheet_data[net_cols])

            if condition_data_list:
                # 完整数据（用于敏感性分析等）
                soln_full_collect[variable] = pd.concat(condition_data_list, axis=1, sort=False)

                # 仅Net列数据（兼容原始格式）
                soln_net_collect[variable] = pd.concat(condition_net_data_list, axis=1, sort=False)

                # 控制台进度显示
                progress_percent = ((condition_idx + 1) / total_conditions) * 100
                print(f"  处理工况 {condition_idx + 1}/{total_conditions} ({progress_percent:.1f}%): {variable} - {len(sheet_names)} 个sheets")

                # Streamlit进度显示
                if progress_bar:
                    progress = (condition_idx + 1) / total_conditions
                    progress_bar.progress(progress, f"处理Solution数据: {condition_idx + 1}/{total_conditions} 工况 ({progress_percent:.1f}%)")

        # 兼容原始格式：soln_collect只包含Net反应速率列
        gas_out_file.soln_collect = soln_net_collect

        # 完整数据存储在新属性中（用于敏感性分析等高级功能）
        gas_out_file.soln_full_collect = soln_full_collect

        print(f'✅ Solution数据收集完毕! 共处理 {len(soln_net_collect)} 个工况点')
        print(f'   - soln_collect: 仅Net列（兼容原始格式）')
        print(f'   - soln_full_collect: 完整数据（用于高级功能）')
    
    def _process_sensitivity_data_optimized(self, all_sheets_data, gas_out_file, progress_bar=None):
        """优化的敏感性数据处理"""
        print("处理敏感性数据...")
        
        sen_collect = {}
        condition_groups = self._group_sheets_by_condition(self.sensitivity_sheets)
        
        for condition_idx, (variable, sheet_names) in enumerate(zip(self.variables, condition_groups)):
            condition_sens_data = []
            for sheet_name in sheet_names:
                if sheet_name in all_sheets_data:
                    sheet_data = all_sheets_data[sheet_name]
                    # 只保留敏感性列
                    sens_cols = sheet_data.columns[sheet_data.columns.str.contains('_Sens_')]
                    condition_sens_data.append(sheet_data[sens_cols])
            
            if condition_sens_data:
                sen_collect[variable] = pd.concat(condition_sens_data, axis=1)
            
            if progress_bar:
                progress = (condition_idx + 1) / len(self.variables)
                progress_bar.progress(progress, f"处理敏感性数据: {condition_idx + 1}/{len(self.variables)}")
        
        gas_out_file.sensitivity_collect = sen_collect
        
        # 提取敏感性分析的目标组分
        if sen_collect:
            first_sheet = list(sen_collect.values())[0]
            sensitivity_species = set()
            for col in first_sheet.columns:
                if '_Sens_' in col:
                    species = col.strip().split('_Sens_')[0].strip()
                    sensitivity_species.add(species)
            gas_out_file.sensitivity_species_output = list(sensitivity_species)
        else:
            gas_out_file.sensitivity_species_output = []
        
        print('敏感性数据处理完毕!')
    
    def _process_reaction_rates_optimized(self, all_sheets_data, gas_out_file, progress_bar1=None, progress_bar2=None):
        """优化的反应速率数据处理 - 使用soln_collect中的Net数据"""
        print("处理反应速率数据...")

        # 使用已经处理好的soln_collect数据（只包含Net列）
        if not hasattr(gas_out_file, 'soln_collect') or not gas_out_file.soln_collect:
            print("❌ 未找到soln_collect数据，无法计算ROP")
            return

        net_rate_collect = gas_out_file.soln_collect  # 直接使用已处理的Net数据

        print('反应速率数据准备完毕!')
        
        # 检查反应速率完整性
        stoichi = copy.deepcopy(gas_out_file.stoichimetric)
        if net_rate_collect:
            first_sheet = list(net_rate_collect.values())[0]
            net_rate_columns = [col for col in first_sheet.columns if 'Net' in col]
            
            if len(net_rate_columns) != len(gas_out_file.reaction_index):
                print("警告：反应速率输出不完全")
                # 处理缺失的反应
                exist_reaction_index = []
                for col in net_rate_columns:
                    exist_reaction_index.append(int(col.split('#')[1].split('_')[0]))
                
                all_reactions = set(range(1, len(gas_out_file.reaction_index) + 1))
                missing_reactions = list(all_reactions - set(exist_reaction_index))
                
                for r in missing_reactions:
                    print(f'缺失反应: {gas_out_file.stoichimetric.index[r - 1]}')
                    stoichi.drop(gas_out_file.stoichimetric.index[r - 1], inplace=True)
        
        # 优化的ROP计算 - 使用专门的净反应速率数据
        print('开始向量化ROP计算...')
        self._calculate_rop_vectorized(net_rate_collect, gas_out_file, stoichi, progress_bar2)
    
    def _calculate_rop_vectorized(self, net_rate_collect, gas_out_file, stoichi, progress_bar=None):
        """ROP计算 - 兼容原始格式"""
        int_rate = {}
        int_rop = {}
        end_rate = {}
        end_rop = {}

        print('正在积分ROP值')

        for i, temp in enumerate(net_rate_collect.keys()):
            rate_data = net_rate_collect[temp]

            # 使用与原始版本相同的积分方法
            int_rate[temp] = [np.trapz(rate_data.loc[:, j], rate_data.index) for j in rate_data.columns]
            end_rate[temp] = rate_data.iloc[-1, :].tolist()

            # 使用与原始版本相同的ROP计算方法
            int_rop[temp] = stoichi.apply(lambda x: np.multiply(x, int_rate[temp]), axis=0)
            end_rop[temp] = stoichi.apply(lambda x: np.multiply(x, end_rate[temp]), axis=0)

            if progress_bar:
                progress = (i + 1) / len(net_rate_collect)
                progress_bar.progress(progress, f"正在积分ROP 工况点：{i + 1}/{len(net_rate_collect)}")

        # 存储结果（与原始格式完全一致）
        gas_out_file.integral_ROP = int_rop
        gas_out_file.end_ROP = end_rop
        gas_out_file.rop_species_output = gas_out_file.species

        print('处理完毕!')
    
    def _group_sheets_by_condition(self, sheet_names):
        """按工况点分组sheets - 使用与原始版本相同的逻辑"""
        # 使用字典来收集每个条件号的sheets
        condition_dict = {}

        for sheet_name in sheet_names:
            if '#' in sheet_name:
                condition_no = sheet_name.split('#')[-1].split('_')[0]
            else:
                condition_no = '1'  # 默认为第一个工况

            if condition_no not in condition_dict:
                condition_dict[condition_no] = []
            condition_dict[condition_no].append(sheet_name)

        # 按照条件号的顺序返回分组
        condition_groups = []
        for condition_no in sorted(condition_dict.keys(), key=int):
            condition_groups.append(condition_dict[condition_no])

        return condition_groups
    
    def _process_rop_data_optimized(self, all_sheets_data, gas_out_file, progress_bar=None):
        """优化的ROP数据处理（当没有Net反应速率时）"""
        print("处理ROP数据...")

        rop_collect = {}
        condition_groups = self._group_sheets_by_condition(self.rop_sheets)

        for condition_idx, (variable, sheet_names) in enumerate(zip(self.variables, condition_groups)):
            condition_rop_data = []
            for sheet_name in sheet_names:
                if sheet_name in all_sheets_data:
                    sheet_data = all_sheets_data[sheet_name]
                    # 排除Total列
                    rop_cols = sheet_data.columns[~sheet_data.columns.str.contains('Total')]
                    condition_rop_data.append(sheet_data[rop_cols])

            if condition_rop_data:
                rop_collect[variable] = pd.concat(condition_rop_data, axis=1)

            if progress_bar:
                progress = (condition_idx + 1) / len(self.variables)
                progress_bar.progress(progress, f"处理ROP数据: {condition_idx + 1}/{len(self.variables)}")

        if not rop_collect:
            print("警告：没有找到ROP数据")
            return

        print('开始整理ROP数据...')

        # 整理ROP数据 - 与原始实现保持一致
        int_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)
        end_rop_raw2 = pd.DataFrame(index=self.variables, columns=rop_collect[self.variables[0]].columns)

        # 关键修复：设置rop_species_output
        gas_out_file.rop_species_output = [
            a for a in set([s.strip().split('_')[0] for s in rop_collect[self.variables[0]].columns])
        ]
        print(f"检测到ROP组分: {gas_out_file.rop_species_output}")

        # 积分ROP数据
        for temp in rop_collect:
            rop = rop_collect[temp]
            int_rop_raw2.loc[temp, :] = [np.trapz(rop.loc[:, j], rop.index) for j in rop.columns]
            end_rop_raw2.loc[temp, :] = rop.iloc[-1, :].tolist()

        # 按组分整理ROP数据
        int_rop2 = {}
        end_rop2 = {}

        for species in gas_out_file.rop_species_output:
            int_rop2[species] = int_rop_raw2.loc[:, int_rop_raw2.columns.str.contains(f' {species}_ROP', regex=False)]
            end_rop2[species] = end_rop_raw2.loc[:, end_rop_raw2.columns.str.contains(f' {species}_ROP', regex=False)]

            # 更新列名
            if len(int_rop2[species].columns) > 0:
                reaction_rop = [
                    gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] + "#" + a[1].split('_')[0]
                    for a in int_rop2[species].columns.str.split('#')
                ]
                reaction_rop_end = [
                    gas_out_file.reaction_index.loc[int(a[1].split('_')[0])].values[0] + "#" + a[1].split('_')[0]
                    for a in end_rop2[species].columns.str.split('#')
                ]

                int_rop2[species].columns = reaction_rop
                end_rop2[species].columns = reaction_rop_end

        # 存储结果
        gas_out_file.integral_ROP = int_rop2
        gas_out_file.end_ROP = end_rop2

        print('ROP数据处理完毕!')
    
    def _extract_endpoints_from_solution_optimized(self, gas_out_file, progress_callback=None):
        """
        优化版本的端点数据提取方法 - 处理Scenario B (无end_point_sheets的情况)
        这是处理没有end_point_sheets文件的关键方法
        """
        if progress_callback:
            progress_callback(0, 100, "开始从solution sheets提取端点数据")
        print("开始从solution sheets提取端点数据...")

        # 使用批量读取的数据（如果可用），否则单独读取
        if hasattr(self, '_current_batch_data') and self._current_batch_data:
            all_sheets_data = self._current_batch_data
        else:
            # 回退到单独读取
            all_sheets_data = {}
            for sheet in self.soln_sheets:
                all_sheets_data[sheet] = self.xlsx.parse(sheet, index_col=0)

        # 按工况分组sheets
        condition_sheets = {}
        for sheet in self.soln_sheets:
            if '#' in sheet:
                condition_id = sheet.split('#')[-1].split('_')[0]
            else:
                condition_id = '1'

            if condition_id not in condition_sheets:
                condition_sheets[condition_id] = []
            condition_sheets[condition_id].append(sheet)

        if progress_callback:
            progress_callback(10, 100, f"发现 {len(condition_sheets)} 个工况组")
        print(f"发现 {len(condition_sheets)} 个工况组")

        # 处理每个工况
        first_point_data = {}
        last_point_data = {}

        for i, (condition_id, sheets) in enumerate(condition_sheets.items()):
            if progress_callback:
                progress = 10 + (i / len(condition_sheets)) * 60
                progress_callback(progress, 100, f"处理工况 {condition_id}")
            print(f"处理工况 {condition_id}...")

            # 合并该工况的所有数据
            condition_data = []
            for sheet in sheets:
                if sheet in all_sheets_data:
                    condition_data.append(all_sheets_data[sheet])

            if condition_data:
                merged_data = pd.concat(condition_data, axis=1) if len(condition_data) > 1 else condition_data[0]

                # 提取端点数据
                first_point_data[condition_id] = merged_data.iloc[0]  # 第一行
                last_point_data[condition_id] = merged_data.iloc[-1]  # 最后一行

                if progress_callback:
                    progress_callback(progress + 5, 100, f"工况 {condition_id}: {len(merged_data)} 个时间点，{len(merged_data.columns)} 个变量")
                print(f"  工况 {condition_id}: {len(merged_data)} 个时间点，{len(merged_data.columns)} 个变量")

        # 转换为DataFrame格式（类似end_point sheets的结构）
        if last_point_data:
            # 创建last_point的DataFrame
            last_df_list = []
            for condition_id, data in last_point_data.items():
                last_row = pd.DataFrame([data.values], columns=data.index, index=[condition_id])
                last_df_list.append(last_row)

            last_points_df = pd.concat(last_df_list, axis=0)

            # 关键修复：确保索引按数值排序而不是字符串排序
            # 将字符串索引转换为整数进行排序
            last_points_df.index = last_points_df.index.astype(int)
            last_points_df = last_points_df.sort_index()

            gas_out_file.last_points = last_points_df

            if progress_callback:
                progress_callback(70, 100, f"数据整理完成: 最后一点数据 {last_points_df.shape}")
            print(f"提取完成: 最后一点数据: {last_points_df.shape}")

        # 关键修复：处理first_point数据
        if first_point_data:
            # 创建first_point的DataFrame
            first_df_list = []
            for condition_id, data in first_point_data.items():
                first_row = pd.DataFrame([data.values], columns=data.index, index=[condition_id])
                first_df_list.append(first_row)

            first_points_df = pd.concat(first_df_list, axis=0)

            # 关键修复：确保索引按数值排序而不是字符串排序
            # 将字符串索引转换为整数进行排序
            first_points_df.index = first_points_df.index.astype(int)
            first_points_df = first_points_df.sort_index()

            gas_out_file.first_points = first_points_df

            if progress_callback:
                progress_callback(75, 100, f"数据整理完成: 第一点数据 {first_points_df.shape}")
            print(f"提取完成: 第一点数据: {first_points_df.shape}")

            # 处理摩尔分数数据（如果存在）
            mole_cols = [col for col in last_points_df.columns if 'Mole_fraction_' in col]

            if mole_cols:
                # 重新组织摩尔分数数据
                mole_data = {}
                for condition_id in last_points_df.index:
                    condition_mole_cols = [col for col in mole_cols if f'_Run#{condition_id}_' in col]
                    for col in condition_mole_cols:
                        species = col.split('Mole_fraction_')[1].split(f'_Run#{condition_id}_')[0]
                        if species not in mole_data:
                            mole_data[species] = {}
                        mole_data[species][condition_id] = last_points_df.loc[condition_id, col]

                # 转换为DataFrame
                if mole_data:
                    mole_fractions = pd.DataFrame(mole_data)

                    # 关键修复：确保数据结构与Scenario A一致 (conditions × species)
                    # mole_data的结构是 {species: {condition: value}}
                    # DataFrame(mole_data)会创建 (conditions × species) 的结构，这正是我们需要的

                    # 设置变量索引（关键修复）
                    try:
                        # 确保索引与self.variables匹配
                        if len(self.variables) == len(mole_fractions.index):
                            mole_fractions.index = self.variables
                    except Exception as e:
                        print(f"  警告：设置索引失败: {e}")

                    gas_out_file.mole_fractions = mole_fractions

                    # 关键修复：从mole_fractions计算mole_fractions_max
                    gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
                    gas_out_file.mole_fractions_max.index = ['max_values']

                    # 计算possible_reactants
                    gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
                        8, 'max_values').index.tolist()

                    if progress_callback:
                        progress_callback(80, 100, f"摩尔分数数据处理完成: {len(mole_fractions.index)} 个组分")
                    print(f"  摩尔分数数据: {len(mole_fractions.index)} 个组分")
                else:
                    print(f"  摩尔分数数据: 0 个组分（数据解析失败）")

            # 设置数据可用性标志
            self.mole_fraction_exist = len(mole_cols) > 0
            rate_cols = [col for col in last_points_df.columns if 'Net_rxn_rate_' in col]
            self.net_reaction_rate_exist = len(rate_cols) > 0

            # 确保mole_fractions_max被设置（关键修复）
            if hasattr(gas_out_file, 'mole_fractions') and gas_out_file.mole_fractions is not None:
                if not hasattr(gas_out_file, 'mole_fractions_max') or gas_out_file.mole_fractions_max is None:
                    print("  修复：计算mole_fractions_max")
                    gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
                    gas_out_file.mole_fractions_max.index = ['max_values']

                    # 重新计算possible_reactants
                    gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
                        8, 'max_values').index.tolist()
                    print(f"  修复：mole_fractions_max设置完成: {gas_out_file.mole_fractions_max.shape}")

            if progress_callback:
                progress_callback(100, 100, "端点数据提取完成!")
            print("端点数据提取完成!")
        else:
            print("警告：未能提取到端点数据")

    def _validate_data_integrity(self, gas_out_file):
        """验证处理后的数据完整性"""
        try:
            # 验证完整Solution数据
            if hasattr(gas_out_file, 'soln_collect') and gas_out_file.soln_collect:
                total_cols = sum(len(df.columns) for df in gas_out_file.soln_collect.values())
                print(f"  ✅ 完整Solution数据: {len(gas_out_file.soln_collect)} 个工况, 总计 {total_cols} 列")

                # 检查第一个工况的数据类型
                first_condition = list(gas_out_file.soln_collect.values())[0]
                temp_cols = [col for col in first_condition.columns if 'Temperature' in col]
                pressure_cols = [col for col in first_condition.columns if 'Pressure' in col]
                mole_cols = [col for col in first_condition.columns if 'Mole_fraction' in col]
                net_cols = [col for col in first_condition.columns if 'Net' in col]

                print(f"    - 温度列: {len(temp_cols)}")
                print(f"    - 压力列: {len(pressure_cols)}")
                print(f"    - 摩尔分数列: {len(mole_cols)}")
                print(f"    - 净反应速率列: {len(net_cols)}")
            else:
                print("  ❌ 完整Solution数据缺失")

            # 验证净反应速率数据
            if hasattr(gas_out_file, 'net_rate_collect') and gas_out_file.net_rate_collect:
                net_total_cols = sum(len(df.columns) for df in gas_out_file.net_rate_collect.values())
                print(f"  ✅ 净反应速率数据: {len(gas_out_file.net_rate_collect)} 个工况, 总计 {net_total_cols} 列")
            else:
                print("  ❌ 净反应速率数据缺失")

            # 验证敏感性数据
            if hasattr(gas_out_file, 'sensitivity_collect') and gas_out_file.sensitivity_collect:
                sens_total_cols = sum(len(df.columns) for df in gas_out_file.sensitivity_collect.values())
                print(f"  ✅ 敏感性数据: {len(gas_out_file.sensitivity_collect)} 个工况, 总计 {sens_total_cols} 列")
            else:
                print("  ⚠️  敏感性数据未找到 (可能文件中不包含)")

            # 验证ROP计算结果
            if hasattr(gas_out_file, 'integral_ROP') and gas_out_file.integral_ROP:
                print(f"  ✅ ROP积分结果: {len(gas_out_file.integral_ROP)} 个工况")
            else:
                print("  ❌ ROP积分结果缺失")

            if hasattr(gas_out_file, 'end_ROP') and gas_out_file.end_ROP:
                print(f"  ✅ ROP终点结果: {len(gas_out_file.end_ROP)} 个工况")
            else:
                print("  ❌ ROP终点结果缺失")

        except Exception as e:
            print(f"  ❌ 数据验证过程中出现错误: {str(e)}")

    def get_available_gradient_variables(self):
        """获取可用的梯度变量列表（标准化变量名，去掉工况编号）"""
        print("=" * 60)
        print("检测可用的梯度变量...")
        print("=" * 60)

        # 从第一个solution sheet中获取完整的列信息
        if len(self.soln_sheets) > 0:
            first_sheet = self.xlsx.parse(self.soln_sheets[0], index_col=0)
            columns = first_sheet.columns.tolist()
            print(f"从 {self.soln_sheets[0]} 读取到 {len(columns)} 个列")
            print("前10个列名:")
            for i, col in enumerate(columns[:10]):
                print(f"  {i+1:2d}. {col}")

            # 筛选可用的梯度变量并标准化名称
            gradient_vars_raw = []
            for col in columns:
                if any(keyword in col for keyword in ['Temperature', 'Pressure', 'Mole_fraction']):
                    gradient_vars_raw.append(col)

            # 标准化变量名（去掉_Run#X_部分）
            gradient_vars_normalized = set()
            for var in gradient_vars_raw:
                normalized_var = self._normalize_variable_name(var)
                gradient_vars_normalized.add(normalized_var)

            gradient_vars = sorted(list(gradient_vars_normalized))

            print(f"\n原始梯度变量 ({len(gradient_vars_raw)} 个):")
            for var in gradient_vars_raw[:5]:  # 显示前5个原始变量
                print(f"  - {var}")
            if len(gradient_vars_raw) > 5:
                print(f"  ... 还有 {len(gradient_vars_raw) - 5} 个原始变量")

            print(f"\n标准化后的梯度变量 ({len(gradient_vars)} 个):")
            for var in gradient_vars[:20]:  # 显示前20个标准化变量
                print(f"  - {var}")

            if len(gradient_vars) > 20:
                print(f"  ... 还有 {len(gradient_vars) - 20} 个变量")

            return gradient_vars
        else:
            print("❌ 没有可用的solution sheets")
            return []

    def _normalize_variable_name(self, col_name):
        """
        标准化变量名，去掉Run#工况标识

        Args:
            col_name: 原始列名，格式如 "变量名_Run#工况序号_(变量单位)"

        Returns:
            标准化后的变量名
        """
        # 先去掉前后空格
        col_name = col_name.strip()

        # 如果包含_Run#，去掉Run#部分
        if '_Run#' in col_name:
            # 找到_Run#的位置
            run_index = col_name.find('_Run#')
            # 找到Run#后面的下一个_的位置
            next_underscore = col_name.find('_', run_index + 1)
            if next_underscore != -1:
                # 重组变量名：前缀 + 后缀（去掉Run#部分）
                prefix = col_name[:run_index]
                suffix = col_name[next_underscore:]
                return prefix + suffix
            else:
                # 如果没有找到下一个_，返回Run#前的部分
                return col_name[:run_index]
        return col_name